# Kubernetes ClusterRole Permissions Comparison Tool

This Python script compares permissions between two Kubernetes ClusterRole YAML manifest files and identifies differences.

## Features

- **Parse and flatten permissions**: Extracts all permissions from both manifest files and flattens them into individual permission entries (apiGroup + resource + verb combinations)
- **Handle special values**: 
  - Replaces empty string `""` in apiGroups with `<CORE>` to represent the core API group
  - Replaces wildcard `*` values with `<ALL>` for clear identification
- **Compare permissions**: Determines if each permission:
  - `EXISTS_IN_BOTH`: Permission exists in both manifest files
  - `EXISTS_IN_FIRST_ONLY`: Permission exists only in the first manifest file  
  - `EXISTS_IN_SECOND_ONLY`: Permission exists only in the second manifest file
- **Export to CSV**: Supports exporting results to CSV format with detailed columns
- **Command-line interface**: Easy to use with argparse support

## Prerequisites

Install the required dependency:
```bash
pip install PyYAML
```

## Usage

### Basic comparison with console output:
```bash
python3 compare_clusterrole_permissions.py file1.yaml file2.yaml
```

### Export results to CSV:
```bash
python3 compare_clusterrole_permissions.py file1.yaml file2.yaml -o results.csv
```

### Skip console summary:
```bash
python3 compare_clusterrole_permissions.py file1.yaml file2.yaml -o results.csv --no-summary
```

### Help:
```bash
python3 compare_clusterrole_permissions.py --help
```

## Example

Compare the complex ClusterRole with a simpler one:
```bash
python3 compare_clusterrole_permissions.py \
    charts/tm-installer/templates/vault-installer-cluster-role.dev.yaml \
    test_clusterrole_simple.yaml \
    -o comparison_results.csv
```

## Output Format

### Console Summary
```
Comparison Summary:
==================
Total unique permissions: 1250
Permissions in both files: 15
Permissions only in vault-installer-cluster-role.dev.yaml: 1200
Permissions only in test_clusterrole_simple.yaml: 35
```

### CSV Export
The CSV file contains the following columns:
- `API_Group`: The Kubernetes API group (with `<CORE>` for empty string, `<ALL>` for wildcard)
- `Resource`: The Kubernetes resource type
- `Verb`: The permission verb (get, list, create, etc.)
- `Status`: One of `EXISTS_IN_BOTH`, `EXISTS_IN_FIRST_ONLY`, `EXISTS_IN_SECOND_ONLY`
- `Source_File_1`: Boolean indicating if permission exists in first file
- `Source_File_2`: Boolean indicating if permission exists in second file

## Test Files

The repository includes:
- `test_clusterrole_simple.yaml`: A simple ClusterRole for testing
- `charts/tm-installer/templates/vault-installer-cluster-role.dev.yaml`: Complex real-world ClusterRole

## Script Structure

The `PermissionComparator` class handles:
1. Loading and parsing YAML files (supports multi-document YAML)
2. Extracting permissions from ClusterRole rules
3. Normalizing special values (`""` → `<CORE>`, `"*"` → `<ALL>`)
4. Flattening permissions into individual entries
5. Comparing permissions between files
6. Exporting results to CSV format
7. Generating summary statistics
