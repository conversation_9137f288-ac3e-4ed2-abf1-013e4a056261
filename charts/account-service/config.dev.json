{"name": "account-service Service", "serviceName": "account-service", "host": "0.0.0.0", "port": 8080, "env": "dev", "ownerInfo": {"name": "digibank", "email": "<EMAIL>", "url": "digibank.myteksi.com"}, "data": {"mysql": {"master": {"dsn": "{{as_username}}:{{as_password}}@tcp($MYSQL_HOST$:3306)/$DB_NAME$?parseTime=true&loc=UTC", "maxIdle": 25, "maxOpen": 50, "connMaxLifetime": "1800s"}, "slave": {"dsn": "{{as_username}}:{{as_password}}@tcp($MYSQL_HOST_REPLICA$:3306)/$DB_NAME$?parseTime=true&loc=UTC", "maxIdle": 25, "maxOpen": 50, "connMaxLifetime": "1800s"}, "masterCircuitBreaker": {"timeoutInMs": 1000}, "slaveCircuitBreaker": {"timeoutInMs": 1000}}}, "statsd": {"host": "$DOGSTATSD_HOST$", "port": 8125}, "trace": {"host": "$DOGSTATSD_HOST$", "port": 8126}, "logger": {"workerCount": 10, "bufferSize": 10000, "logLevel": 1, "stacktraceLevel": 4, "logFormat": "json", "development": true}, "accountServiceConfig": {"productVariantCodeConfig": {"depositAccount": "DEPOSITS_ACCOUNT", "operatingAccount": "BIZ_DEPOSIT_ACCOUNT", "savingsPocket": "SAVINGS_POCKET", "boostPocket": "BOOST_POCKET", "bizLocAccount": "DEFAULT_BIZ_FLEXI_CREDIT_LINE_OF_CREDIT", "bizLoanAccount": "DEFAULT_BIZ_FLEXI_CREDIT_TERM_LOAN"}, "clientConfig": {"maxIdleConnsPerHost": 64, "idleConnTimeoutInSMillis": 10000, "timeoutInMillis": 4000, "requestLogLockTimeoutInMillis": 5000}, "savingsPocketConfig": {"maxPocketLimit": 10}, "pendingActivationAutoCloseExcludedAccounts": ["*************", "*************"], "pendingActivationThresholdInMinutes": 480, "pendingActivationAutoCloseBatchCount": 2, "pendingFirstDepositThresholdInMinutes": 480, "bizOAPendingActivationThresholdInMinutes": 43200, "pendingActivationAutoCloseReminder": {"reminderText": ["1", "3", "5"], "reminderTimeInMinutes": [60, 180, 300], "bufferTimeInHours": 1, "_bufferTimeInHours": "A window starting after (threshold + reminder) time, account within (threshold + reminder) time and buffer time will received notifications"}, "bizOAPendingActivationAutoCloseReminder": {"reminderText": ["1", "3", "5"], "reminderTimeInMinutes": [60, 180, 300], "bufferTimeInHours": 1}, "pendingFirstDepositAutoCloseReminder": {"reminderText": ["1 Hour", "3 Hours", "5 Hours"], "reminderTimeInMinutes": [60, 180, 300], "bufferTimeInHours": 1, "_bufferTimeInHours": "A window starting after (threshold + reminder) time, account within (threshold + reminder) time and buffer time will received notifications"}, "accountStatus": {"disableCloseFromHour": "16:00", "disableCloseFromHour.description": "The hour format should be something like 23:00 and will be parsed as UTC time", "disableCloseDurationSecond": 10800, "asyncClosureProductVariants": ["DEPOSITS_ACCOUNT", "BIZ_DEPOSIT_ACCOUNT", "SAVINGS_POCKET", "BOOST_POCKET"], "casaSyncClosureProductVariants": ["DEPOSITS_ACCOUNT", "SAVINGS_POCKET", "BIZ_DEPOSIT_ACCOUNT"]}, "baseURL": "http://account-service.core-banking.svc.cluster.local", "workflowMonitors": [{"workflowID": "create_casa_account_v3", "targetSlackChannel": "C07S2SH5MBL", "statusFetchDurationInMinutes": 65, "batchSizeInMinutes": 60, "dataFetchCOPInMs": 500, "messagePerBatch": 50, "states": [200, 205, 210, 225, 230, 235, 240, 245, 250, 500, 505, 510], "spamCoolingOffInMinutes": 60}, {"workflowID": "dormant_casa_status", "targetSlackChannel": "C07S2SH5MBL", "statusFetchDurationInMinutes": 65, "batchSizeInMinutes": 60, "dataFetchCOPInMs": 500, "messagePerBatch": 50, "states": [200, 205, 210, 215, 220, 230, 235, 240, 245, 500, 505, 510, 515, 520, 525, 530, 535], "spamCoolingOffInMinutes": 60}]}, "accountNADWorkflowConfig": {"workflowID": "account-nad-workflow-id", "filePrefix": "DSA_BNM_NAD_HOLD_CODE_ACTION_TABLE/", "filePattern": "nad-verified-%s.csv", "localFolder": "/tmp", "dataS3Bucket": "dbmy-dev-mlops-scriptops-output-bnm-hold-code", "coreBankingS3Bucket": "dbmy-dev-backend-account-nad-verification", "pendingFolder": "pending", "completedFolder": "completed", "persistStateEvery": 50, "rsaPrivateKey": "{{NAD_VERIFICATION_PRIVATE_KEY}}"}, "locAccountCreationEventKafkaConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "account-account-creation-event-dev", "clusterType": "critical", "enableTLL": true, "stream": "dev-loc-account-creation-event", "packageName": "pb", "dtoName": "LOCAccountCreationEvent", "offsetType": "oldest", "maxRetryCount": 5, "delayInMilliSeconds": 100, "enabled": true}, "bankRestrictedEventKafkaConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "account-bank-restricted-event-dev", "clusterType": "critical", "enableTLL": true, "stream": "dev-account-bank-restricted-event", "packageName": "pb", "dtoName": "BankRestricted", "offsetType": "oldest", "maxRetryCount": 5, "delayInMilliSeconds": 100, "enabled": true}, "depositsAccountCreationEventKafkaConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "deposits-account-creation-event-dev", "clusterType": "critical", "enableTLL": true, "stream": "dev-deposits-account-creation-event", "packageName": "pb", "dtoName": "DepositsAccountCreateEvent", "offsetType": "oldest", "maxRetryCount": 5, "delayInMilliSeconds": 100, "enable": true}, "depositsAccountCreatedEventKafkaConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "deposits-account-created-event-dev", "clusterType": "critical", "enableTLL": true, "stream": "dev-deposits-account-created-event", "packageName": "pb", "dtoName": "DepositsAccountCreateEvent", "syncprod": true, "requiredAcks": -1}, "locAccountEventKafkaConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "stream": "dev-loan-core-loc-account", "clusterType": "critical", "enableTLL": true, "offsetType": "oldest", "clientID": "account-service-client-dev", "packageName": "pb", "dtoName": "LoanCoreLoc"}, "accountUpdateKafkaConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "account-update-event-dev", "clusterType": "critical", "enableTLL": true, "stream": "dev-account-update-event", "packageName": "pb", "dtoName": "AccountStatusUpdateEvent", "offsetType": "oldest"}, "dormantAccountEventKafkaConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "deposits-dormant-account-event-dev", "clusterType": "critical", "enableTLL": true, "stream": "dev-deposits-dormant-account-event", "packageName": "pb", "dtoName": "DepositsDormantAccount", "offsetType": "oldest"}, "depositsAccountDetailEventKafkaConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "deposits-account-detail-event-dev", "clusterType": "critical", "enableTLL": true, "stream": "dev-deposits-account-detail-event", "packageName": "pb", "dtoName": "AccountDetail", "offsetType": "oldest"}, "depositsPostingBatchEventKafkaConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "account-service-deposits-posting-event-dev", "clusterType": "critical", "enableTLL": true, "stream": "dev-deposits-core-tx", "packageName": "pb", "dtoName": "DepositsCoreTx", "offsetType": "oldest"}, "depositsNotificationEventKafkaConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "account-service-deposits-notification-event-dev", "clusterType": "critical", "enableTLL": true, "stream": "dev-deposits-notification-event", "packageName": "pb", "dtoName": "DepositsCustomerNotification", "syncprod": true, "requiredAcks": -1, "enable": true}, "pocketLifecycleEventKafkaConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "account-service-client-dev", "clusterType": "critical", "enableTLL": true, "stream": "dev-pocket-lifecycle-event", "packageName": "pb", "dtoName": "PocketLifecycle", "offsetType": "oldest"}, "customerJournalEventKafkaConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "account-service-client-dev", "clusterType": "critical", "enableTLL": true, "stream": "dev-audit-log", "packageName": "pb", "dtoName": "AuditLog", "offsetType": "oldest"}, "accountPendingActionKafkaConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "account-service-client-dev", "clusterType": "critical", "enableTLL": true, "stream": "dev-account-pending-action-event", "packageName": "pb", "dtoName": "AccountPendingAction", "offsetType": "oldest"}, "depositsAccountStatusUpdateEventKafkaConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "account-status-update-event-dev", "clusterType": "critical", "enableTLL": true, "stream": "dev-deposits-core-account-status-update-event", "packageName": "pb", "dtoName": "DepositsAccountStatusUpdateEvent", "offsetType": "oldest", "maxRetryCount": 5, "delayInMilliSeconds": 100, "enable": true}, "accountServiceDormantAccountEventKafkaConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "account-service-dormant-account-event-dev", "clusterType": "critical", "enableTLL": true, "stream": "dev-account-service-dormant-account-event", "packageName": "pb", "dtoName": "AccountServiceDormantAccountEvent", "offsetType": "oldest", "maxRetryCount": 5, "delayInMilliSeconds": 100, "enable": true}, "depositsCoreConfig": {"baseURL": "http://deposits-core.core-banking.svc.cluster.local"}, "depositsExpConfig": {"baseURL": "http://deposits-exp.core-banking.svc.cluster.local"}, "customerMasterConfig": {"baseURL": "http://customer-master.onboarding.svc.cluster.local"}, "goalCoreConfig": {"baseURL": "http://goal-core.core-banking.svc.cluster.local", "maxGoalAmountInCent": *********}, "productMasterConfig": {"baseURL": "http://product-master.core-banking.svc.cluster.local"}, "hermesConfig": {"baseURL": "http://hermes.hermes.svc.cluster.local"}, "pigeonConfig": {"baseURL": "http://pigeon.pigeon.svc.cluster.local"}, "loanCoreConfig": {"baseURL": "http://loan-core.lending-platform.svc.cluster.local"}, "customerExperienceConfig": {"baseURL": "http://customer-experience.onboarding.svc.cluster.local"}, "appian": {"hostAddress": "https://dbmydev.gforce.g-bank.app", "clientID": "account-service", "circuitConfig": {"appian": {"timeout": 15}}, "registeredClientID": "{{APPIAN_CLIENT_ID}}", "registeredClientSecret": "{{APPIAN_CLIENT_SECRET}}", "grantType": "client_credentials", "limitCreationCallbackEndpoint": "/suite/webapi/lending/event/limit-creation"}, "workflowRetryConfig": {"enabled": true, "createCASAAccountV3": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 10, "maxTimeWindowInSeconds": 86400}}, "createLOCAccount": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 10}, "auxiliary": {"intervalInSeconds": 30, "maxAttempt": 5}}, "deactivateLOCAccount": {"auxiliary": {"intervalInSeconds": 30, "maxAttempt": 10}}, "closeLOCAccount": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 10}}, "updateCASAAccountStatusV2": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 10, "maxTimeWindowFromCreatedAt": 30}}}, "featureFlags": {"enableR2": true, "enableNewFVInGoalEstimation": false, "enableClientCloseConnHandling": true, "_enableClientCloseConnHandling": "this is a release feature flag, it should be clean up after release", "enableAccountPendingActionPublisher": true, "_enableAccountPendingActionPublisher": "this is a release feature flag, it should be clean up after release", "enableRemoveSchedulerInAsync": false, "_enableRemoveSchedulerInAsync": "this is a release feature flag to enable the remove scheduler flow in async way, it should be clean up after release", "enableReplicaRead": false, "enableLending": true, "enableAsyncAccountCreation": true, "enableBiz": false, "enableBizFlexiCredit": true, "enableBIZAuthorisation": true, "enableNADDecryption": true, "enableAccountDormant": true, "enableLOCAccountCreationEventPublish": true, "enableRetryableStream": true, "enableDepositsCreationRetryableStream": true, "enableAuthzWithProfileID": true, "enableBoostPocket": true, "enableAccountCreationConcurrencyControl": false}, "tenant": "MY", "locale": {"currency": "MYR", "countryCode": "MY"}, "workerConfig": {"closePendingActivationAccountConf": {"lockDurationInMinutes": 10, "cronExpression": "*/15 * * * *", "enabled": false, "queryBatchSizeInDays": 5, "globalBatchStartTime": "2023-02-01T00:00:00Z"}, "autoCloseBizOAPendingActivationAccount": {"globalBatchStartTime": "2024-08-01T00:00:00Z", "queryBatchSizeInDays": 10}, "closePendingFirstDepositAccountConf": {"lockDurationInMinutes": 10, "cronExpression": "0 17 * * *", "enabled": false}, "pendingActivationAutoCloseReminderNotificationConf": {"lockDurationInMinutes": 200, "cronExpression": "0 */1 * * *", "enabled": true, "rateLimitMilliSecond": 150}, "bizOAPendingActivationAutoCloseReminderNotificationConf": {"rateLimitMilliSecond": 150}, "pendingFirstDepositAutoCloseReminderNotificationConf": {"lockDurationInMinutes": 200, "cronExpression": "0 */1 * * *", "enabled": false, "rateLimitMilliSecond": 150}}, "redisConfig": {"addr": "clustercfg.dbmy-dev-backend-ec-account-service.o9f56r.apse1.cache.amazonaws.com:6379", "idleTimeoutInSec": 300, "poolSize": 300, "readOnlyFromSlaves": false, "readTimeoutInSec": 1, "writeTimeoutInSec": 1, "password": "{{ REDIS_PASSWORD }}", "tlsEnabled": true, "timeout": 500, "command_name": "redis_AcntServ", "command_group": "AccountService", "max_concurrent_request": 1000, "error_percent_threshold": 20, "sleep_window": 5000, "max_concurrent_requests": 1000}, "notificationConfig": {"holdcodeAccountBlockNotification": {"PushTemplate": "", "EmailTemplate": "holdcode_email_block_account"}, "holdcodeAccountLockNotification": {"PushTemplate": "", "EmailTemplate": "holdcode_email_lock_account"}, "holdcodeAccountUnblockNotification": {"PushTemplate": "holdcode_push_unblock_account", "EmailTemplate": "holdcode_email_unblock_account"}, "holdcodeAccountUnlockNotification": {"PushTemplate": "holdcode_push_unlock_account", "EmailTemplate": "holdcode_email_unlock_account"}, "savingsPocketClosureNotification": {"PushTemplate": "", "EmailTemplate": "", "_note": "Notification for pocket open and closed is currently disabled hence empty template value"}, "savingsPocketCreationNotification": {"PushTemplate": "", "EmailTemplate": "", "_note": "Notification for pocket open and closed is currently disabled hence empty template value"}, "boostPocketCreationNotification": {"PushTemplate": "boost_pocket_creation_push", "EmailTemplate": "boost_pocket_creation_email"}, "boostPocketCreationFailedGenericNotification": {"PushTemplate": "boost_pocket_creation_failed_generic_push", "EmailTemplate": ""}, "boostPocketCreationFailedAvailabilityNotification": {"PushTemplate": "boost_pocket_creation_failed_high_demand_push", "EmailTemplate": ""}, "boostPocketCreationFailedBalanceNotification": {"PushTemplate": "boost_pocket_creation_failed_balance_push", "EmailTemplate": ""}, "pendingActivationAutoCloseReminderNotification": {"PushTemplate": "pending_activation_auto_close_reminder_push", "EmailTemplate": "pending_first_fund_auto_close_reminder_email"}, "bizOAPendingActivationAutoCloseReminderNotification": {"PushTemplate": "biz_oa_pending_activation_auto_close_reminder_push", "EmailTemplate": "biz_oa_pending_activation_auto_close_reminder_email"}, "pendingFirstFundAutoCloseReminderNotification": {"PushTemplate": "pending_action_auto_close_reminder_push", "EmailTemplate": "pending_first_fund_auto_close_reminder_email"}, "holdCodeBankRestrictedBlockAccountNotification": {"PushTemplate": "holdcode_push_bank_restricted_block_account", "EmailTemplate": "holdcode_email_bank_restricted_block_account"}, "holdCodeBankRestrictedUnblockAccountNotification": {"PushTemplate": "holdcode_push_bank_restricted_unblock_account", "EmailTemplate": "holdcode_email_bank_restricted_unblock_account"}, "locAccountPendingClosureNotification": {"PushTemplate": "loc_closure_processing_push", "EmailTemplate": "loc_closure_processing_email"}, "locAccountFailedClosureNotification": {"PushTemplate": "loc_closure_failed_push", "EmailTemplate": "loc_closure_failed_email"}, "locAccountClosedNotification": {"PushTemplate": "loc_closure_completed_push", "EmailTemplate": "loc_closure_completed_email"}, "dormantAccountNotification": {"PushTemplate": "", "EmailTemplate": "dormancy_email"}, "closedAccountPendingActivationNotification": {"PushTemplate": "", "EmailTemplate": "closed_account_pending_activation_email"}, "autoClosedBizOAPendingActivationNotification": {"PushTemplate": "", "EmailTemplate": "auto_closed_biz_oa_account_pending_activation_email"}, "reactivatedDormantAccountNotification": {"PushTemplate": "reactivated_dormancy_push", "EmailTemplate": "reactivated_dormancy_email"}, "holdCodeMissingDeviceBlockAccountNotification": {"PushTemplate": "", "EmailTemplate": "holdcode_email_missing_device_block_account"}, "holdCodeMissingDeviceUnblockAccountNotification": {"PushTemplate": "holdcode_push_missing_device_unblock_account", "EmailTemplate": "holdcode_email_missing_device_unblock_account"}, "bizOAHoldcodeAccountBlockNotification": {"PushTemplate": "", "EmailTemplate": "biz_oa_holdcode_email_block_account"}, "bizOAHoldcodeAccountUnblockNotification": {"PushTemplate": "biz_oa_holdcode_push_unblock_account", "EmailTemplate": "biz_oa_holdcode_email_unblock_account"}, "bizHoldCodeBankRestrictedBlockAccountNotification": {"PushTemplate": "", "EmailTemplate": "biz_holdcode_email_bank_restricted_block_account"}, "bizHoldCodeBankRestrictedUnblockAccountNotification": {"PushTemplate": "biz_holdcode_push_bank_restricted_unblock_account", "EmailTemplate": "biz_holdcode_email_bank_restricted_unblock_account"}}, "slackConfig": {"token": "{{SLACK_TOKEN}}", "defaultChannelID": "C07S2SH5MBL", "enabled": true}, "retryStreamSQSConfig": {"queueURL": "https://sqs.ap-southeast-1.amazonaws.com/************/dev-backend-account-service-sqs20250123155343781700000002", "awsRegion": "ap-southeast-1", "exponentialBackoffBaseIntervalSeconds": 1, "waitTimeSeconds": 10, "maxNumberOfMessages": 1, "maxConcurrency": 1}, "queryCacherConfig": {"redisKeyPrefix": "qcAccountService", "statsdContextTag": "qcAccountService", "queryConfigs": {"listCASAAccountsForCustomer": {"enable": true, "enableReadCache": true, "enableCacheStats": true, "cacheEntryVersion": 1, "cachedResultTtlMs": 2000, "readCacheTimeoutMs": 60, "updateCacheTimeoutMs": 2000}, "getCASAAccount": {"enable": true, "enableReadCache": true, "enableCacheStats": true, "cacheEntryVersion": 1, "cachedResultTtlMs": 2000, "readCacheTimeoutMs": 60, "updateCacheTimeoutMs": 2000}}}, "boostPocketConfig": {"whitelistAllUsers": true, "isBetaEnabled": false, "isLimitControlEnabled": false, "betaUserSafeIDList": [], "enableBoostPocketWhitelistCaching": false, "enableScheduledMaintenanceWindow": false}, "messagesToUser": {"boostPocketClosureFreezeMessage": {"code": "BP_CLOSURE_BLOCKED", "message": "We are unable to close pocket between 11:55 PM and 2:00 AM. Please try outside the aforementioned time window"}, "boostPocketCreationFreezeMessage": {"code": "BP_CREATION_BLOCKED", "message": "We're unable to create your Bonus Pocket due to high demand. Please check back again soon!"}, "boostPocketInsufficientBalanceErrorMessage": {"code": "BALANCE_INSUFFICIENT", "message": "We couldn't create your Bonus Pocket due to insufficient balance. Please add money and try again."}, "boostPocketCreationNumberLimitReachedMessage": {"code": "BP_CREATION_BLOCKED", "message": "We are unable to create your Bonus Pocket due to maximum limit of #maxLimit Bonus Pocket(s) is reached"}}}