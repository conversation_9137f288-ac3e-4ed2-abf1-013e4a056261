{"name": "whitelist-service", "serviceName": "whitelist-service", "mode": "production", "host": "0.0.0.0", "env": "prd", "port": 8080, "ownerInfo": {"name": "digibank", "email": "<EMAIL>", "url": "digibank.myteksi.com"}, "data": {"mysql": {"master": {"dsn": "{{ MYSQL_USERNAME }}:{{ MYSQL_PASSWORD }}@tcp($MYSQL_HOST$:3306)/whitelist_service?parseTime=true&loc=UTC", "maxIdle": 25, "maxOpen": 50, "connMaxLifetime": "1800s"}, "slave": {"dsn": "{{ MYSQL_USERNAME }}:{{ MYSQL_PASSWORD }}@tcp($MYSQL_HOST$:3306)/whitelist_service?parseTime=true&loc=UTC", "maxIdle": 25, "maxOpen": 50, "connMaxLifetime": "1800s"}, "masterCircuitBreaker": {"timeoutInMs": 1000}, "slaveCircuitBreaker": {"timeoutInMs": 1000}}}, "statsd": {"host": "$DOGSTATSD_HOST$", "port": 8125}, "trace": {"host": "$DOGSTATSD_HOST$", "port": 8126}, "logger": {"workerCount": 10, "bufferSize": 10000, "logLevel": 1, "stacktraceLevel": 4, "logFormat": "json"}, "redis": {"addr": "clustercfg.dbmy-prd-onboarding-ec-whitelist-service.uotimr.apse1.cache.amazonaws.com:6379", "idleTimeoutInSec": 1, "poolSize": 300, "readOnlyFromSlaves": false, "readTimeoutInSec": 1, "writeTimeoutInSec": 1, "password": "{{ REDIS_PASSWORD }}", "tlsEnabled": true}, "featureFlags": {"ratelimiting": true, "enableBulkProcessor": false, "enableWorkers": false, "enableConsumer": false, "enableFlexiCreditAppVersionChecking": true}, "rateLimit": {"customerWhitelist": {"limit": 500, "window": "1m", "type": "FIXED_WINDOW"}, "customerWaitlist": {"limit": 500, "window": "1m", "type": "FIXED_WINDOW"}}, "grabHashConfig": {"salt": "{{ GRAB_HASH_CONFIG_SALT }}", "iterations": 200000, "keyLen": 32, "prefix": "+"}, "dbmyHashConfig": {"salt": "{{ DBMY_HASH_CONFIG_SALT }}", "iterations": 200000, "keyLen": 32, "prefix": "+"}, "bulkprocessor": {"queueURL": "", "waitTimeInSec": 10}, "whitelistWorkerConfig": {"workerConfig": {"delayInSecs": 61, "startImmediately": false, "logTag": "grabWhitelistWorker", "name": "grabWhitelistWorker", "lockKey": "redis_lock_key", "lockDurationInSecs": 120}, "dataTeamBucket": "", "dataTeamGrabPrefix": "", "dataTeamRoleARN": ""}, "applicationCountConfig": {"keyConfig": {"key": "APP_COUNT_KEY", "lock": "APP_COUNT_LOCK"}, "ttlInSecs": 3600, "productCap": {"CASA_ACCOUNT": 20000, "AT_LENDING": 0}}, "kafkaConfig": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "whitelist-service-prd", "clusterType": "critical", "enableTLS": true, "initOffset": "oldest"}, "craStreamConfig": {"streamID": "prd_customer_risk_assessment", "topic": "prd-customer-risk-assessment", "dtoName": "CustomerRiskAssessment"}, "applicationStatusTransitionStreamConfig": {"streamID": "prd_application_status_update_transition", "topic": "prd-application-status-update-transition", "dtoName": "ApplicationStatusTransition"}, "customerMaster": {"serviceName": "customer-master", "baseURL": "http://customer-master.onboarding.svc.cluster.local", "circuitBreaker": {"timeout": 1000}}, "applicationService": {"serviceName": "application-service", "baseURL": "http://application-service.onboarding.svc.cluster.local", "circuitBreaker": {"timeout": 2000}}, "grabCredentials": {"x-client-id": "", "x-api-key": ""}, "grabClient": {"serviceName": "grab", "baseURL": "https://stg-netgw-ep01.stg-grabpay.com/mb-agent", "circuitBreaker": {"timeout": 1000, "max_concurrent_requests": 50}}, "whitelistSource": {"CASA_ACCOUNT": ["GRAB_DB", "DBMY_DB"], "BIZ_ACCOUNT": ["GRAB_DB"], "OPEN_BIZ": ["DBMY_DB", "GRAB_DB"], "NTB_LENDING": ["DBMY_DB"]}, "allowWhitelistReuse": {"CASA_ACCOUNT": false, "AT_LENDING": true, "CARDS": true}, "APIRetryConfig": {"maxRetry": 3, "interval": 1, "initialInterval": 0.5}, "whitelistDisabledConfig": {"disableOpenBizWhitelist": false, "disableBizWhitelist": true, "disableCasaWhitelist": true, "disableCardWhitelist": true, "disableFlexiCreditWhitelist": true, "disableNtbLendingWhitelist": false}, "productWhitelists": [{"name": "AT_LENDING", "rollOutConfig": {"supportedAppVersion": [{"appOperationSystem": "iOS", "enableAppVersionFilter": true, "supportedVersion": "1.15.0"}, {"appOperationSystem": "adr", "enableAppVersionFilter": true, "supportedVersion": "1.15.0"}]}}]}