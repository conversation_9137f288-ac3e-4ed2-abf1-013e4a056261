env: dev
configName: config.dev.json

gateway:
  enabled: false
  annotations: {}
  hosts:
    - "backend.dev.g-bank.app"
  tls: {}

serviceAccount:
  create: true
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks/cluster/dev-backend-01/transaction-statements
  name: statements-gen-worker

autoscaling:
  enabled: true

envVars:
  ENV: dev
  SECRET_CONF: /vault/secrets/
  SERVICE_CONF: /config_files/config.json
  AWS_ROLE_ARN: arn:aws:iam::************:role/eks/cluster/dev-backend-01/transaction-statements
  AWS_WEB_IDENTITY_TOKEN_FILE: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
  AWS_REGION: ap-southeast-1
  SNOWFLAKE_PATH: hu79740.ap-southeast-1.snowflakecomputing.com/GOLD/STATEMENTS?role=TXN_STATEMENTS_SERVICE_ROLE&client_session_keep_alive=true
  QUEUE_URL: https://sqs.ap-southeast-1.amazonaws.com/************/dev-backend-transaction-statements-sqs-statement20250820072720527400000003
  RUN_CASA: "\"true\""
  RUN_BIZ: "\"true\""
  RUN_LENDING: "\"true\""
  RUN_BIZ_FLEXI_CREDIT: "\"true\""
  OVERWRITE: "\"false\""

podAnnotations:
  vault.hashicorp.com/role: "transaction-statements"
  vault.hashicorp.com/agent-pre-populate-only: "true"
  vault.hashicorp.com/agent-inject-secret-snowflake_creds.env: database/static-creds/dev-data-backend-txn-statement-ro
  vault.hashicorp.com/agent-inject-template-snowflake_creds.env: |
    {{ with secret "database/static-creds/dev-data-backend-txn-statement-ro" -}}
      SNOWFLAKE_USER={{ .Data.username }}
      SNOWFLAKE_PASSWORD={{ .Data.password }}
    {{- end }}


volumeMountPath: /config_files

scheduleTime: "30 * * * *"

ttlSecondsAfterFinished: 1800

restartPolicy: OnFailure

concurrencyPolicy: Replace

vaultAnnotation:
  disabled: false

# it's mandantory, don't comment out
suspend: true

workerConfig:
  consumer:
    resources:
      limits:
        cpu: 2000m
        memory: 1Gi
      requests:
        cpu: 50m
        memory: 64Mi
    suspend: true
  largeConsumer:
    resources:
      limits:
        cpu: 2000m
        memory: 1Gi
      requests:
        cpu: 50m
        memory: 64Mi
    suspend: true
  publisher:
    resources:
      limits:
        cpu: 2000m
        memory: 1Gi
      requests:
        cpu: 50m
        memory: 64Mi
    suspend: false
  opsS3Publisher:
    scheduleTime: "0 0 3 * *"
    resources:
      limits:
        cpu: 2000m
        memory: 1Gi
      requests:
        cpu: 50m
        memory: 64Mi
    suspend: true

bizWorkerConfig:
  scheduleTime: "0 0 3 * *"
  consumer:
    resources:
      limits:
        cpu: 2000m
        memory: 1Gi
      requests:
        cpu: 50m
        memory: 64Mi
    suspend: true
  largeConsumer:
    scheduleTime: "15 0 3 * *"
    resources:
      limits:
        cpu: 2000m
        memory: 1Gi
      requests:
        cpu: 50m
        memory: 64Mi
    suspend: true
  publisher:
    resources:
      limits:
        cpu: 2000m
        memory: 1Gi
      requests:
        cpu: 50m
        memory: 64Mi
    suspend: true

lendingWorkerConfig:
  scheduleTime: "40 * * * *"
  consumer:
    resources:
      limits:
        cpu: 2000m
        memory: 1Gi
      requests:
        cpu: 50m
        memory: 64Mi
    suspend: true
    parallelism: 3
  publisher:
    resources:
      limits:
        cpu: 2000m
        memory: 1Gi
      requests:
        cpu: 50m
        memory: 64Mi
    suspend: true

bizFlexiCreditWorkerConfig:
  scheduleTime: "40 * * * *"
  consumer:
    resources:
      limits:
        cpu: 2000m
        memory: 1Gi
      requests:
        cpu: 50m
        memory: 64Mi
    suspend: true
    parallelism: 3
  publisher:
    resources:
      limits:
        cpu: 2000m
        memory: 1Gi
      requests:
        cpu: 50m
        memory: 64Mi
    suspend: true
