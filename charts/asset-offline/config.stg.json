{"name": "asset-offline Service", "serviceName": "asset-offline", "host": "0.0.0.0", "port": 8080, "env": "stg", "ownerInfo": {"name": "digibank", "email": "<EMAIL>", "url": "digibank.myteksi.com"}, "data": {"mysql": {"master": {"dsn": "{{mysql_username}}:{{mysql_password}}@tcp($MYSQL_HOST$:3306)/$DB_NAME$?parseTime=true&loc=UTC", "maxIdle": 10, "maxOpen": 20, "connMaxLifetime": "1800s"}, "slave": {"dsn": "{{mysql_username}}:{{mysql_password}}@tcp($MYSQL_HOST_REPLICA$:3306)/$DB_NAME$?parseTime=true&loc=UTC", "maxIdle": 10, "maxOpen": 20, "connMaxLifetime": "1800s"}, "masterCircuitBreaker": {"timeoutInMs": 1000}, "slaveCircuitBreaker": {"timeoutInMs": 1000}}}, "statsd": {"host": "$DOGSTATSD_HOST$", "port": 8125}, "trace": {"host": "$DOGSTATSD_HOST$", "port": 8126}, "logger": {"workerCount": 10, "bufferSize": 10000, "logLevel": 1, "stacktraceLevel": 4, "logFormat": "json", "development": false}, "redisConfig": {"addr": "clustercfg.dbmy-stg-lending-ec-asset-offline.nofyfm.apse1.cache.amazonaws.com:6379", "idleTimeoutInSec": 1, "poolSize": 300, "readOnlyFromSlaves": false, "readTimeoutInSec": 1, "writeTimeoutInSec": 1, "password": "{{REDIS_PASSWORD}}", "tlsEnabled": true}, "rateLimiters": [{"key": "per_select_consumer", "maxReq": 2, "windowSizeInSec": 1}], "assetOfflinePreSelectUserOfferKafkaConfig": {"brokers": ["b-1.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "asset-offline-stg", "clusterType": "critical", "enableTLL": true, "stream": "stg-pre-select-user-offer-event", "offsetType": "oldest", "packageName": "pb", "dtoName": "BatchifyPreSelectUserOffer", "syncprod": true, "requiredAcks": -1, "maxRetryCount": 5, "delayInMilliSeconds": 200}, "creditBureauEnquiryKafkaConfig": {"brokers": ["b-1.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "asset-offline-stg", "clusterType": "critical", "enableTLL": true, "stream": "stg-credit-bureau-event", "offsetType": "oldest", "packageName": "pb", "dtoName": "CreditBureauEvent", "syncprod": true, "requiredAcks": -1, "maxRetryCount": 5, "delayInMilliSeconds": 200}, "bilrLmsActionKafkaConfig": {"brokers": ["b-1.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "asset-offline-client-stg", "clusterType": "critical", "enableTLL": true, "stream": "stg-bilr-lms-action-tx", "dtoName": "AssetOfflineLimitReview", "enable": true, "maxRetryCount": 5, "offsetType": "oldest", "packageName": "pb", "delayInMilliSeconds": 200}, "bilrCbsUpdateReportKafkaConfig": {"brokers": ["b-1.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "asset-offline-client-stg", "clusterType": "critical", "enableTLL": true, "stream": "stg-bilr-cbs-update-tx", "dtoName": "AssetOfflineUpdateReport", "enable": true, "maxRetryCount": 5, "offsetType": "oldest", "packageName": "pb", "delayInMilliSeconds": 200, "syncprod": true, "requiredAcks": -1}, "enableBILRConsumerStreams": false, "featureFlags": {"preSelectUserOfferFlag": true, "enableExecutionPeriodFlag": false, "enableRecomputingCdeFlag": true, "enableCreditProductConsentCheck": false}, "executionPeriodConfig": {"executionPeriods": [{"startTime": "08:00", "endTime": "17:59"}, {"startTime": "18:00", "endTime": "23:59"}]}, "dynamicConstants": {"preSelectOfferExpiryDays": 30, "defaultCountryCode": "MY", "defaultChannelCode": "GXBANK", "defaultCurrencyCode": "MYR", "defaultRequestedLoanAmount": 10000, "preSelectSleepTimeInMilliSecs": 500, "inactivePeriodSleepTimeInMilliSecs": 1000}, "customerMasterConfig": {"serviceName": "customer-master", "baseURL": "http://customer-master.onboarding.svc.cluster.local", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": true}, "customerExperienceConfig": {"serviceName": "customer-experience", "baseURL": "http://customer-experience.onboarding.svc.cluster.local", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": true}, "creditBureauServiceConfig": {"baseURL": "http://credit-bureau.lending-platform.svc.cluster.local"}, "creditDecisionEngineConfig": {"baseURL": "http://cr-decision-eng.lending-platform.svc.cluster.local"}, "loanAppConfig": {"baseURL": "http://loan-app.lending-platform.svc.cluster.local", "circuitConfig": {"customer-master": {"timeout": 1500}}}, "workflowRetry": {"default": {"interval": 10, "maxAttempts": 5}}, "cdeEmploymentMapping": {"B52": {"employmentType": "SA", "occupationCode": "4"}, "B43": {"employmentType": "SA", "occupationCode": "4"}, "B54": {"employmentType": "SA", "occupationCode": "4"}, "B60": {"employmentType": "SA", "occupationCode": "4"}, "B42": {"employmentType": "SA", "occupationCode": "4"}, "B62": {"employmentType": "SA", "occupationCode": "4"}, "B04": {"employmentType": "SA", "occupationCode": "4"}, "B73": {"employmentType": "SE", "occupationCode": "1"}, "B53": {"employmentType": "SA", "occupationCode": "4"}, "B63": {"employmentType": "SA", "occupationCode": "4"}, "B16": {"employmentType": "SA", "occupationCode": "4"}, "B55": {"employmentType": "SA", "occupationCode": "4"}, "B03": {"employmentType": "SA", "occupationCode": "4"}, "B07": {"employmentType": "SA", "occupationCode": "4"}, "B34": {"employmentType": "SA", "occupationCode": "4"}, "B51": {"employmentType": "SA", "occupationCode": "4"}, "B27": {"employmentType": "SA", "occupationCode": "4"}, "B25": {"employmentType": "SA", "occupationCode": "4"}, "B24": {"employmentType": "SA", "occupationCode": "4"}, "B36": {"employmentType": "SA", "occupationCode": "4"}, "B20": {"employmentType": "SA", "occupationCode": "4"}, "B19": {"employmentType": "SA", "occupationCode": "4"}, "B09": {"employmentType": "SA", "occupationCode": "4"}, "B70": {"employmentType": "GIG", "occupationCode": "1"}, "B08": {"employmentType": "SA", "occupationCode": "4"}, "B10": {"employmentType": "SA", "occupationCode": "4"}, "B71": {"employmentType": "SE", "occupationCode": "3"}, "B35": {"employmentType": "SA", "occupationCode": "4"}, "B12": {"employmentType": "SA", "occupationCode": "4"}, "B01": {"employmentType": "SA", "occupationCode": "4"}, "B02": {"employmentType": "SA", "occupationCode": "4"}, "B18": {"employmentType": "SA", "occupationCode": "4"}, "B45": {"employmentType": "SA", "occupationCode": "4"}, "B26": {"employmentType": "SA", "occupationCode": "4"}, "B28": {"employmentType": "SA", "occupationCode": "4"}, "B23": {"employmentType": "SA", "occupationCode": "4"}, "B14": {"employmentType": "SA", "occupationCode": "4"}, "B06": {"employmentType": "SA", "occupationCode": "4"}, "B05": {"employmentType": "SA", "occupationCode": "4"}, "B39": {"employmentType": "SA", "occupationCode": "4"}, "B11": {"employmentType": "SA", "occupationCode": "4"}, "B31": {"employmentType": "SA", "occupationCode": "4"}, "B13": {"employmentType": "SA", "occupationCode": "4"}, "B57": {"employmentType": "SA", "occupationCode": "4"}, "B49": {"employmentType": "SA", "occupationCode": "4"}, "B40": {"employmentType": "SA", "occupationCode": "4"}, "B37": {"employmentType": "SA", "occupationCode": "4"}, "B30": {"employmentType": "SA", "occupationCode": "4"}, "B47": {"employmentType": "SA", "occupationCode": "4"}, "B50": {"employmentType": "SA", "occupationCode": "4"}, "B61": {"employmentType": "SA", "occupationCode": "4"}, "B74": {"employmentType": "SA", "occupationCode": "4"}, "A01": {"employmentType": "SA", "occupationCode": "4"}, "A02": {"employmentType": "SA", "occupationCode": "4"}, "A03": {"employmentType": "SA", "occupationCode": "4"}, "A04": {"employmentType": "SA", "occupationCode": "4"}, "A05": {"employmentType": "SA", "occupationCode": "4"}, "A08": {"employmentType": "SA", "occupationCode": "4"}, "A07": {"employmentType": "SA", "occupationCode": "4"}, "A06": {"employmentType": "SA", "occupationCode": "4"}, "A09": {"employmentType": "SA", "occupationCode": "4"}, "A10": {"employmentType": "SA", "occupationCode": "4"}, "A11": {"employmentType": "SA", "occupationCode": "4"}, "B15": {"employmentType": "SA", "occupationCode": "4"}, "B32": {"employmentType": "SA", "occupationCode": "4"}, "B64": {"employmentType": "SA", "occupationCode": "4"}, "B65": {"employmentType": "SA", "occupationCode": "4"}, "B66": {"employmentType": "SA", "occupationCode": "4"}, "B67": {"employmentType": "SA", "occupationCode": "4"}, "B68": {"employmentType": "SA", "occupationCode": "4"}, "B69": {"employmentType": "SA", "occupationCode": "4"}, "B72": {"employmentType": "GIG", "occupationCode": "2"}}, "accountServiceConfig": {"baseURL": "http://account-service.core-banking.svc.cluster.local", "serviceName": "account-service", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": true, "commandGroup": "AssetOffline"}, "loanCoreConfig": {"baseURL": "http://loan-core.lending-platform.svc.cluster.local", "serviceName": "loan-core", "circuitBreaker": {"timeout": 10000, "max_concurrent_requests": 100, "request_volume_threshold": 100, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": true, "commandGroup": "AssetOffline"}, "hedwig": {"clientConfig": {"hostAddress": "https://debug.sgbank.dev", "pushServerPath": "/hedwig/hedwig/v1/push", "pushInboxServerPath": "/hedwig/hedwig/v1/pushInbox", "emailServerPath": "/hedwig/hedwig/v1/email", "circuitConfig": {"hedwig": {"timeout": 15}}, "serviceName": "", "serviceKey": ""}}, "pigeonConfig": {"baseURL": "http://pigeon.pigeon.svc.cluster.local", "circuitConfig": {"pigeon": {"timeout": 1500}}}}