{"name": "deposits-core Service", "serviceName": "deposits-core", "host": "0.0.0.0", "port": 8080, "env": "dev", "ownerInfo": {"name": "digibank", "email": "<EMAIL>", "url": "digibank.myteksi.com"}, "data": {"mysql": {"master": {"dsn": "{{dc_username}}:{{dc_password}}@tcp($MYSQL_HOST$:3306)/$DB_NAME$?parseTime=true&loc=UTC", "maxIdle": 25, "maxOpen": 50, "connMaxLifetime": "1800s"}, "slave": {"dsn": "{{dc_username}}:{{dc_password}}@tcp($MYSQL_HOST_REPLICA$:3306)/$DB_NAME$?parseTime=true&loc=UTC", "maxIdle": 25, "maxOpen": 50, "connMaxLifetime": "1800s"}, "masterCircuitBreaker": {"timeoutInMs": 1000}, "slaveCircuitBreaker": {"timeoutInMs": 1000}}}, "statsd": {"host": "$DOGSTATSD_HOST$", "port": 8125}, "trace": {"host": "$DOGSTATSD_HOST$", "port": 8126}, "logger": {"workerCount": 10, "bufferSize": 10000, "logLevel": 1, "stacktraceLevel": 4, "logFormat": "json", "development": true}, "depositsCoreConfig": {"clientConfig": {"maxIdleConnsPerHost": 64, "idleConnTimeoutInSMillis": 10000, "timeoutInMillis": 4000, "requestLogLockTimeoutInMillis": 5000}, "dormancySchedulerLockDurationInHours": 12, "dormancySchedulerCronExpression": "0 0 * * *", "dormancyThresholdInDays": 365, "interestUpdateScheduleCronExpression": "*/15 * * * *", "interestUpdateSchedulerLockDurationInMinutes": 720, "updateProductAvailabilitySchedulerLockDurationInMinutes": 5, "productVariantCodeConfig": {"depositAccount": "DEPOSITS_ACCOUNT", "operatingAccount": "BIZ_DEPOSIT_ACCOUNT", "savingsPocket": "SAVINGS_POCKET", "boostPocket": "BOOST_POCKET"}, "dynamicConstants": {"defaultCurrencyCode": "MYR", "defaultTimeZoneOffset": 28800}, "boostPocketConfig": {"messagesToUser": {"boostPocketGlobalLimitBreachedMessage": {"code": "ACCOUNT_CREATION_FAILED_PRODUCT_UNAVAILABLE", "message": "Boost Pocket slots have been oversubscribed."}}, "fetchBalanceDelayWindowInSeconds": 14400}, "savingsPocketConfig": {"maxAllowedLength": 4, "productVariant": "SAVINGS_POCKET"}, "childAccountConfig": {"maxAllowedLength": 3}, "tmAccountListMaxPage": 10, "schedulerConfig": {"thoughtMachineFailedJob": {"lockDurationInMinutes": 5}, "productAvailabilityPositionJob": {"BOOST_POCKET": {"enable": true}}}, "thoughtMachineFailedJobConfig": {"maxRepublishCount": 5, "maxRepublishFailureCount": 5, "ignoreJobIDs": []}, "allowedDepositsTMSchedulers": {"LABYRINTH_SUPERVISOR_DEPOSIT_DAILY_SCHEDULE_AST": true, "LABYRINTH_SUPERVISOR_BIZ_DEPOSIT_DAILY_SCHEDULE_AST": true}, "kafkaPostingPhaseRolloutPercentage": 80, "allowedTxnCodeDomain": {"DEPOSITS": true, "FINANCE": true, "DEBIT_CARD": true, "TREASURY": true, "INSURANCE": true, "BIZ_DEPOSITS": true, "INVEST_P2P": true}, "workflowMonitors": [{"workflowID": "create_boost_pocket_account", "targetSlackChannel": "C07S2SH5MBL", "statusFetchDurationInMinutes": 1440, "batchSizeInMinutes": 60, "dataFetchCOPInMs": 500, "messagePerBatch": 50, "states": [201, 205, 210, 215, 220, 225, 230, 235, 240, 245, 250, 255, 260, 265, 275, 280, 285, 290, 295, 500, 501, 503, 505], "enableAntiSpam": false, "spamCoolingOffInMinutes": 60}, {"workflowID": "auto_renew_boost_pocket_account", "targetSlackChannel": "C07S2SH5MBL", "statusFetchDurationInMinutes": 1440, "batchSizeInMinutes": 60, "dataFetchCOPInMs": 500, "messagePerBatch": 50, "states": [200, 205], "enableAntiSpam": false, "spamCoolingOffInMinutes": 60}]}, "thoughtMachineConfig": {"thoughtMachineURL": "https://core-api.tm.dev.g-bank.app", "thoughtMachineToken": "{{tm_token}}", "postingClientResponseTopic": "integration.sdkclient.deposits_core", "postingClientLowPriorityResponseTopic": "integration.sdkclient.deposits_core.low_priority", "balanceEventResponseTopic": "vault.core_api.v1.balances.balance.events", "postingBatchEventResponseTopic": "vault.api.v1.postings.posting_instruction_batch.created", "accountUpdateEventResponseTopic": "vault.core_api.v1.accounts.account_update.events", "planUpdateEventResponseTopic": "vault.core_api.v1.plans.plan_update.events", "planAssociateEventResponseTopic": "vault.core_api.v1.plans.account_plan_assoc.events", "accountCreatedEventResponseTopic": "vault.core_api.v1.accounts.account.events", "planCreatedEventResponseTopic": "vault.core_api.v1.plans.plan.events", "thoughtMachineKafkaURL": ["b-1.dbmydevtmmskclust.wlcoxl.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.dbmydevtmmskclust.wlcoxl.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.dbmydevtmmskclust.wlcoxl.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "kafkaConsumerGroupID": "tm-deposits-core-dev", "boostPocketKafkaConsumerGroupID": "tm-deposits-core-bp-dev", "requestTimeoutMillis": 100000, "initialIntervalMillis": 10, "maxIntervalMillis": 100000, "attemptCount": 5, "supervisorContractVersionID": "b9075abf-6ac3-4b09-a0fc-290e81239907", "opsPortalURL": "https://ops.tm.dev.g-bank.app", "bizSupervisorContractVersionID": "2d62e020-0bf0-4b8f-abab-25858c4c35ff", "boostPocketProductVersionID": "914"}, "accountServiceConfig": {"baseURL": "http://account-service.core-banking.svc.cluster.local"}, "transactionLimitConfig": {"baseURL": "http://transaction-limit.payments.svc.cluster.local"}, "productMasterConfig": {"baseURL": "http://product-master.core-banking.svc.cluster.local"}, "depositsExpConfig": {"baseURL": "http://deposits-exp.core-banking.svc.cluster.local"}, "goalCoreConfig": {"baseURL": "http://goal-core.core-banking.svc.cluster.local"}, "accountStatusEventKafkaConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "account-status-update-event-dev", "clusterType": "critical", "enableTLL": true, "stream": "dev-deposits-core-account-status-update-event", "packageName": "pb", "dtoName": "DepositsAccountStatusUpdateEvent", "offsetType": "oldest", "enable": true}, "accountCreationEventKafkaConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "deposits-account-creation-event-dev", "clusterType": "critical", "enableTLL": true, "stream": "dev-deposits-account-creation-event", "packageName": "pb", "dtoName": "DepositsAccountCreateEvent", "offsetType": "oldest", "enable": true}, "postingInstructionKafkaConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "deposits-core-dev", "clusterType": "critical", "enableTLL": true, "stream": "dev-deposits-core-tx", "packageName": "pb", "dtoName": "DepositsCoreTx", "offsetType": "oldest", "enableRetry": true, "producerSQSRetry": {"messageTTL": 60000, "retryBackoff": 1000, "enabled": true}}, "lowPriorityPostingInstructionKafkaConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "deposits-core-dev", "clusterType": "critical", "enableTLL": true, "stream": "dev-deposits-core-low-priority-tx", "packageName": "pb", "dtoName": "DepositsCoreTx", "offsetType": "oldest", "enableRetry": true, "producerSQSRetry": {"messageTTL": 60000, "retryBackoff": 1000, "enabled": true}}, "accountUpdateKafkaConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "account-update-event-dev", "clusterType": "critical", "enableTLL": true, "stream": "dev-account-update-event", "packageName": "pb", "dtoName": "AccountStatusUpdateEvent", "offsetType": "oldest", "enable": true}, "balanceEventKafkaConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "deposits-balance-event-dev", "clusterType": "critical", "enableTLL": true, "stream": "dev-deposits-balance-event", "packageName": "pb", "dtoName": "DepositsBalanceEvent", "offsetType": "oldest", "enableRetry": true, "producerSQSRetry": {"messageTTL": 60000, "retryBackoff": 1000, "enabled": false}}, "postingInstructionBatchCreatedEventKafkaConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "deposits-posting-batch-event-dev", "clusterType": "critical", "enableTLL": true, "stream": "dev-deposits-posting-batch-event", "packageName": "pb", "dtoName": "DepositsCoreTx", "offsetType": "oldest", "enableRetry": true, "producerSQSRetry": {"messageTTL": 60000, "retryBackoff": 1000, "enabled": true}}, "dormantAccountEventKafkaConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "dormant-account-event-dev", "clusterType": "critical", "enableTLL": true, "stream": "dev-deposits-dormant-account-event", "packageName": "pb", "dtoName": "DepositsDormantAccount", "offsetType": "oldest"}, "featureFlags": {"enableTransactionLimit": false, "enableAsyncAccountClosure": true, "enablePriorityPosting": true, "enableAsyncPosting": true, "enableAccountCreationV2": true, "enableRetryableStream": true, "enableSQSBackgroundJob": true, "enableAcctDormantJob": true, "enableAsyncAccountDormantReactivation": true, "enableAsyncAccountDormant": true, "enableBoostPocket": true, "enableAvailabilityCheckForBoostPocket": true}, "workflowConfig": {"enabled": true, "closeCASAAccount": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5}, "auxiliary": {"intervalInSeconds": 10, "maxAttempt": 2}}, "createOperatingAccountWorkflow": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5, "maxTimeWindowFromCreatedAt": 86400}, "auxiliary": {"intervalInSeconds": 10, "maxAttempt": 2}}, "createBoostPocketAccountWorkflow": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5}}, "closeBoostPocketAccountWorkflow": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5}, "auxiliary": {"intervalInSeconds": 20, "maxAttempt": 5}}, "autoRenewBoostPocketAccountWorkflow": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5}, "auxiliary": {"intervalInSeconds": 20, "maxAttempt": 5}}, "reactivateDormantCASAAccountRetryPolicy": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5, "maxTimeWindowFromCreatedAt": 30}}, "dormantCASAAccountRetryPolicy": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5, "maxTimeWindowFromCreatedAt": 30}}}, "redisConfig": {"addr": "clustercfg.dbmy-dev-backend-ec-deposits-core.o9f56r.apse1.cache.amazonaws.com:6379", "idleTimeoutInSec": 1, "poolSize": 300, "readOnlyFromSlaves": false, "readTimeoutInSec": 1, "writeTimeoutInSec": 1, "password": "{{REDIS_PASSWORD}}", "tlsEnabled": true}, "locale": {"currency": "MYR"}, "tenant": "MY", "accountNumberFormat": {"CASA": "-B-L-A-N-C-"}, "retry": {"accountClosureMaxRetries": 3, "accountClosureRetryWaitTimeInMs": 300, "pocketClosureMaxRetries": 3, "pocketClosureRetryWaitTimeInMs": 300, "boostPocketClosureMaxRetries": 3, "boostPocketClosureRetryWaitTimeInMs": 1000, "enquireTransferBatchMaxRetries": 5, "enquireTransferBatchRetryWaitTimeInMs": 100}, "maxChildCount": 999, "slackConfig": {"token": "{{slack_bot_token}}", "defaultChannelID": "C07S2SH5MBL", "enabled": true}, "sqsConfig": {"queueURL": "https://sqs.ap-southeast-1.amazonaws.com/************/dev-backend-deposits-core-sqs20240621033635710000000003", "awsRegion": "ap-southeast-1", "exponentialBackoffBaseIntervalSeconds": 1}, "backgroundJobSQSConfig": {"queueURL": "https://sqs.ap-southeast-1.amazonaws.com/************/dev-backend-deposits-core-sqs-background-job-queue20240628033407992900000004", "awsRegion": "ap-southeast-1", "exponentialBackoffBaseIntervalSeconds": 2, "waitTimeSeconds": 20, "defaultVisibilityTimeoutSeconds": 30, "workerCount": 3, "maxNumberOfMessages": 10, "environment": ""}, "acctDormantJobConfig": {"batchSize": 3, "batchStartFrom": 0, "s3BucketName": "dbmy-dev-backend-casa-dormancy-acct", "fileNamePattern": "dormancy_accounts", "enableDecryptFile": true, "gpgSecretKey": "{{ DORMANCY_ACCT_GPG_PRIVATE_KEY }}"}}