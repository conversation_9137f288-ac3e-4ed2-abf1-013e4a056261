{"name": "deposits-core Service", "serviceName": "deposits-core", "host": "0.0.0.0", "port": 8080, "env": "stg", "ownerInfo": {"name": "digibank", "email": "<EMAIL>", "url": "digibank.myteksi.com"}, "data": {"mysql": {"master": {"dsn": "{{dc_username}}:{{dc_password}}@tcp($MYSQL_HOST$:3306)/$DB_NAME$?parseTime=true&loc=UTC", "maxIdle": 25, "maxOpen": 50, "connMaxLifetime": "1800s"}, "slave": {"dsn": "{{dc_username}}:{{dc_password}}@tcp($MYSQL_HOST_REPLICA$:3306)/$DB_NAME$?parseTime=true&loc=UTC", "maxIdle": 25, "maxOpen": 50, "connMaxLifetime": "1800s"}, "masterCircuitBreaker": {"timeoutInMs": 1000}, "slaveCircuitBreaker": {"timeoutInMs": 1000}}}, "statsd": {"host": "$DOGSTATSD_HOST$", "port": 8125}, "trace": {"host": "$DOGSTATSD_HOST$", "port": 8126}, "logger": {"workerCount": 10, "bufferSize": 10000, "logLevel": 1, "stacktraceLevel": 4, "logFormat": "json", "development": true}, "depositsCoreConfig": {"clientConfig": {"maxIdleConnsPerHost": 64, "idleConnTimeoutInSMillis": 10000, "timeoutInMillis": 4000, "requestLogLockTimeoutInMillis": 5000}, "dormancySchedulerLockDurationInHours": 12, "dormancySchedulerCronExpression": "0 0 * * *", "dormancyThresholdInDays": 365, "interestUpdateScheduleCronExpression": "*/15 * * * *", "interestUpdateSchedulerLockDurationInMinutes": 720, "updateProductAvailabilitySchedulerLockDurationInMinutes": 30, "productVariantCodeConfig": {"depositAccount": "DEPOSITS_ACCOUNT", "operatingAccount": "BIZ_DEPOSIT_ACCOUNT", "savingsPocket": "SAVINGS_POCKET", "boostPocket": "BOOST_POCKET"}, "dynamicConstants": {"defaultCurrencyCode": "MYR", "defaultTimeZoneOffset": 28800}, "boostPocketConfig": {"messagesToUser": {"boostPocketGlobalLimitBreachedMessage": {"code": "ACCOUNT_CREATION_FAILED_PRODUCT_UNAVAILABLE", "message": "Boost Pocket slots have been oversubscribed."}}, "fetchBalanceDelayWindowInSeconds": 14400}, "savingsPocketConfig": {"maxAllowedLength": 4, "productVariant": "SAVINGS_POCKET"}, "childAccountConfig": {"maxAllowedLength": 3}, "tmAccountListMaxPage": 10, "schedulerConfig": {"thoughtMachineFailedJob": {"lockDurationInMinutes": 5}, "productAvailabilityPositionJob": {"BOOST_POCKET": {"enable": true}}}, "thoughtMachineFailedJobConfig": {"maxRepublishCount": 5, "maxRepublishFailureCount": 5, "ignoreJobIDs": ["06cdd8e9c7657a0aac8908bb2a7b3fee", "6f96b739aaed7ec07c5aa3607a32233a"], "ignoreScheduleIDs": ["0261cdd6-6d83-4de4-83b5-217f8401a0a2", "a8a101e5-350b-4f30-a8dd-ba604bddc413"]}, "allowedDepositsTMSchedulers": {"LABYRINTH_SUPERVISOR_DEPOSIT_DAILY_SCHEDULE_AST": true, "LABYRINTH_SUPERVISOR_BIZ_DEPOSIT_DAILY_SCHEDULE_AST": true}, "kafkaPostingPhaseRolloutPercentage": 100, "allowedTxnCodeDomain": {"DEPOSITS": true, "FINANCE": true, "DEBIT_CARD": true, "TREASURY": true, "INSURANCE": true, "BIZ_DEPOSITS": true, "INVEST_P2P": true}, "workflowMonitors": [{"workflowID": "create_boost_pocket_account", "targetSlackChannel": "C07RZG0M3L4", "statusFetchDurationInMinutes": 1440, "batchSizeInMinutes": 60, "dataFetchCOPInMs": 500, "messagePerBatch": 50, "states": [201, 205, 210, 215, 220, 225, 230, 235, 240, 245, 250, 255, 260, 265, 275, 280, 285, 290, 295, 500, 501, 503, 505], "enableAntiSpam": false, "spamCoolingOffInMinutes": 60}, {"workflowID": "auto_renew_boost_pocket_account", "targetSlackChannel": "C07RZG0M3L4", "statusFetchDurationInMinutes": 1440, "batchSizeInMinutes": 60, "dataFetchCOPInMs": 500, "messagePerBatch": 50, "states": [200, 205], "enableAntiSpam": false, "spamCoolingOffInMinutes": 60}]}, "thoughtMachineConfig": {"thoughtMachineURL": "https://core-api.tm.stg.g-bank.app", "thoughtMachineToken": "{{tm_token}}", "postingClientResponseTopic": "integration.sdkclient.deposits_core", "postingClientLowPriorityResponseTopic": "integration.sdkclient.deposits_core.low_priority", "balanceEventResponseTopic": "vault.core_api.v1.balances.balance.events", "postingBatchEventResponseTopic": "vault.api.v1.postings.posting_instruction_batch.created", "accountUpdateEventResponseTopic": "vault.core_api.v1.accounts.account_update.events", "planUpdateEventResponseTopic": "vault.core_api.v1.plans.plan_update.events", "planAssociateEventResponseTopic": "vault.core_api.v1.plans.account_plan_assoc.events", "accountCreatedEventResponseTopic": "vault.core_api.v1.accounts.account.events", "planCreatedEventResponseTopic": "vault.core_api.v1.plans.plan.events", "thoughtMachineKafkaURL": ["b-1.dbmystgtmmskclust.6ym1sy.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.dbmystgtmmskclust.6ym1sy.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.dbmystgtmmskclust.6ym1sy.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "kafkaConsumerGroupID": "tm-deposits-core-stg", "boostPocketKafkaConsumerGroupID": "tm-deposits-core-bp-stg", "enableLazyTopicSubscription": true, "requestTimeoutMillis": 100000, "initialIntervalMillis": 10, "maxIntervalMillis": 100000, "attemptCount": 5, "supervisorContractVersionID": "4d53afc8-37b6-4e8e-8923-4b2117ad96b6", "opsPortalURL": "https://ops.tm.stg.g-bank.app", "bizSupervisorContractVersionID": "438a88d1-1346-43b4-a7b3-8c3a213e5765", "boostPocketProductVersionID": "318"}, "accountServiceConfig": {"baseURL": "http://account-service.core-banking.svc.cluster.local"}, "transactionLimitConfig": {"baseURL": "http://transaction-limit.payments.svc.cluster.local"}, "productMasterConfig": {"baseURL": "http://product-master.core-banking.svc.cluster.local"}, "depositsExpConfig": {"baseURL": "http://deposits-exp.core-banking.svc.cluster.local"}, "goalCoreConfig": {"baseURL": "http://goal-core.core-banking.svc.cluster.local"}, "accountStatusEventKafkaConfig": {"brokers": ["b-1.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "account-status-update-event-stg", "clusterType": "critical", "enableTLL": true, "stream": "stg-deposits-core-account-status-update-event", "packageName": "pb", "dtoName": "DepositsAccountStatusUpdateEvent", "offsetType": "oldest", "enable": true}, "accountCreationEventKafkaConfig": {"brokers": ["b-1.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "deposits-account-creation-event-stg", "clusterType": "critical", "enableTLL": true, "stream": "stg-deposits-account-creation-event", "packageName": "pb", "dtoName": "DepositsAccountCreateEvent", "offsetType": "oldest", "enable": true}, "postingInstructionKafkaConfig": {"brokers": ["b-1.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "deposits-core-stg", "clusterType": "critical", "enableTLL": true, "stream": "stg-deposits-core-tx", "packageName": "pb", "dtoName": "DepositsCoreTx", "offsetType": "oldest", "producerSQSRetry": {"messageTTL": 60000, "retryBackoff": 1000, "enabled": false}, "enableRetry": true}, "balanceEventKafkaConfig": {"brokers": ["b-1.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "deposits-balance-event-stg", "clusterType": "critical", "enableTLL": true, "stream": "stg-deposits-balance-event", "packageName": "pb", "dtoName": "DepositsBalanceEvent", "offsetType": "oldest", "producerSQSRetry": {"messageTTL": 60000, "retryBackoff": 1000, "enabled": false}, "enableRetry": true}, "postingInstructionBatchCreatedEventKafkaConfig": {"brokers": ["b-1.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "deposits-posting-batch-event-stg", "clusterType": "critical", "enableTLL": true, "stream": "stg-deposits-posting-batch-event", "packageName": "pb", "dtoName": "DepositsCoreTx", "offsetType": "oldest", "producerSQSRetry": {"messageTTL": 60000, "retryBackoff": 1000, "enabled": false}, "enableRetry": true}, "lowPriorityPostingInstructionKafkaConfig": {"brokers": ["b-1.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "deposits-core-stg", "clusterType": "critical", "enableTLL": true, "stream": "stg-deposits-core-low-priority-tx", "packageName": "pb", "dtoName": "DepositsCoreTx", "offsetType": "oldest", "producerSQSRetry": {"messageTTL": 60000, "retryBackoff": 1000, "enabled": false}, "enableRetry": true}, "dormantAccountEventKafkaConfig": {"brokers": ["b-1.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "dormant-account-event-stg", "clusterType": "critical", "enableTLL": true, "stream": "stg-deposits-dormant-account-event", "packageName": "pb", "dtoName": "DepositsDormantAccount", "offsetType": "oldest"}, "featureFlags": {"enableTransactionLimit": false, "enableAsyncAccountClosure": false, "enablePriorityPosting": true, "enableAsyncPosting": true, "enableAccountCreationV2": true, "enableRetryableStream": true, "enableAsyncAccountDormantReactivation": true, "enableAsyncAccountDormant": true, "enableSQSBackgroundJob": true, "enableBoostPocket": true, "enableAvailabilityCheckForBoostPocket": true}, "workflowConfig": {"enabled": true, "createOperatingAccountWorkflow": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5, "maxTimeWindowFromCreatedAt": 86400}, "auxiliary": {"intervalInSeconds": 10, "maxAttempt": 2}}, "createBoostPocketAccountWorkflow": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5}}, "closeBoostPocketAccountWorkflow": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5}, "auxiliary": {"intervalInSeconds": 20, "maxAttempt": 5}}, "autoRenewBoostPocketAccountWorkflow": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5}, "auxiliary": {"intervalInSeconds": 20, "maxAttempt": 5}}, "closeCASAAccount": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5}, "auxiliary": {"intervalInSeconds": 10, "maxAttempt": 2}}, "reactivateDormantCASAAccountRetryPolicy": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5, "maxTimeWindowFromCreatedAt": 30}}, "dormantCASAAccountRetryPolicy": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5, "maxTimeWindowFromCreatedAt": 30}}}, "redisConfig": {"addr": "clustercfg.dbmy-stg-cb-ec-deposits-core.esalbp.apse1.cache.amazonaws.com:6379", "idleTimeoutInSec": 1, "poolSize": 300, "readOnlyFromSlaves": false, "readTimeoutInSec": 1, "writeTimeoutInSec": 1, "password": "{{REDIS_PASSWORD}}", "tlsEnabled": true}, "locale": {"currency": "MYR"}, "tenant": "MY", "accountNumberFormat": {"CASA": "-B-L-A-N-C-"}, "retry": {"accountClosureMaxRetries": 3, "accountClosureRetryWaitTimeInMs": 300, "pocketClosureMaxRetries": 3, "pocketClosureRetryWaitTimeInMs": 300, "boostPocketClosureMaxRetries": 3, "boostPocketClosureRetryWaitTimeInMs": 1000, "enquireTransferBatchMaxRetries": 5, "enquireTransferBatchRetryWaitTimeInMs": 100}, "maxChildCount": 999, "slackConfig": {"token": "{{slack_bot_token}}", "defaultChannelID": "C06S0JRQXQE", "enabled": true}, "sqsConfig": {"queueURL": "https://sqs.ap-southeast-1.amazonaws.com/************/stg-cb-deposits-core-sqs20240704065205944400000003", "awsRegion": "ap-southeast-1", "exponentialBackoffBaseIntervalSeconds": 1}, "backgroundJobSQSConfig": {"queueURL": "https://sqs.ap-southeast-1.amazonaws.com/************/stg-cb-deposits-core-sqs-background-job-queue20240919075436966400000004", "awsRegion": "ap-southeast-1", "exponentialBackoffBaseIntervalSeconds": 2, "waitTimeSeconds": 20, "defaultVisibilityTimeoutSeconds": 30, "workerCount": 3, "maxNumberOfMessages": 10, "environment": ""}, "acctDormantJobConfig": {"batchSize": 3, "batchStartFrom": 0, "s3BucketName": "dbmy-stg-cb-casa-dormancy-acct", "fileNamePattern": "dormancy_accounts", "enableDecryptFile": true, "gpgSecretKey": "{{ DORMANCY_ACCT_GPG_PRIVATE_KEY }}"}}