{{- if (and .Values.regionShort (eq .Values.regionShort "apne1") ) }}
---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: access-control-api
  namespace: istio-ingress
spec:
  hosts:
  - "access-control-api.tm.{{ .Values.env }}.g-bank.app"
  - "access-control-api.apne1.tm.{{ .Values.env }}.g-bank.app"
  gateways:
  - tm-gateway
  http:
  - route:
    - destination:
        port:
          number: 8080
        host: access-control-api-gateway.tm-vault.svc.cluster.local
---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: audit-api-gateway
  namespace: istio-ingress
spec:
  hosts:
  - "audit-api.tm.{{ .Values.env }}.g-bank.app"
  - "audit-api.apne1.tm.{{ .Values.env }}.g-bank.app"
  gateways:
  - tm-gateway
  http:
  - route:
    - destination:
        port:
          number: 8080
        host: audit-api-gateway.tm-vault.svc.cluster.local
---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: core-api-gateway
  namespace: istio-ingress
spec:
  hosts:
  - "core-api.tm.{{ .Values.env }}.g-bank.app"
  - "core-api.apne1.tm.{{ .Values.env }}.g-bank.app"
  gateways:
  - tm-gateway
  http:
  - route:
    - destination:
        port:
          number: 8080
        host: core-api-gateway.tm-vault.svc.cluster.local
---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: data-loader-api-gateway
  namespace: istio-ingress
spec:
  hosts:
  - "data-loader-api.tm.{{ .Values.env }}.g-bank.app"
  - "data-loader-api.apne1.tm.{{ .Values.env }}.g-bank.app"
  gateways:
  - tm-gateway
  http:
  - route:
    - destination:
        port:
          number: 8080
        host: data-loader-api-gateway.tm-vault.svc.cluster.local
---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: documentation
  namespace: istio-ingress
spec:
  hosts:
  - "documentation.tm.{{ .Values.env }}.g-bank.app"
  - "documentation.apne1.tm.{{ .Values.env }}.g-bank.app"
  gateways:
  - tm-gateway
  http:
  - route:
    - destination:
        port:
          number: 80
        host: documentation.tm-vault.svc.cluster.local
---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: documents
  namespace: istio-ingress
spec:
  hosts:
  - "documents.tm.{{ .Values.env }}.g-bank.app"
  - "documents.apne1.tm.{{ .Values.env }}.g-bank.app"
  gateways:
  - tm-gateway
  http:
  - route:
    - destination:
        port:
          number: 32666
        host: vault-documents-webserver-internal.tm-vault.svc.cluster.local
---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: products-api-gateway
  namespace: istio-ingress
spec:
  hosts:
  - "products-api.tm.{{ .Values.env }}.g-bank.app"
  - "products-api.apne1.tm.{{ .Values.env }}.g-bank.app"
  gateways:
  - tm-gateway
  http:
  - route:
    - destination:
        port:
          number: 8080
        host: products-api-gateway.tm-vault.svc.cluster.local
---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: saml-idp
  namespace: istio-ingress
spec:
  hosts:
  - "saml-idp.tm.{{ .Values.env }}.g-bank.app"
  - "saml-idp.apne1.tm.{{ .Values.env }}.g-bank.app"
  gateways:
  - tm-gateway
  http:
  - route:
    - destination:
        port:
          number: 8000
        host: saml-idp.tm-vault.svc.cluster.local
---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: admin-website
  namespace: istio-ingress
spec:
  hosts:
  - "ops.tm.{{ .Values.env }}.g-bank.app"
  - "ops.apne1.tm.{{ .Values.env }}.g-bank.app"
  gateways:
  - tm-gateway
  http:
  - route:
    - destination:
        port:
          number: 6008
        host: vault-admin-website.tm-vault.svc.cluster.local
---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: workflow-simulator
  namespace: istio-ingress
spec:
  hosts:
  - "workflow-simulator.tm.{{ .Values.env }}.g-bank.app"
  - "workflow-simulator.apne1.tm.{{ .Values.env }}.g-bank.app"
  gateways:
  - tm-gateway
  http:
  - route:
    - destination:
        port:
          number: 8080
        host: workflow-simulator.tm-vault.svc.cluster.local
---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: workflows-api-gateway
  namespace: istio-ingress
spec:
  hosts:
  - "workflows-api.tm.{{ .Values.env }}.g-bank.app"
  - "workflows-api.apne1.tm.{{ .Values.env }}.g-bank.app"
  gateways:
  - tm-gateway
  http:
  - route:
    - destination:
        port:
          number: 8080
        host: workflows-api-gateway.tm-vault.svc.cluster.local
---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: grafana
  namespace: istio-ingress
spec:
  hosts:
  - "grafana.tm.{{ .Values.env }}.g-bank.app"
  - "grafana.apne1.tm.{{ .Values.env }}.g-bank.app"
  - "tm-grafana.tm.{{ .Values.env }}.g-bank.app"
  - "tm-grafana.apne1.tm.{{ .Values.env }}.g-bank.app"
  gateways:
  - tm-gateway
  http:
  - route:
    - destination:
        port:
          number: {{ .Values.virtualService.grafana.port }}
        host: tm-grafana.tm-monitoring.svc.cluster.local
---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: thanos-query
  namespace: istio-ingress
spec:
  hosts:
    - "metrics.tm.{{ .Values.env }}.g-bank.app"
    - "metrics.apne1.tm.{{ .Values.env }}.g-bank.app"
    - "tm-metrics.tm.{{ .Values.env }}.g-bank.app"
    - "tm-metrics.apne1.tm.{{ .Values.env }}.g-bank.app"
  gateways:
    - tm-gateway
  http:
    - route:
      - destination:
          port:
            number: {{ .Values.virtualService.thanosQuery.port }}
          host: thanos-query.tm-monitoring.svc.cluster.local
---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: alertmanager
  namespace: istio-ingress
spec:
  hosts:
  - "alertmanager.tm.{{ .Values.env }}.g-bank.app"
  - "alertmanager.apne1.tm.{{ .Values.env }}.g-bank.app"
  - "tm-alertmanager.tm.{{ .Values.env }}.g-bank.app"
  - "tm-alertmanager.apne1.tm.{{ .Values.env }}.g-bank.app"
  gateways:
  - tm-gateway
  http:
  - route:
    - destination:
        port:
          number: {{ .Values.virtualService.alertmanager.port }}
        host: alertmanager.tm-monitoring.svc.cluster.local
{{- end }}
