---
# ClusterRole required by <PERSON><PERSON> Installer pod for installing/upgrading Thought Machine Vault instance
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: tm-installer
rules:
  # ---
  - apiGroups:
      - ""
    resources:
      - pods
      - pods/exec
      - configmaps
      - endpoints
      - serviceaccounts
      - services
      - services/proxy
      - services/finalizers
      - secrets
      - namespaces #vault installer patches annotations
    verbs:
      - create
      - delete
      - patch
      - update
  - apiGroups:
      - ""
    resources:
      - pods
      - pods/exec
      - configmaps
      - endpoints
      - serviceaccounts
      - services
      - services/proxy
      - services/finalizers
      - secrets
      - bindings
      - events
      - limitranges
      - deployments
      - namespaces
      - nodes
      - nodes/proxy
      - persistentvolumes
      - persistentvolumeclaims
      - replicationcontrollers
      - resourcequotas
      - statefulsets
    verbs:
      - get
      - list
      - watch
      - patch
  - apiGroups:
      - ""
    resources:
      - pods
      - pods/exec
      - services/proxy
    verbs:
      - deletecollection
  # ---
  - apiGroups:
      - apps
      - extensions
    resources:
      - daemonsets
      - deployments
      - replicasets
      - statefulsets
    verbs:
      - create
      - delete
      - patch
      - update
  - apiGroups:
      - apps
      - extensions
    resources:
      - daemonsets
      - deployments
      - replicasets
      - statefulsets
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - apps
      - extensions
    resources:
      - statefulsets
    verbs:
      - deletecollection
  # ---
  # WTF section
  - apiGroups:
      - apps
      - extensions
    resources:
      - configmaps
      - namespaces
      - pods
      - services
    verbs:
      - create
      - get
      - list
      - watch
  # ---
  - apiGroups:
      - extensions
      - networking.k8s.io
    resources:
      - ingresses
      - networkpolicies
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - extensions
      - networking.k8s.io
    resources:
      - ingresses
    verbs:
      - create
      - delete
      - patch
      - update
  - apiGroups:
      - autoscaling
    resources:
      - horizontalpodautoscalers
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
  - apiGroups:
      - autoscaling.k8s.io
    resources:
      - verticalpodautoscalers
    verbs:
      - list
      - watch
  - apiGroups:
      - batch
    resources:
      - cronjobs
      - jobs
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
  - apiGroups:
      - policy
    resources:
      - poddisruptionbudgets
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
  - apiGroups:
      - rbac.authorization.k8s.io
    resources:
      - clusterroles
      - clusterrolebindings
      - rolebindings
      - roles
    verbs:
      - get
      - list
      - create
      - patch
      - update
      - delete
  - apiGroups:
      - apiextensions.k8s.io
    resources:
      - customresourcedefinitions
    verbs:
      - create
      - get
      - update
      - delete
      - list
      - patch
  - apiGroups:
      - storage.k8s.io
    resources:
      - storageclasses
    verbs:
      - create
      - update
      - patch
      - delete
  - apiGroups:
      - storage.k8s.io
    resources:
      - storageclasses
      - volumeattachments
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - security.istio.io
    resources:
      - authorizationpolicies
      - peerauthentications
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
  - apiGroups:
      - networking.istio.io
    resources:
      - destinationrules
      - sidecars
      - virtualservices #gke only
      - gateways  #tm-installer switcher only
      - serviceentries #gke only
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
  - nonResourceURLs:
      - "/metrics"
    verbs:
      - get
  - apiGroups:
      - apiregistration.k8s.io
    resources:
      - apiservices
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - apiregistration.k8s.io
    resources:
      - apiservices
    verbs:
      - create
      - update
      - patch
      - delete
  - apiGroups:
      - monitoring.coreos.com
    resources:
      - alertmanagers
      - alertmanagers/finalizers
      - alertmanagers/status
      - alertmanagerconfigs
      - probes
      - podmonitors
      - prometheuses
      - prometheusagents
      - prometheusagents/finalizers
      - prometheusagents/status
      - scrapeconfigs
      - prometheuses/finalizers
      - prometheuses/status
      - prometheusrules
      - servicemonitors
      - thanosrulers
      - thanosrulers/finalizers
      - thanosrulers/status
    verbs:
      - create
      - delete
      - patch
      - update
      - get
      - list
      - watch
      - deletecollection
  - apiGroups:
      - custom.metrics.k8s.io
    resources:
      - "*"
    verbs:
      - get
      - list
      - watch
      - create
      - update
      - patch
      - delete
  - apiGroups:
      - metrics.k8s.io
    resources:
      - '*'
    verbs:
      - get
      - list
  - apiGroups:
      - authentication.k8s.io
    resources:
      - tokenreviews
    verbs:
      - create
  - apiGroups:
      - authorization.k8s.io
    resources:
      - subjectaccessreviews
    verbs:
      - create
  - apiGroups:
      - certificates.k8s.io
    resources:
      - certificatesigningrequests
    verbs:
      - list
      - watch
  - apiGroups:
      - admissionregistration.k8s.io
    resources:
      - admissionwebhookconfigurations
      - mutatingwebhookconfigurations
      - validatingwebhookconfigurations
    verbs:
      - get
      - create
      - patch
      - list
      - watch
      - update
      - delete
  - apiGroups:
      - coordination.k8s.io
    resources:
      - leases
    verbs:
      - list
      - watch
      - get
      - update
      - create
      - patch
  - apiGroups:
      - tmachine.io
    resources:
      - disasterrecoveryprocessdefinitions
      - managedwebhooks
      - tmcomponent
      - tmcomponents
      - tmcomponents/status
      - crowns
      - crowns/status
      - crowns/finalizers
    verbs:
      - "*"
  - apiGroups:
      - v1
    resources:
      - configmaps
    verbs:
      - get
  - apiGroups:
      - scheduling.k8s.io
    verbs:
      - delete
      - get
      - list
      - watch
      - update
      - create
      - patch
    resources:
      - priorityclasses
  - apiGroups:
      - ''
    resources:
      - nodes
    verbs:
      - delete
  - apiGroups:
      - ''
    resources:
      - nodes/proxy
    verbs:
      - delete
      - create
      - update
      - delete
  - apiGroups:
      - ''
    resources:
      - persistentvolumeclaims
    verbs:
      - create
      - update
      - delete
  - apiGroups:
      - ''
    resources:
      - pods/log
    verbs:
      - get
      - list
      - watch
      - delete
  - apiGroups:
      - ''
    resources:
      - replicationcontrollers
    verbs:
      - delete
  - apiGroups:
      - admissionregistration.k8s.io
    resources:
      - "*"
    verbs:
      - get
      - create
      - list
      - watch
      - update
      - patch
  - apiGroups:
      - apiextensions.k8s.io
    resources:
      - customresourcedefinitions
    verbs:
      - "*"
  - apiGroups:
      - apps
    resources:
      - deployments/scale
    verbs:
      - get
      - list
      - update
      - watch
      - delete
      - create
      - patch
  - apiGroups:
      - authentication.istio.io
    resources:
      - "*"
    verbs:
      - "*"
  - apiGroups:
      - authentication.k8s.io
    resources:
      - "*"
    verbs:
      - "*"
  - apiGroups:
      - authorization.k8s.io
    resources:
      - "*"
    verbs:
      - "*"
  - apiGroups:
      - certificates.k8s.io
    resources:
      - certificatesigningrequests
    verbs:
      - approve
      - update
      - create
      - get
      - delete
  - apiGroups:
      - certificates.k8s.io
    resources:
      - certificatesigningrequests/approval
    verbs:
      - approve
      - update
      - create
      - get
      - delete
      - watch
  - apiGroups:
      - certificates.k8s.io
    resources:
      - certificatesigningrequests/status
    verbs:
      - approve
      - update
      - create
      - get
      - delete
      - watch
  - apiGroups:
      - certificates.k8s.io
    resources:
      - signers
    verbs:
      - approve
      - update
      - create
      - get
      - delete
      - watch
  - apiGroups:
      - config.istio.io
    resources:
      - "*"
    verbs:
      - "*"
  - apiGroups:
      - discovery.k8s.io
    resources:
      - endpointslices
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - extensions
    resources:
      - deployments/finalizers
    resourceNames:
      - istio-galley
    verbs:
      - update
  - apiGroups:
      - extensions
    resources:
      - deployments/scale
    verbs:
      - get
      - list
      - watch
      - update
      - delete
  - apiGroups:
      - extensions
    resources:
      - ingresses
    verbs:
      - "*"
  - apiGroups:
      - extensions
    resources:
      - ingresses/status
    verbs:
      - "*"
  - apiGroups:
      - extensions
    resources:
      - podsecuritypolicies
    resourceNames:
      - istio
    verbs:
      - use
  - apiGroups:
      - extensions.istio.io
    resources:
      - "*"
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - gateway.networking.k8s.io
    resources:
      - "*"
    verbs:
      - get
      - watch
      - list
      - update
      - patch
  - apiGroups:
      - gateway.networking.k8s.io
    resources:
      - gatewayclasses
    verbs:
      - create
      - delete
  - apiGroups:
      - monitoring.kiali.io
    resources:
      - monitoringdashboards
    verbs:
      - get
      - list
  - apiGroups:
      - multicluster.x-k8s.io
    resources:
      - serviceexports
    verbs:
      - get
      - list
      - watch
      - create
      - delete
  - apiGroups:
      - multicluster.x-k8s.io
    resources:
      - serviceimports
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - networking.istio.io
    resources:
      - "*"
    verbs:
      - "*"
  - apiGroups:
      - networking.k8s.io
    resources:
      - "*"
    verbs:
      - "*"
  - apiGroups:
      - networking.x-k8s.io
    resources:
      - "*"
    verbs:
      - "*"
  - apiGroups:
      - networking.x.k8s.io
    resources:
      - "*"
    verbs:
      - get
      - watch
      - list
  - apiGroups:
      - rbac.authorization.k8s.io
    resources:
      - clusterrolebindings
    verbs:
      - watch
  - apiGroups:
      - rbac.authorization.k8s.io
    resources:
      - clusterroles
    verbs:
      - watch
  - apiGroups:
      - rbac.authorization.k8s.io
    resources:
      - rolebindings
    verbs:
      - watch
  - apiGroups:
      - rbac.authorization.k8s.io
    resources:
      - roles
    verbs:
      - watch
  - apiGroups:
      - rbac.istio.io
    resources:
      - "*"
    verbs:
      - get
      - list
      - watch
      - create
      - delete
      - patch
  - apiGroups:
      - rbac.istio.io
    resources:
      - "*/status"
    verbs:
      - update
  - apiGroups:
      - security.istio.io
    resources:
      - "*"
    verbs:
      - get
      - watch
      - list
      - create
      - delete
      - patch
      - update
  - apiGroups:
      - security.istio.io
    resources:
      - "*/status"
    verbs:
      - update
  - apiGroups:
      - telemetry.istio.io
    resources:
      - "*"
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - ''
    resources:
      - events
    verbs:
      - delete
      - create
      - update
      - patch
  - apiGroups:
      - ''
    resources:
      - namespaces/finalizers
    verbs:
      - update
  - apiGroups:
      - ''
    resources:
      - cronjobs
    verbs:
      - list
  - apiGroups:
      - ''
    resources:
      - jobs
    verbs:
      - list
  - apiGroups:
      - apps
    resources:
      - deployments/finalizers
    resourceNames:
      - tmcomponent
    verbs:
      - update
  - apiGroups:
      - secrets-store.csi.x-k8s.io
    resources:
      - secretproviderclasses
    verbs:
      - get
      - create
      - patch
      - list
      - watch
      - update
      - delete
  - apiGroups:
      - ""
    resources:
      - pods/portforward
    verbs:
      - create
      - update
      - delete
  - apiGroups:
      - k8s.cni.cncf.io
    resources:
      - network-attachment-definitions
    verbs:
      - get
      - update
      - patch
      - create
      - delete
      - watch
      - list
