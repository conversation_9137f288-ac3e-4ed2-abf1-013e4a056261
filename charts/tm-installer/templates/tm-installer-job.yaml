{{- if ne .Values.regionShort "disabled" -}}
apiVersion: batch/v1
kind: Job
metadata:
  name: tm-installer
  annotations:
    sidecar.istio.io/inject: "false"
    argocd.argoproj.io/hook: PostSync
    argocd.argoproj.io/hook-delete-policy: HookSucceeded
  namespace: tm-system
  labels:
    app: tm-installer
    app.kubernetes.io/name: tm-installer
    app.kubernetes.io/instance: tm-installer
    tags.datadoghq.com/service: tm-installer
spec:
  template:
    metadata:
      annotations:
        sidecar.istio.io/inject: "false"
        apm.datadoghq.com/env: '{ "DD_SERVICE": "tm-installer" }'
      labels:
        app: tm-installer
        app.kubernetes.io/name: tm-installer
        app.kubernetes.io/instance: tm-installer
        tags.datadoghq.com/service: tm-installer
    spec:
      containers:
        - image: {{ .Values.replicas.image }}
          command:
            - sh
            - "/config_files/install.sh"
          name: tm-installer
          resources:
            requests:
              cpu: 100m
              memory: 256Mi
            limits:
              cpu: 100m
              memory: 256Mi
          {{- with .Values.installerEnvVars }}
          env:
            {{- toYaml . | nindent 12 }}
          {{- end }}
            - name: ENV
              value: {{ .Values.env }}
          volumeMounts:
            - mountPath: /config_files
              name: config-volume
      restartPolicy: Never
      serviceAccountName: tm-installer
      volumes:
        - name: config-volume
          configMap:
            name: tm-installer
            defaultMode: 0755
{{- end -}}