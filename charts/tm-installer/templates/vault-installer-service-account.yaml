---
apiVersion: v1
kind: ServiceAccount
metadata:
  # service account name. should be the same service account which the vault-installer <PERSON><PERSON><PERSON><PERSON><PERSON> Vault role
  # is set to for successful vault-installer pod and HashiCorp Vault authentication
  name: 'tm-installer'
  # the namespace where the Vault Installer pod is deployed to
  namespace: 'tm-system'
{{- if (and .Values.serviceAccount .Values.serviceAccount.annotations) }}
  {{- with .Values.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- end }}
# this should be uncommented if a private repo is used
#imagePullSecrets:
#  - name: {IMAGE_PULL_SECRETS_NAME}
{{- if (and .Values.serviceAccount .Values.serviceAccount.createServiceAccountCredentials) }}
secrets:
  - name: {{ .Values.serviceAccount.serviceAccountSecretName }}
{{- end }}
