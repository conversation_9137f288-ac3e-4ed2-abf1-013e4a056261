{{- define "scheduled-config-monitor.helper.tpl" -}}
prefix: tm
name: scheduled-config-monitor
fullName: tm-scheduled-config-monitor
containerUser: 65532
{{- end }}

{{- define "scheduled-config-monitor.cmd.tpl" -}}
command:
  - sh
  - /config_files/init.sh
{{- end }}


{{- define "scheduled-config-monitor-label.tpl" -}}
app.kubernetes.io/instance: {{ .fullName }}
app.kubernetes.io/name: {{ .fullName }}
tags.datadoghq.com/service: {{ .fullName }}
{{- end }}

{{- define "scheduled-config-monitor.tpl" -}}
{{ $helperConfig:=(include "scheduled-config-monitor.helper.tpl" . | fromYaml )}}
{{ $appName:= get $helperConfig "name" }}
{{ $appPrefix:= get $helperConfig "prefix" }}
{{ $appFullName:= get $helperConfig "fullName" }}
{{ $envVars:=dict "HOME" "/home/<USER>" }}
{{- range .Values.scheduledConfigMonitor.envVars }}
    {{- $_10 := set $envVars .name .value }}
{{- end }}
{{ $_2 := set .Values "envVars" $envVars }}
{{ $tpl := fromYaml (include "go-app-lib.cron.tpl" .) }}
{{ $metadata := (dig "spec" "jobTemplate" "spec" "template" "metadata" dict $tpl) }}
{{ $label := (get $metadata "labels") }}
{{ $overideLabels := fromYaml (include "scheduled-config-monitor-label.tpl" $helperConfig) }}
{{ $mergedLabels := mergeOverwrite $label $overideLabels }}
{{ $container:= (dig "spec" "jobTemplate" "spec" "template" "spec" "containers" dict $tpl | first)}}
{{ $resources:= (include "cron-resources.tpl" .Values.scheduledConfigMonitor) | fromYaml }}
{{ $_3 := set $container "resources" (get $resources "resources") }}
{{ $_4 := set $container "image" "bitnami/python:3.10.14" }}
{{ $_5 := set $metadata "labels" $mergedLabels }}
{{ $_6 := mergeOverwrite $container (include "scheduled-config-monitor.cmd.tpl" . | fromYaml) }}
{{ $volumeMounts := list }}
{{ $volumeMounts = append $volumeMounts (dict "mountPath" "/config_files" "name" "config-volume") }}
{{ $volumeMounts = append $volumeMounts (dict "mountPath" "/home/<USER>" "name" "home-dir") }}
{{ $_7 := set $container "volumeMounts" $volumeMounts }}
{{ $user := dig "spec" "jobTemplate" "spec" "template" "spec" "securityContext" "runAsUser" $helperConfig.containerUser $tpl }}


metadata:
  name: {{ $appFullName }}
  namespace: tm-system
spec:
  concurrencyPolicy: Replace
  jobTemplate:
    spec:
      template:
        metadata:
          annotations:
            checksum/config: {{ include (print $.Chart.Name "/templates/" $appFullName "-config.yaml") . | sha256sum }}
            sidecar.istio.io/inject: "false"
            vault.hashicorp.com/agent-inject: "true"
            vault.hashicorp.com/agent-init-first: "true"
            vault.hashicorp.com/agent-pre-populate-only: "true"
            vault.hashicorp.com/client-max-retries: '15'
            vault.hashicorp.com/log-format: "json"
            vault.hashicorp.com/log-level: "trace"
            vault.hashicorp.com/role: {{ .Values.scheduledConfigMonitor.vaultRole }}
            vault.hashicorp.com/agent-inject-secret-slack.json: secret/tm/alertmanager
            vault.hashicorp.com/agent-inject-template-slack.json: |
              {{`{{ with secret "secret/tm/alertmanager" -}}
                {
                  "slack_bot_token": "{{ .Data.SLACK_API_URL_env }}"
                }
              {{- end }}`}}
          labels: {{ toYaml $mergedLabels | nindent 12 }}
        spec:
          initContainers:
            - name: init-home-dir
              image: 712221657655.dkr.ecr.ap-southeast-1.amazonaws.com/mirror/docker.io/library/busybox:1.34.1
              command: [ "sh", "-c", "mkdir -p /home/<USER>" ]
              volumeMounts:
                - mountPath: /home/<USER>
                  name: home-dir
              securityContext:
                runAsGroup: {{ $user }}
                runAsUser: {{ $user }}
#            - name: init-vault-secrets
#              image: hashicorp/vault:1.8.0
#              command:
#                - sh
#                - -c
#                - |
#                  # Login to Vault using the Kubernetes auth method
#                  export VAULT_TOKEN=$(vault write -field=token auth/k8s-{{ .Values.env }}-tm-01/login role={{ .Values.scheduledConfigMonitor.vaultRole }} jwt=$(cat /var/run/secrets/kubernetes.io/serviceaccount/token))
#
#                  # Fetch secrets from Vault and write them to a file
#                  vault kv get -version=1 -format=json secret/tm/alertmanager > /vault/secrets/slack.json
#              env:
#                - name: VAULT_ADDR
#                  value: "http://vault-active.vault.svc.cluster.local:8200"  # Vault server address
#              volumeMounts:
#                - name: vault-secrets
#                  mountPath: /vault/secrets
          containers:
            - {{ toYaml $container | nindent 14 }}
          restartPolicy: Never
          serviceAccountName: tm-installer
          volumes:
            - name: config-volume
              configMap:
                name: {{ $appFullName }}-config
                defaultMode: 0755
            - name: home-dir
              emptyDir: {}
      ttlSecondsAfterFinished: 600
  schedule: {{ .Values.scheduledConfigMonitor.schedule }}
  suspend: {{  ternary (hasKey .Values.scheduledConfigMonitor "suspend") .Values.scheduledConfigMonitor.suspend false }}
{{- end }}
{{- if and .Values.scheduledConfigMonitor .Values.scheduledConfigMonitor.enabled -}}
{{- include "go-app-lib.cron" (list . "scheduled-config-monitor.tpl") }}
---
{{- end }}
