---
# Source: tm-installer/templates/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: tm-system
  labels:
    istio-injection: enabled
---
# Source: tm-installer/templates/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: tm-vault
  labels:
    istio-injection: enabled
    istio-annotation-tm-webhook-tm-vault: enabled
    istio-io/rev: "1.14.6"
---
# Source: tm-installer/templates/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: webhook-operator
  labels:
    istio-injection: enabled
---
# Source: tm-installer/templates/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: tm-monitoring
  labels:
    istio-injection: enabled
---
# Source: tm-installer/templates/vault-installer-service-account.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  # service account name. should be the same service account which the vault-installer HashiCorp Vault role
  # is set to for successful vault-installer pod and <PERSON><PERSON><PERSON>orp Vault authentication
  name: 'tm-installer'
  # the namespace where the Vault Installer pod is deployed to
  namespace: 'tm-system'
# this should be uncommented if a private repo is used
#imagePullSecrets:
#  - name: {IMAGE_PULL_SECRETS_NAME}
---
# Source: tm-installer/templates/tm-installer-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: tm-installer
  namespace: tm-system
  labels:
    app: tm-installer
    app.kubernetes.io/name: tm-installer
    app.kubernetes.io/instance: tm-installer
    tags.datadoghq.com/service: tm-installer
data:
  install.sh: |-
    #!/bin/env sh
    set -e
    if [[ "dev" != "${TM_ENV}" ]]
    then
      echo "INFO: Current environment is [dev] but requested installation target TM_ENV is [${TM_ENV}]"
      echo "INFO: This could be due to changes in installer helm chart and triggered a chart upgrade"
      echo "INFO: Skipping installation"
      exit 0
    fi
    # This maintenance mode is to allow manual verification of the pod connectivity and permission setup
    if [[ "${ACTION}" == "manual_maintenance" ]];
    then
      echo "INFO: Bootstrap installer pod..."
      while true
      do
        if [[ -f ".stop" ]];
        then
          echo "INFO: Stop file found. Exiting from manual maintenance"
          exit 0
        fi
        sleep 5
      done
      exit 0
    fi
    echo "INFO: Assume S3 role"
    eval $(aws sts assume-role --role-arn "${AWS_S3_ROLE}" --role-session-name AWSCLI-Session | jq -r '.Credentials | "export S3_ARTEFACT_AWS_ACCESS_KEY_ID=\(.AccessKeyId)\nexport S3_ARTEFACT_AWS_SECRET_ACCESS_KEY=\(.SecretAccessKey)\nexport S3_ARTEFACT_AWS_SESSION_TOKEN=\(.SessionToken)\n"')
    echo "INFO: Download installation files from S3"
    AWS_ACCESS_KEY_ID=$S3_ARTEFACT_AWS_ACCESS_KEY_ID AWS_SECRET_ACCESS_KEY=$S3_ARTEFACT_AWS_SECRET_ACCESS_KEY AWS_SESSION_TOKEN=$S3_ARTEFACT_AWS_SESSION_TOKEN aws s3 sync s3://${S3_ARTEFACT_PATH} .
    if ! [[ -f "tokens/${TM_DEPLOY_TOKEN}" ]]
    then
      echo "INFO: Deployment token [${TM_DEPLOY_TOKEN}] not found in tokens directory"
      echo "INFO: Skipping installation"
      exit 0
    else
      echo "INFO: Deleting deployment token [${TM_DEPLOY_TOKEN}] before installation"
      AWS_ACCESS_KEY_ID=$S3_ARTEFACT_AWS_ACCESS_KEY_ID AWS_SECRET_ACCESS_KEY=$S3_ARTEFACT_AWS_SECRET_ACCESS_KEY AWS_SESSION_TOKEN=$S3_ARTEFACT_AWS_SESSION_TOKEN aws s3 rm s3://${S3_ARTEFACT_PATH}/tokens/${TM_DEPLOY_TOKEN}
      echo "INFO: Deployment token deleted"
    fi
    mkdir -p values
    mv "${TM_VERSION}-values.yaml" "values/${TM_VERSION}-values.yaml"
    echo "INFO: Start installation"
    dryrun_flag=""
    if [[ "${DRYRUN}" == "true" ]]
    then
      dryrun_flag="-d"
    fi
    main_exit_code=0

    case ${ACTION} in
    upgrade|install)
      echo "INFO: Run installation with LOAD_S3=${LOAD_S3} ./install.sh -r ${TM_VERSION} -a ${ACTION} ${dryrun_flag}"
      LOAD_S3=$LOAD_S3 sh ./install.sh -r ${TM_VERSION} -a "${ACTION}" "${dryrun_flag}" || main_exit_code=$?
      ;;
    configure_values)
      echo "INFO: Run configure values with LOAD_S3=${LOAD_S3} ./configure_values.sh -r ${TM_VERSION} ${dryrun_flag}"
      LOAD_S3=$LOAD_S3 sh ./configure_values.sh -r ${TM_VERSION} "${dryrun_flag}" || main_exit_code=$?
      ;;
    configure_alertmanager)
      echo "INFO: Run configure alert manager with LOAD_S3=${LOAD_S3} ./configure_alertmanager.sh -r ${TM_VERSION} ${dryrun_flag}"
      LOAD_S3=$LOAD_S3 sh ./configure_alertmanager.sh -r ${TM_VERSION} "${dryrun_flag}" || main_exit_code=$?
      ;;
    configure_audit_cleanup)
      echo "INFO: Run configure audit cleanup cron with LOAD_S3=${LOAD_S3} ./configure_audit_cleanup.sh -r ${TM_VERSION} ${dryrun_flag}"
      LOAD_S3=$LOAD_S3 sh ./configure_audit_cleanup.sh -r ${TM_VERSION} "${dryrun_flag}" || main_exit_code=$?
      ;;
    configure_scheduler_poller)
      echo "INFO: Run configure scheduler-job-poller with LOAD_S3=${LOAD_S3} ./configure_scheduler_poller.sh -r ${TM_VERSION} ${dryrun_flag}"
      LOAD_S3=$LOAD_S3 sh ./configure_scheduler_poller.sh -r ${TM_VERSION} "${dryrun_flag}" || main_exit_code=$?
      ;;
    configure_prometheus_vault_storage)
      echo "INFO: Run configure prometheus-vault-storage with LOAD_S3=${LOAD_S3} ./configure_prometheus_vault_storage.sh -r ${TM_VERSION} ${dryrun_flag}"
      LOAD_S3=$LOAD_S3 sh ./configure_prometheus_vault_storage.sh -r ${TM_VERSION} "${dryrun_flag}" || main_exit_code=$?
      ;;
    run_audit_cleanup)
      echo "INFO: Run manual audit log cleanup with LOAD_S3=${LOAD_S3} ./run_audit_cleanup.sh -r ${TM_VERSION} ${dryrun_flag}"
      LOAD_S3=$LOAD_S3 sh ./run_audit_cleanup.sh -r ${TM_VERSION} "${dryrun_flag}" || main_exit_code=$?
      ;;
    configure_vault_custom_alerts)
      echo "INFO: Run manual audit log cleanup with LOAD_S3=${LOAD_S3} ./configure_vault_custom_alerts.sh -r ${TM_VERSION} ${dryrun_flag}"
      LOAD_S3=$LOAD_S3 sh ./configure_vault_custom_alerts.sh -r ${TM_VERSION} "${dryrun_flag}" || main_exit_code=$?
      ;;
    *)
      if [[ -z ${ACTION} ]]
      then
        echo "WARNING: No action specified. Exiting"
      else
        echo "ERROR: Unknown action [${ACTION}]" >&2
      fi
      exit 0
      ;;
    esac

    if [[ "${main_exit_code}" -ne 0 ]]
    then
      echo "ERROR: Installation failed" >&2
    else
      echo "INFO: Installation succeeded"
    fi
    exit 0
---
# Source: tm-installer/templates/tm-scheduled-redis-restarter-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: tm-scheduled-redis-restarter
  namespace: tm-system
  labels:
    app: tm-scheduled-redis-restarter
    app.kubernetes.io/name: tm-scheduled-redis-restarter
    app.kubernetes.io/instance: tm-scheduled-redis-restarter
    tags.datadoghq.com/service: tm-scheduled-redis-restarter
data:
  run-restart.sh: |-
    #!/bin/env sh
    set -e

    if ! [ "${ENABLED}" = "true" ]; then
      echo "INFO: Restarter is not enabled"
      exit 0
    fi

    # setup kubectl
    namespace=tm-system
    serviceAccount=tm-installer
    secretName=$(kubectl --namespace $namespace get serviceAccount $serviceAccount -o jsonpath='{.secrets[0].name}')
    KUBE_API_EP="https://$KUBERNETES_PORT_443_TCP_ADDR:443"
    KUBE_API_TOKEN="$(cat /var/run/secrets/kubernetes.io/serviceaccount/token)"
    KUBE_CERT=$(kubectl --namespace $namespace get secret/$secretName -o jsonpath='{.data.ca\.crt}' | base64 -d)

    echo "$KUBE_CERT
    " >deploy.crt
    kubectl config set-cluster k8s --server=$KUBE_API_EP --certificate-authority=deploy.crt --embed-certs=true
    kubectl config set-credentials tm-installer --token=$KUBE_API_TOKEN
    kubectl config set-context k8s --cluster k8s --user tm-installer
    kubectl config use-context k8s
    function normalise_memory {
      local memory=$1
      local unit=$(echo $memory | sed 's/[^a-zA-Z]*//g')
      local value=$(echo $memory | sed 's/[^0-9]*//g')
      if [ -z "$unit" ]; then
        echo ${value}
      else
        case $unit in
          Ki)
            echo $((value * 1024))
            ;;
          Mi)
            echo $((value * 1024 * 1024))
            ;;
          Gi)
            echo $((value * 1024 * 1024 * 1024))
            ;;
          *)
            echo "ERROR: Unknown memory unit $unit"
            exit 1
            ;;
        esac
      fi
    }

    deployment_name=redis-vault-postings
    default_mem_threshold_ratio=0.8
    mem_threshold_ratio=$1
    if [ -z "$mem_threshold_ratio" ]; then
      echo "INFO: Memory threshold ratio is not provided, using default value ${default_mem_threshold_ratio}"
      mem_threshold_ratio=${default_mem_threshold_ratio}
    fi

    # Check redis container memory usage using kubectl
    pod_name=$(kubectl -n tm-vault get pods -l app=${deployment_name} -o jsonpath='{.items[0].metadata.name}')
    echo "INFO: Redis pod name is ${pod_name}"

    memory_usage=$(kubectl get --raw "/apis/metrics.k8s.io/v1beta1/namespaces/tm-vault/pods/${pod_name}" | jq -r '.containers[] | select(.name == "redis") | .usage.memory')
    echo "INFO: Redis memory usage is ${memory_usage}"
    memory_usage=$(normalise_memory $memory_usage)

    memory_limit=$(kubectl get pods -n tm-vault $pod_name -o=json | jq '.spec.containers[] | select(.name == "redis")|.resources.limits.memory')
    echo "INFO: Redis memory limit is ${memory_limit}"
    memory_limit=$(normalise_memory $memory_limit)

    # compute the memory usage ratio
    memory_usage_ratio=$(echo "scale=2; $memory_usage / $memory_limit" | bc)
    echo "INFO: Memory usage ratio is ${memory_usage_ratio}"
    if [ $(echo "$memory_usage_ratio >= ${mem_threshold_ratio}" | bc) -eq 1 ]; then
      echo "INFO: Memory usage ratio is gte than ${mem_threshold_ratio}, restart the deployment"
      kubectl rollout restart  -n tm-vault deployment ${deployment_name}
    fi
---
# Source: tm-installer/templates/tm-scheduled-scaler-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: tm-scheduled-scaler
  namespace: tm-system
  labels:
    app: tm-scheduled-scaler
    app.kubernetes.io/name: tm-scheduled-scaler
    app.kubernetes.io/instance: tm-scheduled-scaler
    tags.datadoghq.com/service: tm-scheduled-scaler
data:
  run-scaling.sh: |-
    #!/bin/env sh
    set -e

    if ! [ "${ENABLED}" = "true" ]; then
      echo "INFO: Scaling is not enabled"
      exit 0
    fi

    # setup kubectl
    namespace=tm-system
    serviceAccount=tm-installer
    secretName=$(kubectl --namespace $namespace get serviceAccount $serviceAccount -o jsonpath='{.secrets[0].name}')
    KUBE_API_EP="https://$KUBERNETES_PORT_443_TCP_ADDR:443"
    KUBE_API_TOKEN="$(cat /var/run/secrets/kubernetes.io/serviceaccount/token)"
    KUBE_CERT=$(kubectl --namespace $namespace get secret/$secretName -o jsonpath='{.data.ca\.crt}' | base64 -d)

    echo "$KUBE_CERT
    " >deploy.crt
    kubectl config set-cluster k8s --server=$KUBE_API_EP --certificate-authority=deploy.crt --embed-certs=true
    kubectl config set-credentials tm-installer --token=$KUBE_API_TOKEN
    kubectl config set-context k8s --cluster k8s --user tm-installer
    kubectl config use-context k8s

    # Perform scaling by patching HPA
    hpa="${HPA}"
    min_replicas="${MIN_REPLICAS}"
    echo "INFO: Input min replicas for ${hpa} is ${min_replicas}"
    echo "INFO: Getting current max replicas for ${hpa}"
    current_max_replicas=$(kubectl -n tm-vault get hpa "${hpa}" -o jsonpath='{.spec.maxReplicas}')
    echo "INFO: Current max replicas for ${hpa} is ${current_max_replicas}"
    echo "INFO: Getting current min replicas for ${hpa}"
    current_min_replicas=$(kubectl -n tm-vault get hpa "${hpa}" -o jsonpath='{.spec.minReplicas}')
    echo "INFO: Current min replicas for ${hpa} is ${current_min_replicas}"
    if [ "${current_max_replicas}" -ge "${min_replicas}" ]
    then
      echo "INFO: Patching ${hpa} min replicas from ${current_min_replicas} to ${min_replicas}"
      kubectl -n tm-vault patch hpa "${hpa}" --type='json' -p='[{"op": "replace", "path": "/spec/minReplicas", "value": '"${min_replicas}"'}]'
    else
      echo "WARN: ${min_replicas} is greater than ${current_max_replicas} for ${hpa}. Skipping"
    fi
---
# Source: tm-installer/templates/vault-installer-cluster-role.yaml
# ClusterRole required by Vault Installer pod for installing/upgrading Thought Machine Vault instance
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: tm-installer
rules:
  # ---
  - apiGroups:
      - ""
    resources:
      - pods
      - pods/exec
      - configmaps
      - endpoints
      - serviceaccounts
      - services
      - services/proxy
      - services/finalizers
      - secrets
      - namespaces #vault installer patches annotations
    verbs:
      - create
      - delete
      - patch
      - update
  - apiGroups:
      - ""
    resources:
      - pods
      - pods/exec
      - configmaps
      - endpoints
      - serviceaccounts
      - services
      - services/proxy
      - services/finalizers
      - secrets
      - bindings
      - events
      - limitranges
      - deployments
      - namespaces
      - nodes
      - nodes/proxy
      - persistentvolumes
      - persistentvolumeclaims
      - replicationcontrollers
      - resourcequotas
      - statefulsets
    verbs:
      - get
      - list
      - watch
      - patch
  - apiGroups:
      - ""
    resources:
      - pods
      - pods/exec
      - services/proxy
    verbs:
      - deletecollection
  # ---
  - apiGroups:
      - apps
      - extensions
    resources:
      - daemonsets
      - deployments
      - replicasets
      - statefulsets
    verbs:
      - create
      - delete
      - patch
      - update
  - apiGroups:
      - apps
      - extensions
    resources:
      - daemonsets
      - deployments
      - replicasets
      - statefulsets
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - apps
      - extensions
    resources:
      - statefulsets
    verbs:
      - deletecollection
  # ---
  # WTF section
  - apiGroups:
      - apps
      - extensions
    resources:
      - configmaps
      - namespaces
      - pods
      - services
    verbs:
      - create
      - get
      - list
      - watch
  # ---
  - apiGroups:
      - extensions
      - networking.k8s.io
    resources:
      - ingresses
      - networkpolicies
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - extensions
      - networking.k8s.io
    resources:
      - ingresses
    verbs:
      - create
      - delete
      - patch
      - update
  - apiGroups:
      - autoscaling
    resources:
      - horizontalpodautoscalers
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
  - apiGroups:
      - autoscaling.k8s.io
    resources:
      - verticalpodautoscalers
    verbs:
      - list
      - watch
  - apiGroups:
      - batch
    resources:
      - cronjobs
      - jobs
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
  - apiGroups:
      - policy
    resources:
      - poddisruptionbudgets
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
  - apiGroups:
      - rbac.authorization.k8s.io
    resources:
      - clusterroles
      - clusterrolebindings
      - rolebindings
      - roles
    verbs:
      - get
      - list
      - create
      - patch
      - update
      - delete
  - apiGroups:
      - apiextensions.k8s.io
    resources:
      - customresourcedefinitions
    verbs:
      - create
      - get
      - update
      - delete
      - list
      - patch
  - apiGroups:
      - storage.k8s.io
    resources:
      - storageclasses
    verbs:
      - create
      - update
      - patch
      - delete
  - apiGroups:
      - storage.k8s.io
    resources:
      - storageclasses
      - volumeattachments
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - security.istio.io
    resources:
      - authorizationpolicies
      - peerauthentications
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
  - apiGroups:
      - networking.istio.io
    resources:
      - destinationrules
      - sidecars
      - virtualservices #gke only
      - gateways  #tm-installer switcher only
      - serviceentries #gke only
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
  - nonResourceURLs:
      - "/metrics"
    verbs:
      - get
  - apiGroups:
      - apiregistration.k8s.io
    resources:
      - apiservices
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - apiregistration.k8s.io
    resources:
      - apiservices
    verbs:
      - create
      - update
      - patch
      - delete
  - apiGroups:
      - monitoring.coreos.com
    resources:
      - alertmanagers
      - alertmanagers/finalizers
      - alertmanagers/status
      - alertmanagerconfigs
      - probes
      - podmonitors
      - prometheuses
      - prometheusagents
      - prometheusagents/finalizers
      - prometheusagents/status
      - scrapeconfigs
      - prometheuses/finalizers
      - prometheuses/status
      - prometheusrules
      - servicemonitors
      - thanosrulers
      - thanosrulers/finalizers
      - thanosrulers/status
    verbs:
      - create
      - delete
      - patch
      - update
      - get
      - list
      - watch
      - deletecollection
  - apiGroups:
      - custom.metrics.k8s.io
    resources:
      - "*"
    verbs:
      - get
      - list
      - watch
      - create
      - update
      - patch
      - delete
  - apiGroups:
      - metrics.k8s.io
    resources:
      - '*'
    verbs:
      - get
      - list
  - apiGroups:
      - authentication.k8s.io
    resources:
      - tokenreviews
    verbs:
      - create
  - apiGroups:
      - authorization.k8s.io
    resources:
      - subjectaccessreviews
    verbs:
      - create
  - apiGroups:
      - certificates.k8s.io
    resources:
      - certificatesigningrequests
    verbs:
      - list
      - watch
  - apiGroups:
      - admissionregistration.k8s.io
    resources:
      - admissionwebhookconfigurations
      - mutatingwebhookconfigurations
      - validatingwebhookconfigurations
    verbs:
      - get
      - create
      - patch
      - list
      - watch
      - update
      - delete
  - apiGroups:
      - coordination.k8s.io
    resources:
      - leases
    verbs:
      - list
      - watch
      - get
      - update
      - create
      - patch
  - apiGroups:
      - tmachine.io
    resources:
      - disasterrecoveryprocessdefinitions
      - managedwebhooks
      - tmcomponent
      - tmcomponents
      - tmcomponents/status
      - crowns
      - crowns/status
      - crowns/finalizers
    verbs:
      - "*"
  - apiGroups:
      - v1
    resources:
      - configmaps
    verbs:
      - get
  - apiGroups:
      - scheduling.k8s.io
    verbs:
      - delete
      - get
      - list
      - watch
      - update
      - create
      - patch
    resources:
      - priorityclasses
  - apiGroups:
      - ''
    resources:
      - nodes
    verbs:
      - delete
  - apiGroups:
      - ''
    resources:
      - nodes/proxy
    verbs:
      - delete
      - create
      - update
      - delete
  - apiGroups:
      - ''
    resources:
      - persistentvolumeclaims
    verbs:
      - create
      - update
      - delete
  - apiGroups:
      - ''
    resources:
      - pods/log
    verbs:
      - get
      - list
      - watch
      - delete
  - apiGroups:
      - ''
    resources:
      - replicationcontrollers
    verbs:
      - delete
  - apiGroups:
      - admissionregistration.k8s.io
    resources:
      - "*"
    verbs:
      - get
      - create
      - list
      - watch
      - update
      - patch
  - apiGroups:
      - apiextensions.k8s.io
    resources:
      - customresourcedefinitions
    verbs:
      - "*"
  - apiGroups:
      - apps
    resources:
      - deployments/scale
    verbs:
      - get
      - list
      - update
      - watch
      - delete
      - create
      - patch
  - apiGroups:
      - authentication.istio.io
    resources:
      - "*"
    verbs:
      - "*"
  - apiGroups:
      - authentication.k8s.io
    resources:
      - "*"
    verbs:
      - "*"
  - apiGroups:
      - authorization.k8s.io
    resources:
      - "*"
    verbs:
      - "*"
  - apiGroups:
      - certificates.k8s.io
    resources:
      - certificatesigningrequests
    verbs:
      - approve
      - update
      - create
      - get
      - delete
  - apiGroups:
      - certificates.k8s.io
    resources:
      - certificatesigningrequests/approval
    verbs:
      - approve
      - update
      - create
      - get
      - delete
      - watch
  - apiGroups:
      - certificates.k8s.io
    resources:
      - certificatesigningrequests/status
    verbs:
      - approve
      - update
      - create
      - get
      - delete
      - watch
  - apiGroups:
      - certificates.k8s.io
    resources:
      - signers
    verbs:
      - approve
      - update
      - create
      - get
      - delete
      - watch
  - apiGroups:
      - config.istio.io
    resources:
      - "*"
    verbs:
      - "*"
  - apiGroups:
      - discovery.k8s.io
    resources:
      - endpointslices
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - extensions
    resources:
      - deployments/finalizers
    resourceNames:
      - istio-galley
    verbs:
      - update
  - apiGroups:
      - extensions
    resources:
      - deployments/scale
    verbs:
      - get
      - list
      - watch
      - update
      - delete
  - apiGroups:
      - extensions
    resources:
      - ingresses
    verbs:
      - "*"
  - apiGroups:
      - extensions
    resources:
      - ingresses/status
    verbs:
      - "*"
  - apiGroups:
      - extensions
    resources:
      - podsecuritypolicies
    resourceNames:
      - istio
    verbs:
      - use
  - apiGroups:
      - extensions.istio.io
    resources:
      - "*"
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - gateway.networking.k8s.io
    resources:
      - "*"
    verbs:
      - get
      - watch
      - list
      - update
      - patch
  - apiGroups:
      - gateway.networking.k8s.io
    resources:
      - gatewayclasses
    verbs:
      - create
      - delete
  - apiGroups:
      - monitoring.kiali.io
    resources:
      - monitoringdashboards
    verbs:
      - get
      - list
  - apiGroups:
      - multicluster.x-k8s.io
    resources:
      - serviceexports
    verbs:
      - get
      - list
      - watch
      - create
      - delete
  - apiGroups:
      - multicluster.x-k8s.io
    resources:
      - serviceimports
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - networking.istio.io
    resources:
      - "*"
    verbs:
      - "*"
  - apiGroups:
      - networking.k8s.io
    resources:
      - "*"
    verbs:
      - "*"
  - apiGroups:
      - networking.x-k8s.io
    resources:
      - "*"
    verbs:
      - "*"
  - apiGroups:
      - networking.x.k8s.io
    resources:
      - "*"
    verbs:
      - get
      - watch
      - list
  - apiGroups:
      - rbac.authorization.k8s.io
    resources:
      - clusterrolebindings
    verbs:
      - watch
  - apiGroups:
      - rbac.authorization.k8s.io
    resources:
      - clusterroles
    verbs:
      - watch
  - apiGroups:
      - rbac.authorization.k8s.io
    resources:
      - rolebindings
    verbs:
      - watch
  - apiGroups:
      - rbac.authorization.k8s.io
    resources:
      - roles
    verbs:
      - watch
  - apiGroups:
      - rbac.istio.io
    resources:
      - "*"
    verbs:
      - get
      - list
      - watch
      - create
      - delete
      - patch
  - apiGroups:
      - rbac.istio.io
    resources:
      - "*/status"
    verbs:
      - update
  - apiGroups:
      - security.istio.io
    resources:
      - "*"
    verbs:
      - get
      - watch
      - list
      - create
      - delete
      - patch
      - update
  - apiGroups:
      - security.istio.io
    resources:
      - "*/status"
    verbs:
      - update
  - apiGroups:
      - telemetry.istio.io
    resources:
      - "*"
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - ''
    resources:
      - events
    verbs:
      - delete
      - create
      - update
      - patch
  - apiGroups:
      - ''
    resources:
      - namespaces/finalizers
    verbs:
      - update
  - apiGroups:
      - ''
    resources:
      - cronjobs
    verbs:
      - list
  - apiGroups:
      - ''
    resources:
      - jobs
    verbs:
      - list
  - apiGroups:
      - apps
    resources:
      - deployments/finalizers
    resourceNames:
      - tmcomponent
    verbs:
      - update
  - apiGroups:
      - secrets-store.csi.x-k8s.io
    resources:
      - secretproviderclasses
    verbs:
      - get
      - create
      - patch
      - list
      - watch
      - update
      - delete
  - apiGroups:
      - ""
    resources:
      - pods/portforward
    verbs:
      - create
      - update
      - delete
  - apiGroups:
      - k8s.cni.cncf.io
    resources:
      - network-attachment-definitions
    verbs:
      - get
      - update
      - patch
      - create
      - delete
      - watch
      - list
---
# Source: tm-installer/templates/vault-installer-cluster-role-binding.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: tm-installer
subjects:
  - kind: ServiceAccount
    # service account name. should be the same service account which the tm-installer HashiCorp Vault role
    # is set to for successful tm-installer pod and HashiCorp Vault authentication
    name: 'tm-installer'
    # the namespace where the Vault Installer pod is deployed to
    namespace: 'tm-system'
roleRef:
  kind: ClusterRole
  # Allows read/write access to most resources in a namespace, including the ability
  # to create roles and rolebindings within the namespace. It does not allow write
  # access to resource quota or to the namespace itself.
  name: tm-installer
  apiGroup: rbac.authorization.k8s.io
---
# Source: tm-installer/templates/tm-installer-replica-set.yaml
apiVersion: apps/v1
kind: ReplicaSet
metadata:
  name: tm-installer
  annotations:
    sidecar.istio.io/inject: "false"
  namespace: tm-system
spec:
  replicas: 0
  selector:
    matchLabels:
      app: tm-installer
  template:
    metadata:
      annotations:
        sidecar.istio.io/inject: "false"
      labels:
        sidecar.istio.io/inject: "false"
        app: tm-installer
    spec:
      containers:
        - image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/tm/installer:4.6.14-manual
          command:
            - sleep
            - "10d"
          name: tm-installer
          resources:
            requests:
              cpu: 100m
              memory: 256Mi
            limits:
              cpu: 100m
              memory: 256Mi
      restartPolicy: Always
      serviceAccountName: tm-installer
