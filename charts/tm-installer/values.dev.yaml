env: dev
serviceAccount:
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks/cluster/dev-tm-01/tm-installer
virtualService:
  alertmanager:
    port: 9093
  grafana:
    port: 8080
  thanosQuery:
    port: 8080
vaultAnnotation:
#  we will manage the annotation in dedicated templates
  disabled: true
scheduleScaling:
  vault-account-hpa:
    scaleOut:
      enabled: "true"
      schedule: "30 15 * * *"
      minReplicas: 6
    scaleIn:
      enabled: "true"
      schedule: "30 16 * * *"
      minReplicas: 5
scheduleRedisRestart:
  enabled: "true"
  schedule: "30 18 * * *"
  memoryThreshold: "0.80"
scheduledMonitor:
  requests:
    memory: 256Mi
    cpu: 100m
  limits:
    memory: 256Mi
  schedule: "0 * * * *"
  suspend: false
  enabled: true
  vaultRole: "tm-scheduled-monitoring"
  vaultDbRole: "dbmy-dev-tm-postgres-rds-postgres-ro"
  dbHost: "dbmy-dev-tm-postgres-rds-postgres.cjmchrv0whqs.ap-southeast-1.rds.amazonaws.com"
scheduledFailedJobMonitor:
  requests:
    memory: 256Mi
    cpu: 100m
  limits:
    memory: 256Mi
  schedule: "0 * * * *"
  suspend: false
  enabled: true
  vaultRole: "tm-scheduled-monitoring"
  vaultDbRole: "dbmy-dev-tm-postgres-rds-postgres-ro"
  dbHost: "dbmy-dev-tm-postgres-rds-postgres.cjmchrv0whqs.ap-southeast-1.rds.amazonaws.com"
  envVars:
    - name: JOB_SCHEDULE_START_TIME
      # time format (must be utc): 2024-05-13T16:00:00Z, setting this to empty will default to beginning of the day
      value: ""
    - name: TM_OPS_PORTAL_HOST
      value: "ops.tm.dev.g-bank.app"
# To patch the deprecated failure-domain.beta.kubernetes.io/zone topology key in TM clusters, which causes prometheus
# StatefulSets to get stuck and block all Karpenter disruption operations, only run on admin non workload worker nodes
# 6 times daily between 2am to 3:40am, INFO: https://github.com/aws/karpenter-provider-aws/issues/6440
topologyKeyPatch:
  enabled: true
  image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/mirror/docker.io/alpine/kubectl:1.33.4
  schedule: "*/20 18,19 * * *"
  suspend: false
  startingDeadlineSeconds: 300
scheduledConfigMonitor:
  requests:
    memory: 256Mi
    cpu: 100m
  limits:
    memory: 256Mi
  vaultRole: "tm-scheduled-monitoring"
  schedule: "0 * * * *"
  suspend: false
  enabled: true
  envVars:
    - name: SEND_TO_SLACK
      value: "\"true\""
#    - name: DEBUG_MODE
#      value: "\"true\""
