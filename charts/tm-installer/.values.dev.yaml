env: dev
installerEnvVars:
  - name: TM_ENV
    value: dev
  - name: AWS_S3_ROLE
    value: arn:aws:iam::856922395213:role/dbmy-dev-tm-s3-installer-role
  - name: S3_ARTEFACT_PATH
    value: dbmy-dev-tm-installer-artefact-s3/dev/4.6.57
  - name: TM_VERSION
    value: 4.6.57
  - name: ACTION
    value: upgrade
  - name: DRYRUN
    value: "false"
  - name: UPDATED_AT
    value: 2025-09-09T07:28:35+00:00
  - name: TM_DEPLOY_TOKEN
    value: "d05a90f-2029100705"
  - name: TM_APPROVAL_TOKEN
    value: "approval-approved-d05a90f-2029100705"
  - name: TM_APPROVAL_READY_TOKEN
    value: "approval-ready-d05a90f-2029100705"
  - name: TM_REGION_SHORT
    value: ""
updatedOn: 2025-09-09T07:28:35+00:00
# FIXME This config has no bearing on the chart but merely to prevent nil pointer error on template parsing in Argo
image:
  repository: 712221657655.dkr.ecr.ap-southeast-1.amazonaws.com/tm/installer:4.6.14-manual
  tag: "d05a90f-2029100705"

