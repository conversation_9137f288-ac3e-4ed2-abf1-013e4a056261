---
# Source: tm-installer/templates/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: tm-system
  labels:
    istio-injection: enabled
---
# Source: tm-installer/templates/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: tm-vault
  labels:
    istio-injection: enabled
    istio-annotation-tm-webhook-tm-vault: enabled
    istio-io/rev: "1.14.6"
---
# Source: tm-installer/templates/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: webhook-operator
  labels:
    istio-injection: enabled
---
# Source: tm-installer/templates/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: tm-monitoring
  labels:
    istio-injection: enabled
---
# Source: tm-installer/templates/vault-installer-service-account.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  # service account name. should be the same service account which the vault-installer HashiCorp Vault role
  # is set to for successful vault-installer pod and <PERSON>hiCorp Vault authentication
  name: 'tm-installer'
  # the namespace where the Vault Installer pod is deployed to
  namespace: 'tm-system'
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks/cluster/prd-tm-01/tm-installer
# this should be uncommented if a private repo is used
#imagePullSecrets:
#  - name: {IMAGE_PULL_SECRETS_NAME}
---
# Source: tm-installer/templates/tm-installer-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: tm-installer
  namespace: tm-system
  labels:
    app: tm-installer
    app.kubernetes.io/name: tm-installer
    app.kubernetes.io/instance: tm-installer
    tags.datadoghq.com/service: tm-installer
data:
  install.sh: |-
    #!/bin/env sh
    set -e
    if [[ "prd" != "${TM_ENV}" ]]
    then
      echo "INFO: Current environment is [prd] but requested installation target TM_ENV is [${TM_ENV}]"
      echo "INFO: This could be due to changes in installer helm chart and triggered a chart upgrade"
      echo "INFO: Skipping installation"
      exit 0
    fi
    # This maintenance mode is to allow manual verification of the pod connectivity and permission setup
    if [[ "${ACTION}" == "manual_maintenance" ]];
    then
      echo "INFO: Bootstrap installer pod..."
      while true
      do
        if [[ -f ".stop" ]];
        then
          echo "INFO: Stop file found. Exiting from manual maintenance"
          exit 0
        fi
        sleep 5
      done
      exit 0
    fi
    echo "INFO: Assume S3 role"
    eval $(aws sts assume-role --role-arn "${AWS_S3_ROLE}" --role-session-name AWSCLI-Session | jq -r '.Credentials | "export S3_ARTEFACT_AWS_ACCESS_KEY_ID=\(.AccessKeyId)\nexport S3_ARTEFACT_AWS_SECRET_ACCESS_KEY=\(.SecretAccessKey)\nexport S3_ARTEFACT_AWS_SESSION_TOKEN=\(.SessionToken)\n"')
    echo "INFO: Download installation files from S3"
    AWS_ACCESS_KEY_ID=$S3_ARTEFACT_AWS_ACCESS_KEY_ID AWS_SECRET_ACCESS_KEY=$S3_ARTEFACT_AWS_SECRET_ACCESS_KEY AWS_SESSION_TOKEN=$S3_ARTEFACT_AWS_SESSION_TOKEN aws s3 sync s3://${S3_ARTEFACT_PATH} .
    if ! [[ -f "tokens/${TM_DEPLOY_TOKEN}" ]]
    then
      echo "INFO: Deployment token [${TM_DEPLOY_TOKEN}] not found in tokens directory"
      echo "INFO: Skipping installation"
      exit 0
    else
      echo "INFO: Deleting deployment token [${TM_DEPLOY_TOKEN}] before installation"
      AWS_ACCESS_KEY_ID=$S3_ARTEFACT_AWS_ACCESS_KEY_ID AWS_SECRET_ACCESS_KEY=$S3_ARTEFACT_AWS_SECRET_ACCESS_KEY AWS_SESSION_TOKEN=$S3_ARTEFACT_AWS_SESSION_TOKEN aws s3 rm s3://${S3_ARTEFACT_PATH}/tokens/${TM_DEPLOY_TOKEN}
      echo "INFO: Deployment token deleted"
    fi
    mkdir -p values
    mv "${TM_VERSION}-values.yaml" "values/${TM_VERSION}-values.yaml"
    echo "INFO: Start installation"
    dryrun_flag=""
    if [[ "${DRYRUN}" == "true" ]]
    then
      dryrun_flag="-d"
    fi
    main_exit_code=0

    case ${ACTION} in
    upgrade|install)
      echo "INFO: Run installation with LOAD_S3=${LOAD_S3} ./install.sh -r ${TM_VERSION} -a ${ACTION} ${dryrun_flag}"
      LOAD_S3=$LOAD_S3 sh ./install.sh -r ${TM_VERSION} -a "${ACTION}" "${dryrun_flag}" || main_exit_code=$?
      ;;
    configure_values)
      echo "INFO: Run configure values with LOAD_S3=${LOAD_S3} ./configure_values.sh -r ${TM_VERSION} ${dryrun_flag}"
      LOAD_S3=$LOAD_S3 sh ./configure_values.sh -r ${TM_VERSION} "${dryrun_flag}" || main_exit_code=$?
      ;;
    configure_alertmanager)
      echo "INFO: Run configure alert manager with LOAD_S3=${LOAD_S3} ./configure_alertmanager.sh -r ${TM_VERSION} ${dryrun_flag}"
      LOAD_S3=$LOAD_S3 sh ./configure_alertmanager.sh -r ${TM_VERSION} "${dryrun_flag}" || main_exit_code=$?
      ;;
    configure_audit_cleanup)
      echo "INFO: Run configure audit cleanup cron with LOAD_S3=${LOAD_S3} ./configure_audit_cleanup.sh -r ${TM_VERSION} ${dryrun_flag}"
      LOAD_S3=$LOAD_S3 sh ./configure_audit_cleanup.sh -r ${TM_VERSION} "${dryrun_flag}" || main_exit_code=$?
      ;;
    configure_scheduler_poller)
      echo "INFO: Run configure scheduler-job-poller with LOAD_S3=${LOAD_S3} ./configure_scheduler_poller.sh -r ${TM_VERSION} ${dryrun_flag}"
      LOAD_S3=$LOAD_S3 sh ./configure_scheduler_poller.sh -r ${TM_VERSION} "${dryrun_flag}" || main_exit_code=$?
      ;;
    configure_prometheus_vault_storage)
      echo "INFO: Run configure prometheus-vault-storage with LOAD_S3=${LOAD_S3} ./configure_prometheus_vault_storage.sh -r ${TM_VERSION} ${dryrun_flag}"
      LOAD_S3=$LOAD_S3 sh ./configure_prometheus_vault_storage.sh -r ${TM_VERSION} "${dryrun_flag}" || main_exit_code=$?
      ;;
    run_audit_cleanup)
      echo "INFO: Run manual audit log cleanup with LOAD_S3=${LOAD_S3} ./run_audit_cleanup.sh -r ${TM_VERSION} ${dryrun_flag}"
      LOAD_S3=$LOAD_S3 sh ./run_audit_cleanup.sh -r ${TM_VERSION} "${dryrun_flag}" || main_exit_code=$?
      ;;
    configure_vault_custom_alerts)
      echo "INFO: Run manual audit log cleanup with LOAD_S3=${LOAD_S3} ./configure_vault_custom_alerts.sh -r ${TM_VERSION} ${dryrun_flag}"
      LOAD_S3=$LOAD_S3 sh ./configure_vault_custom_alerts.sh -r ${TM_VERSION} "${dryrun_flag}" || main_exit_code=$?
      ;;
    *)
      if [[ -z ${ACTION} ]]
      then
        echo "WARNING: No action specified. Exiting"
      else
        echo "ERROR: Unknown action [${ACTION}]" >&2
      fi
      exit 0
      ;;
    esac

    if [[ "${main_exit_code}" -ne 0 ]]
    then
      echo "ERROR: Installation failed" >&2
    else
      echo "INFO: Installation succeeded"
    fi
    exit 0
---
# Source: tm-installer/templates/tm-scheduled-failed-job-monitor-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: tm-scheduled-failed-job-monitor-config
  namespace: tm-system
  labels:
    app: tm-scheduled-failed-job-monitor
    app.kubernetes.io/name: tm-scheduled-failed-job-monitor
    app.kubernetes.io/instance: tm-scheduled-failed-job-monitor
    tags.datadoghq.com/service: tm-scheduled-failed-job-monitor
data:
  monitor.py: |
    
    import re
    from datetime import datetime, timedelta, timezone
    import json
    import logging
    import os
    import sys
    import time
    import html
    
    import psycopg2
    import requests
    from datadog import initialize, statsd
    
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    
    logger = logging.getLogger()
    logger.setLevel(logging.DEBUG)
    handler = logging.StreamHandler(sys.stdout)
    handler.setLevel(logging.DEBUG)
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    # for local testing
    IS_LOCAL = os.getenv('LOCAL') == '1'
    TARGET_DATABASE = 'scheduler'
    DATABASE_HOST=os.getenv('DB_HOST')
    SLACK_WEBHOOK_URL=""
    DATABASE_USERNAME=""
    DATABASE_PASSWORD=""
    TM_OPS_PORTAL_HOST=os.getenv("TM_OPS_PORTAL_HOST", "")
    TM_FAILED_JOB_STATUS=3
    # metric
    TM_FAILED_JOB_COUNT_METRIC_NAME = "tm.scheduler.failed_job_count"
    TM_OUTSTANDING_JOB_COUNT_METRIC_NAME = "tm.scheduler.outstanding_job_count"
    CONFIG = {}
    
    def init_cred():
        logger.debug("loading creds")
        # Load database credentials from JSON file
        db_cred_path = 'tmp/db_creds.json' if IS_LOCAL else '/vault/secrets/db_creds.json'
        with open(db_cred_path, 'r') as f:
            db_creds = json.load(f)
    
        slack_cred_path = 'tmp/slack.json' if IS_LOCAL else '/vault/secrets/slack.json'
        with open(slack_cred_path, 'r') as f:
            slack_creds = json.load(f)
        global SLACK_WEBHOOK_URL, DATABASE_USERNAME, DATABASE_PASSWORD
        SLACK_WEBHOOK_URL = slack_creds["data"]["SLACK_API_URL_env"] if not IS_LOCAL else os.getenv("SLACK_WEBHOOK_URL")
        DATABASE_USERNAME = db_creds.get('username', None) or (db_creds.get('data', None) and db_creds['data'].get('username', None))
        DATABASE_PASSWORD = db_creds.get('password', None) or (db_creds.get('data', None) and db_creds['data'].get('password', None))
    
    JOB_SCHEDULE_START_TIME = os.getenv("JOB_SCHEDULE_START_TIME", "")
    JOB_SCHEDULE_END_TIME = os.getenv("JOB_SCHEDULE_END_TIME", "")
    
    
    def init_config():
        config_path = "charts/tm-installer/files/failed_job_monitor/config.prd.json" if IS_LOCAL else "/config_files/config.json"
        logger.info(f"loading config from {config_path}")
        global CONFIG
        with open(config_path, "r") as f:
            CONFIG = json.load(f)
    
    logger.info("initialising config...")
    init_config()
    
    def get_job_schedule_query_start_time_str() -> str:
        global JOB_SCHEDULE_START_TIME
        if JOB_SCHEDULE_START_TIME == "" or JOB_SCHEDULE_START_TIME is None:
            beginning_of_day = get_utc_minus_1ms_before_today_gmt8()
            JOB_SCHEDULE_START_TIME = beginning_of_day.strftime("%Y-%m-%dT%H:%M:%SZ")
            return JOB_SCHEDULE_START_TIME
        return JOB_SCHEDULE_START_TIME
    
    
    def get_utc_minus_1ms_before_today_gmt8():
        now_utc = datetime.now(timezone.utc)
    
        gmt8 = timezone(timedelta(hours=8))
        now_gmt8 = now_utc.astimezone(gmt8)
    
        today_start_gmt8 = now_gmt8.replace(hour=0, minute=0, second=0, microsecond=0)
    
        one_ms = timedelta(milliseconds=1)
        before_today_start_gmt8 = today_start_gmt8 - one_ms
    
        result_utc = before_today_start_gmt8.astimezone(timezone.utc)
    
        return result_utc
    
    
    def initiate_statsd():
        if IS_LOCAL:
            return
        options = {
            'statsd_host': os.getenv('DOGSTATSD_HOST'),
            'statsd_port': 8125
        }
        initialize(**options)
    
    
    class DummyStatsd:
        def gauge(self, *args, **kwargs): logger.info(f"DummyStatsd: gauge {args} {kwargs}")
    
        def increment(self, *args, **kwargs): logger.info(f"DummyStatsd: increment {args} {kwargs}")
    
        def decrement(self, *args, **kwargs): logger.info(f"DummyStatsd: decrement {args} {kwargs}")
    
        def histogram(self, *args, **kwargs): logger.info(f"DummyStatsd: histogram {args} {kwargs}")
    
        def timing(self, *args, **kwargs): logger.info(f"DummyStatsd: timing {args} {kwargs}")
    
        def set(self, *args, **kwargs): logger.info(f"DummyStatsd: set {args} {kwargs}")
    
        def service_check(self, *args, **kwargs): logger.info(f"DummyStatsd: service_check {args} {kwargs}")
    
        def event(self, *args, **kwargs): logger.info(f"DummyStatsd: event {args} {kwargs}")
    
    STATSD_CLIENT = DummyStatsd() if IS_LOCAL else statsd
    
    
    def get_db_conn(dbname: str):
        logger.info(f"connecting to db {dbname}")
        return psycopg2.connect(
            dbname=dbname,
            user=DATABASE_USERNAME,
            password=DATABASE_PASSWORD,
            host=DATABASE_HOST,
        )
    
    def get_excluded_schedule_ids(quoted: bool = False) -> list[str]:
        schedule_ids =  CONFIG.get("exclude_schedule_ids", [])
        if len(schedule_ids) == 0:
            return []
        if quoted:
            schedule_ids = [f"'{id}'" for id in schedule_ids]
        return schedule_ids
    
    
    FAILED_JOB_COUNT_QUERY = f"""
    SELECT tags.name as schedule_tag, count(1) as failed_job_count 
    FROM scheduler.jobs j 
    JOIN scheduler.schedules s ON j.schedule_id = s.id 
    JOIN scheduler.job_metadata jm ON jm.job_id = j.id 
    JOIN scheduler.schedules_tags st ON s.id = st.schedule_id 
    JOIN scheduler.tags tags on st.tag_id = tags.id 
    WHERE j.status = {TM_FAILED_JOB_STATUS} and j.scheduled_timestamp >= '{get_job_schedule_query_start_time_str()}'
    AND {"j.schedule_id not in (" + ",".join(get_excluded_schedule_ids(True)) + ")" if len(get_excluded_schedule_ids()) > 0 else "1 = 1"}
    GROUP BY tags.name
    """
    
    
    FAILED_JOB_DATA_QUERY = f"""
    SELECT 
        j.id AS job_id,
        s.id AS schedule_id,
        j.trigger_timestamp AS job_trigger_timestamp,
        jm.description as error_description
    FROM scheduler.jobs j
    JOIN scheduler.schedules s 
        ON j.schedule_id = s.id
    JOIN scheduler.job_metadata jm 
        ON jm.job_id = j.id
    WHERE 
        j.status = {TM_FAILED_JOB_STATUS} 
        AND j.scheduled_timestamp >= '{get_job_schedule_query_start_time_str()}'
        AND {"j.schedule_id not in (" + ",".join(get_excluded_schedule_ids(True)) + ")" if len(get_excluded_schedule_ids()) > 0 else "1 = 1"}
    ORDER BY 
        j.scheduled_timestamp DESC
    LIMIT 25
    """
    
    OUTSTANDING_JOB_COUNT_QUERY = f"""
    SELECT 
        tag.name,
        count(1) as outstanding_job_count
    FROM scheduler.jobs_outstanding jo
    LEFT JOIN scheduler.jobs j 
        ON j.id = jo.id
    INNER JOIN scheduler.schedules s 
        ON j.schedule_id = s.id
    JOIN scheduler.schedules_tags st ON s.id = st.schedule_id 
    LEFT JOIN scheduler.tags tag
        ON st.tag_id = tag.id
    WHERE 
        j.id IS NULL 
        AND jo.enqueued_timestamp < (NOW() - interval '1 hour')
        AND jo.scheduled_timestamp >= '{get_job_schedule_query_start_time_str()}'
        AND {"j.schedule_id not in (" + ",".join(get_excluded_schedule_ids(True)) + ")" if len(get_excluded_schedule_ids()) > 0 else "1 = 1"}
    GROUP BY tag.name
    """
    
    OUTSTANDING_JOB_DATA_QUERY = f"""
    SELECT 
        jo.id AS job_id,
        jo.schedule_id AS schedule_id,
        jo.trigger_timestamp AS job_trigger_timestamp
    FROM scheduler.jobs_outstanding jo
    LEFT JOIN scheduler.jobs j 
        ON j.id = jo.id
    WHERE 
        j.id IS NULL 
        AND jo.enqueued_timestamp < (NOW() - interval '1 hour')
        AND jo.scheduled_timestamp >= '{get_job_schedule_query_start_time_str()}'
        AND {"j.schedule_id not in (" + ",".join(get_excluded_schedule_ids(True)) + ")" if len(get_excluded_schedule_ids()) > 0 else "1 = 1"}
    ORDER BY 
        jo.scheduled_timestamp DESC
    LIMIT 25
    """
    
    def main():
        failed_job_data_results = []
        outstanding_job_data_results = []
        postgres_cur, postgres_conn = None, None
        try:
            logger.info("Initialising credentials and statsd client...")
            init_cred()
    
            initiate_statsd()
    
            logger.info("Starting job execution status report...")
    
            logger.info("Connecting to database...")
            postgres_conn = get_db_conn(TARGET_DATABASE)
    
            postgres_cur = postgres_conn.cursor()
            logger.info(f"execute failed job count query: {FAILED_JOB_COUNT_QUERY}")
            postgres_cur.execute(FAILED_JOB_COUNT_QUERY)
            failed_job_count_results = postgres_cur.fetchall()
    
    
            if len(failed_job_count_results) > 0:
                logger.info(f"execute failed job data query: {FAILED_JOB_DATA_QUERY}")
                postgres_cur.execute(FAILED_JOB_DATA_QUERY)
                failed_job_data_results = postgres_cur.fetchall()
    
            logger.info(f"execute outstanding job count query: {OUTSTANDING_JOB_COUNT_QUERY}")
            postgres_cur.execute(OUTSTANDING_JOB_COUNT_QUERY)
            outstanding_job_count_results = postgres_cur.fetchall()
    
            if len(outstanding_job_count_results) > 0:
                logger.info(f"execute outstanding job data query: {OUTSTANDING_JOB_DATA_QUERY}")
                postgres_cur.execute(OUTSTANDING_JOB_DATA_QUERY)
                outstanding_job_data_results = postgres_cur.fetchall()
    
        finally:
            # Close the cursor and connection
            if postgres_cur:
                postgres_cur.close()
            if postgres_conn:
                postgres_conn.close()
    
        # Publish metrics
        # publish failed job count metrics by scheduler
        if len(failed_job_count_results) > 0:
            for row in failed_job_count_results:
                schedule_tag = row[0]
                failed_job_count = row[1]
                metric_tags = [f"schedule_tag:{schedule_tag}"]
                logger.info(f"Publishing failed job count for schedule tag {schedule_tag}: {failed_job_count}")
                STATSD_CLIENT.gauge(TM_FAILED_JOB_COUNT_METRIC_NAME, failed_job_count, tags=metric_tags)
        else:
            STATSD_CLIENT.gauge(TM_FAILED_JOB_COUNT_METRIC_NAME, 0)
    
        # Publish outstanding job count metrics by scheduler
        if len(outstanding_job_count_results) > 0:
            for row in outstanding_job_count_results:
                schedule_tag = row[0]
                outstanding_job_count = row[1]
                metric_tags = [f"schedule_tag:{schedule_tag}"]
                logger.info(f"Publishing outstanding job count for schedule tag {schedule_tag}: {outstanding_job_count}")
                STATSD_CLIENT.gauge('tm.scheduler.outstanding_job_count', outstanding_job_count, tags=metric_tags)
        else:
            STATSD_CLIENT.gauge(TM_OUTSTANDING_JOB_COUNT_METRIC_NAME, 0)
    
        # aggregate total failed job count
        total_failed_job_count = 0
        for row in failed_job_count_results:
            total_failed_job_count += row[1]
        logger.info(f"Total failed job count: {total_failed_job_count}")
    
        # aggregate total outstanding job count
        total_outstanding_job_count = 0
        for row in outstanding_job_count_results:
            total_outstanding_job_count += row[1]
        logger.info(f"Total outstanding job count: {total_outstanding_job_count}")
    
        send_to_slack(failed_job_count_results, failed_job_data_results,
                      outstanding_job_count_results, outstanding_job_data_results)
    
    def truncate_txt(error_msg: str, max_length=100) -> str:
        """
    
        :param error_msg: The error message to truncate and sanitize
        :param max_length: The output length will not exceed this value, including the ellipsis if truncated. The input will be truncated more than the given max length to accommodate the ellipsis.
        :return: The truncated and sanitized error message
        """
        if error_msg is None or error_msg == "":
            return ""
        eff_max_length = max_length - 3
    
        if len(error_msg) < eff_max_length:
            return error_msg[:min(len(error_msg), eff_max_length)]
    
        return error_msg[:eff_max_length] + "..." if len(error_msg)-3 > max_length else error_msg
    
    
    def send_to_slack(failed_job_summary, failed_job_samples,
                      outstanding_job_summary, outstanding_job_samples):
        if not SLACK_WEBHOOK_URL or SLACK_WEBHOOK_URL == "":
            logger.error("SLACK_WEBHOOK_URL environment variable is not set")
            return
    
        # Format failed jobs summary
        failed_summary = []
        total_failed = 0
        if failed_job_summary:
            for row in failed_job_summary:
                schedule_tag, count = row
                total_failed += count
                failed_summary.append(f"• {schedule_tag}: {count} failed jobs")
    
        # Format outstanding jobs summary
        outstanding_summary = []
        total_outstanding = 0
        if outstanding_job_summary:
            for row in outstanding_job_summary:
                schedule_tag, count = row
                total_outstanding += count
                outstanding_summary.append(f"• {schedule_tag}: {count} outstanding jobs")
    
        message_title_emoji = "🚨" if total_failed > 0 or total_outstanding > 0 else "✅"
        tm_failed_job_dashboard_url = f"https://{TM_OPS_PORTAL_HOST}/organisation-history/schedules?status=SCHEDULE_STATUS_FAILED"
    
        message = {
            "blocks": [
                {
                    "type": "header",
                    "text": {
                        "type": "plain_text",
                        "text": f"{message_title_emoji} Job Execution Status Report"
                    }
                },
                {
                    "type": "section",
                    "text": {
                        "type": "mrkdwn",
                        "text": f"*<{tm_failed_job_dashboard_url}|Failed Jobs Summary>* (Total: {total_failed})\n" +
                                (("\n".join(failed_summary)) if failed_summary else "No failed jobs")
                    }
                }
            ]
        }
    
        # Only add failed job samples if there are any
        if failed_job_samples and len(failed_job_samples) > 0:
            message["blocks"].append({
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "*Failed Job Samples* (job_id, schedule_id, trigger_timestamp, error_description):"
                }
            })
            # chunk the samples in 5 row per section to prevent breaching char limit in slack block
            for i in range(0, len(failed_job_samples), 5):
                chunk = failed_job_samples[i:i + 5]
                message["blocks"].append({
                    "type": "section",
                    "text": {
                        "type": "mrkdwn",
                        "text":"```\n" + "\n".join(
                            [f"({row[0]}, {row[1]}, '{row[2]}', '{truncate_txt(row[3], 100)}')" for row in chunk])  + "\n```"
                    }
                })
    
        message["blocks"].extend([
            {
                "type": "divider"
            },
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"*Outstanding Jobs Summary* (Total: {total_outstanding})\n" +
                            (("\n".join(outstanding_summary)) if outstanding_summary else "No outstanding jobs")
                }
            }
        ])
    
        # Only add outstanding job samples if there are any
        if outstanding_job_samples and len(outstanding_job_samples) > 0:
            message["blocks"].append({
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "*Outstanding Job Samples* (job_id, schedule_id, trigger_timestamp):\n```\n" +
                            "\n".join([f"({row[0]}, {row[1]}, '{row[2]}')" for row in outstanding_job_samples]) +
                            "\n```"
                }
            })
    
        # Send the message to Slack
        try:
            logger.info("Sending message to Slack...")
            response = requests.post(SLACK_WEBHOOK_URL, json=message, timeout=5)
            response.raise_for_status()
            logger.info("Message sent to Slack successfully")
        except requests.exceptions.RequestException as e:
            err_msg = f"Failed to send message to Slack: {e}"
            err_msg = re.sub(r"https://[^ ]+", "https://<REDACTED>", err_msg)
            logger.error(f"Failed to send message to Slack: {err_msg}")
            if e.response is not None:
                logger.error(f"Slack response: {e.response.text}")
    
    main()
  init.sh: |
    
    
    exit_code=0
    echo "INFO: Installing dependency"
    pip install --user psycopg2-binary datadog requests || exit_code=$?
    
    if [ $exit_code -ne 0 ]; then
        echo "ERROR: Failed to install psycopg2-binary, datadog and requests"
        exit 0
    fi
    
    echo "INFO: Running Thought Machine failed job monitoring script"
    python /config_files/monitor.py || exit_code=$?
    
    if [ $exit_code -ne 0 ]; then
        echo "ERROR: Failed to run monitor.py"
        exit 0
    fi
    
    echo "INFO: Thought Machine failed job monitor script completed successfully, exiting..."
    
  config.json: |
    
    {
      "exclude_schedule_ids": [
        "fabcf775-95fe-4683-bbb8-3cf6ef8eff12",
        "8db7072f-7966-4612-b9d5-1b9e513555e4",
        "41800f14-3aad-4782-848d-e32368dd78c6",
        "61b2a2de-2caa-44d0-a43d-7cbc30ba070e",
        "3032dc63-6ce3-4bbe-8b93-c2c759ced7cd"
      ]
    }
---
# Source: tm-installer/templates/tm-scheduled-monitor-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: tm-scheduled-monitor-config
  namespace: tm-system
  labels:
    app: tm-scheduled-monitor
    app.kubernetes.io/name: tm-scheduled-monitor
    app.kubernetes.io/instance: tm-scheduled-monitor
    tags.datadoghq.com/service: tm-scheduled-monitor
data:
  monitor.py: |
    
    import json
    import logging
    import os
    import sys
    import time
    
    import psycopg2
    from datadog import initialize, statsd
    
    logger = logging.getLogger()
    logger.setLevel(logging.DEBUG)
    handler = logging.StreamHandler(sys.stdout)
    handler.setLevel(logging.DEBUG)
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    # for local testing
    IS_LOCAL = os.getenv('LOCAL') == '1'
    TARGET_DATABASES = [
        'access_control', 'attribute_library', 'audit', 'balances', 'calendar', 'calendar_schedules', 'configuration_layer',
        'core''data_loader', 'eplatform', 'integrations', 'ledger_balances', 'ops', 'parameter_store',
        'postings', 'product_catalogue', 'schedule_manager', 'scheduler', 'switchboard', 'vault', 'xpl',
    ]
    TARGET_DATABASES_VALUES = ','.join([f"'{db}'" for db in TARGET_DATABASES])
    
    
    logger.debug("loading creds")
    # Load database credentials from JSON file
    cred_path = 'tmp/db_creds.json' if IS_LOCAL else '/vault/secrets/db_creds.json'
    with open(cred_path, 'r') as f:
        db_creds = json.load(f)
    
    logger.info("connecting to db")
    db_username = db_creds.get('username', None) or (db_creds.get('data', None) and db_creds['data'].get('username', None))
    db_password = db_creds.get('password', None) or (db_creds.get('data', None) and db_creds['data'].get('password', None))
    
    
    def initiate_statsd():
        if IS_LOCAL:
            return
        options = {
            'statsd_host': os.getenv('DOGSTATSD_HOST'),
            'statsd_port': 8125
        }
        initialize(**options)
    
    
    class DummyStatsd:
        def gauge(self, *args, **kwargs): logger.info(f"DummyStatsd: gauge {args} {kwargs}")
        def increment(self, *args, **kwargs): logger.info(f"DummyStatsd: increment {args} {kwargs}")
        def decrement(self, *args, **kwargs): logger.info(f"DummyStatsd: decrement {args} {kwargs}")
        def histogram(self, *args, **kwargs): logger.info(f"DummyStatsd: histogram {args} {kwargs}")
        def timing(self, *args, **kwargs): logger.info(f"DummyStatsd: timing {args} {kwargs}")
        def set(self, *args, **kwargs): logger.info(f"DummyStatsd: set {args} {kwargs}")
        def service_check(self, *args, **kwargs): logger.info(f"DummyStatsd: service_check {args} {kwargs}")
        def event(self, *args, **kwargs): logger.info(f"DummyStatsd: event {args} {kwargs}")
    
    
    statsd_client = DummyStatsd() if IS_LOCAL else statsd
    
    
    def get_db_conn(dbname: str):
        logger.info(f"connecting to db {dbname}")
        return psycopg2.connect(
            dbname=dbname,
            user=db_username,
            password=db_password,
            host=os.getenv('DB_HOST'),
        )
    
    
    initiate_statsd()
    
    postgres_conn = get_db_conn('postgres')
    postgres_cur = postgres_conn.cursor()
    
    list_db_query = f"""
    select datname from pg_database where datistemplate = false and datname in ({TARGET_DATABASES_VALUES});
    """
    
    table_storage_query = """
    SELECT current_database(), schemaname, tblname, (bs*tblpages)::bigint AS real_size,
    (est_tblpages*bs)::bigint AS estimated_size,
      (tblpages-est_tblpages)*bs AS extra_size,
      CASE WHEN tblpages > 0 AND tblpages - est_tblpages > 0
        THEN 100 * (tblpages - est_tblpages)/tblpages::float
        ELSE 0
      END AS extra_pct, fillfactor,
      CASE WHEN tblpages - est_tblpages_ff > 0
        THEN (tblpages-est_tblpages_ff)*bs
        ELSE 0
      END AS bloat_size,
      CASE WHEN tblpages > 0 AND tblpages - est_tblpages_ff > 0
        THEN 100 * (tblpages - est_tblpages_ff)/tblpages::float
        ELSE 0
      END AS bloat_pct, is_na
    FROM (
      SELECT ceil(reltuples / ((bs-page_hdr)/tpl_size)) + ceil(toasttuples / 4) AS est_tblpages,
             ceil(reltuples / ((bs-page_hdr)*fillfactor/(tpl_size*100))) + ceil(toasttuples / 4) AS est_tblpages_ff,
             tblpages, fillfactor, bs, tblid, schemaname, tblname, heappages, toastpages, is_na
      FROM (
        SELECT (4 + tpl_hdr_size + tpl_data_size + (2*ma)
                - CASE WHEN tpl_hdr_size%ma = 0 THEN ma ELSE tpl_hdr_size%ma END
                - CASE WHEN ceil(tpl_data_size)::int%ma = 0 THEN ma ELSE ceil(tpl_data_size)::int%ma END
               ) AS tpl_size, bs - page_hdr AS size_per_block, (heappages + toastpages) AS tblpages, heappages,
               toastpages, reltuples, toasttuples, bs, page_hdr, tblid, schemaname, tblname, fillfactor, is_na
        FROM (
          SELECT tbl.oid AS tblid, ns.nspname AS schemaname, tbl.relname AS tblname, tbl.reltuples,
                 tbl.relpages AS heappages, coalesce(toast.relpages, 0) AS toastpages,
                 coalesce(toast.reltuples, 0) AS toasttuples,
                 coalesce(substring(array_to_string(tbl.reloptions, ' ') FROM 'fillfactor=([0-9]+)')::smallint, 100) AS fillfactor,
                 current_setting('block_size')::numeric AS bs,
                 CASE WHEN version()~'mingw32' OR version()~'64-bit|x86_64|ppc64|ia64|amd64' THEN 8 ELSE 4 END AS ma,
                 24 AS page_hdr,
                 23 + CASE WHEN MAX(coalesce(s.null_frac,0)) > 0 THEN (7 + count(s.attname)) / 8 ELSE 0::int END
                   + CASE WHEN bool_or(att.attname = 'oid' and att.attnum < 0) THEN 4 ELSE 0 END AS tpl_hdr_size,
                 sum((1-coalesce(s.null_frac, 0)) * coalesce(s.avg_width, 0)) AS tpl_data_size,
                 bool_or(att.atttypid = 'pg_catalog.name'::regtype) OR sum(CASE WHEN att.attnum > 0 THEN 1 ELSE 0 END) <> count(s.attname) AS is_na
          FROM pg_attribute AS att
          JOIN pg_class AS tbl ON att.attrelid = tbl.oid
          JOIN pg_namespace AS ns ON ns.oid = tbl.relnamespace
          LEFT JOIN pg_stats AS s ON s.schemaname=ns.nspname AND s.tablename=tbl.relname AND s.inherited=false AND s.attname=att.attname
          LEFT JOIN pg_class AS toast ON tbl.reltoastrelid = toast.oid
          WHERE NOT att.attisdropped
          AND tbl.relkind in ('r','m')
          GROUP BY 1,2,3,4,5,6,7,8,9,10
          ORDER BY 2,3
        ) AS s
      ) AS s2
    ) AS s3
    where schemaname NOT LIKE 'pg_%' AND schemaname != 'information_schema';
    """
    
    index_storage_query = """
    SELECT current_database(), nspname AS schemaname, tblname, idxname, 
      bs*(relpages)::bigint AS real_size,
      bs*(est_pages)::bigint AS estimated_size,
      bs*(relpages-est_pages)::bigint AS extra_size,
      100 * (relpages-est_pages)::float / relpages AS extra_pct,
      fillfactor,
      CASE WHEN relpages > est_pages_ff THEN bs*(relpages-est_pages_ff) ELSE 0 END AS bloat_size,
      100 * (relpages-est_pages_ff)::float / relpages AS bloat_pct,
      is_na
    FROM (
      SELECT coalesce(1 + ceil(reltuples/floor((bs-pageopqdata-pagehdr)/(4+nulldatahdrwidth)::float)), 0) AS est_pages,
             coalesce(1 + ceil(reltuples/floor((bs-pageopqdata-pagehdr)*fillfactor/(100*(4+nulldatahdrwidth)::float))), 0) AS est_pages_ff,
             bs, nspname, tblname, idxname, relpages, fillfactor, is_na
      FROM (
        SELECT maxalign, bs, nspname, tblname, idxname, reltuples, relpages, idxoid, fillfactor,
               (index_tuple_hdr_bm + maxalign - CASE WHEN index_tuple_hdr_bm%maxalign = 0 THEN maxalign ELSE index_tuple_hdr_bm%maxalign END
                + nulldatawidth + maxalign - CASE WHEN nulldatawidth = 0 THEN 0 WHEN nulldatawidth::integer%maxalign = 0 THEN maxalign ELSE nulldatawidth::integer%maxalign END
               )::numeric AS nulldatahdrwidth, pagehdr, pageopqdata, is_na
        FROM (
          SELECT n.nspname, i.tblname, i.idxname, i.reltuples, i.relpages, i.idxoid, i.fillfactor,
                 current_setting('block_size')::numeric AS bs,
                 CASE WHEN version() ~ 'mingw32' OR version() ~ '64-bit|x86_64|ppc64|ia64|amd64' THEN 8 ELSE 4 END AS maxalign,
                 24 AS pagehdr,
                 16 AS pageopqdata,
                 CASE WHEN max(coalesce(s.null_frac,0)) = 0 THEN 8 ELSE 8 + ((32 + 8 - 1) / 8) END AS index_tuple_hdr_bm,
                 sum((1-coalesce(s.null_frac, 0)) * coalesce(s.avg_width, 1024)) AS nulldatawidth,
                 max(CASE WHEN i.atttypid = 'pg_catalog.name'::regtype THEN 1 ELSE 0 END) > 0 AS is_na
          FROM (
            SELECT ct.relname AS tblname, ct.relnamespace, ic.idxname, ic.attpos, ic.indkey, ic.indkey[ic.attpos], ic.reltuples, ic.relpages, ic.tbloid, ic.idxoid, ic.fillfactor,
                   coalesce(a1.attnum, a2.attnum) AS attnum, coalesce(a1.attname, a2.attname) AS attname, coalesce(a1.atttypid, a2.atttypid) AS atttypid,
                   CASE WHEN a1.attnum IS NULL THEN ic.idxname ELSE ct.relname END AS attrelname
            FROM (
              SELECT idxname, reltuples, relpages, tbloid, idxoid, fillfactor, indkey, pg_catalog.generate_series(1,indnatts) AS attpos
              FROM (
                SELECT ci.relname AS idxname, ci.reltuples, ci.relpages, i.indrelid AS tbloid, i.indexrelid AS idxoid,
                       coalesce(substring(array_to_string(ci.reloptions, ' ') from 'fillfactor=([0-9]+)')::smallint, 90) AS fillfactor,
                       i.indnatts, pg_catalog.string_to_array(pg_catalog.textin(pg_catalog.int2vectorout(i.indkey)),' ')::int[] AS indkey
                FROM pg_catalog.pg_index i
                JOIN pg_catalog.pg_class ci ON ci.oid = i.indexrelid
                WHERE ci.relam=(SELECT oid FROM pg_am WHERE amname = 'btree') AND ci.relpages > 0
              ) AS idx_data
            ) AS ic
            JOIN pg_catalog.pg_class ct ON ct.oid = ic.tbloid
            LEFT JOIN pg_catalog.pg_attribute a1 ON ic.indkey[ic.attpos] <> 0 AND a1.attrelid = ic.tbloid AND a1.attnum = ic.indkey[ic.attpos]
            LEFT JOIN pg_catalog.pg_attribute a2 ON ic.indkey[ic.attpos] = 0 AND a2.attrelid = ic.idxoid AND a2.attnum = ic.attpos
          ) i
          JOIN pg_catalog.pg_namespace n ON n.oid = i.relnamespace
          JOIN pg_catalog.pg_stats s ON s.schemaname = n.nspname AND s.tablename = i.attrelname AND s.attname = i.attname
          GROUP BY 1,2,3,4,5,6,7,8,9,10,11
        ) AS rows_data_stats
      ) AS rows_hdr_pdg_stats
    ) AS relation_stats
    where nspname NOT LIKE 'pg_%' AND nspname != 'information_schema'
    ORDER BY nspname, tblname, idxname;
    """
    
    db_size_query = f"""
    SELECT datname as database, pg_database_size(datname) as size
    FROM pg_stat_database
    WHERE datname IN ({TARGET_DATABASES_VALUES});
    """
    
    # Fetch all database name
    logger.info("execute query")
    postgres_cur.execute(list_db_query)
    databases = postgres_cur.fetchall()
    
    # Fetch db size
    logger.info("fetching db size")
    postgres_cur.execute(db_size_query)
    db_size_results = postgres_cur.fetchall()
    db_size_results = {k: v for (k, v) in zip([row[0] for row in db_size_results], [row[1] for row in db_size_results])}
    
    # Close communication with the database
    postgres_cur.close()
    postgres_conn.close()
    
    all_result = []
    
    for db in databases:
        estimated_size = 0
        real_size = 0
        dbname = db[0]
        logger.info(f"Acquiring connection from database {dbname}")
        conn = get_db_conn(dbname)
        logger.info(f"Fetching table size from database {dbname}")
        cur = conn.cursor()
        # Fetch table size
        cur.execute(table_storage_query)
        results = cur.fetchall()
        rows = [dict(zip([desc[0] for desc in cur.description], row)) for row in results]
        for row in rows:
            estimated_size += row['estimated_size']
            real_size += row['real_size']
    
        # Fetch index size
        logger.info(f"Fetching index size from database {dbname}")
        cur.execute(index_storage_query)
        results = cur.fetchall()
        rows = [dict(zip([desc[0] for desc in cur.description], row)) for row in results]
        for row in rows:
            estimated_size += row['estimated_size']
            real_size += row['real_size']
    
        all_result.append({"dbname": dbname, "estimated_size": estimated_size, 'real_size': real_size})
    
        cur.close()
        conn.close()
        time.sleep(0.2)
    
    logger.info("publishing metrics")
    for result in all_result:
        db_name = result['dbname']
        estimated_size = result['estimated_size']
        real_size = result['real_size']
        size = db_size_results.get(db_name, 0)
    
        logger.info(f"db_name={db_name}, size={size} estimated_size={estimated_size}, real_size={real_size}")
    
        # Send metrics to Datadog
        metric_tags = [f"db_name:{db_name}"]
        logger.info(f"Sending metrics to Datadog")
        statsd_client.gauge('tm.rds.db.size', float(size), tags=metric_tags)
        statsd_client.gauge('tm.rds.db.estimated_size', float(estimated_size), tags=metric_tags)
        statsd_client.gauge('tm.rds.db.real_size', float(real_size), tags=metric_tags)
        time.sleep(0.1)
    
    for handler in logger.handlers:
        handler.close()
        logger.removeHandler(handler)
    
  init.sh: |
    
    
    exit_code=0
    echo "INFO: Installing dependency"
    pip install --user psycopg2-binary datadog || exit_code=$?
    
    if [ $exit_code -ne 0 ]; then
        echo "ERROR: Failed to install psycopg2-binary and datadog"
        exit 0
    fi
    
    echo "INFO: Running monitoring script"
    python /config_files/monitor.py || exit_code=$?
    
    if [ $exit_code -ne 0 ]; then
        echo "ERROR: Failed to run monitor.py"
        exit 0
    fi
    
    echo "INFO: Monitor script completed successfully, exiting..."
---
# Source: tm-installer/templates/tm-scheduled-redis-restarter-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: tm-scheduled-redis-restarter
  namespace: tm-system
  labels:
    app: tm-scheduled-redis-restarter
    app.kubernetes.io/name: tm-scheduled-redis-restarter
    app.kubernetes.io/instance: tm-scheduled-redis-restarter
    tags.datadoghq.com/service: tm-scheduled-redis-restarter
data:
  run-restart.sh: |-
    #!/bin/env sh
    set -e

    if ! [ "${ENABLED}" = "true" ]; then
      echo "INFO: Restarter is not enabled"
      exit 0
    fi

    # setup kubectl
    namespace=tm-system
    serviceAccount=tm-installer
    secretName=$(kubectl --namespace $namespace get serviceAccount $serviceAccount -o jsonpath='{.secrets[0].name}')
    KUBE_API_EP="https://$KUBERNETES_PORT_443_TCP_ADDR:443"
    KUBE_API_TOKEN="$(cat /var/run/secrets/kubernetes.io/serviceaccount/token)"
    KUBE_CERT=$(kubectl --namespace $namespace get secret/$secretName -o jsonpath='{.data.ca\.crt}' | base64 -d)

    echo "$KUBE_CERT
    " >deploy.crt
    kubectl config set-cluster k8s --server=$KUBE_API_EP --certificate-authority=deploy.crt --embed-certs=true
    kubectl config set-credentials tm-installer --token=$KUBE_API_TOKEN
    kubectl config set-context k8s --cluster k8s --user tm-installer
    kubectl config use-context k8s
    function normalise_memory {
      local memory=$1
      local unit=$(echo $memory | sed 's/[^a-zA-Z]*//g')
      local value=$(echo $memory | sed 's/[^0-9]*//g')
      if [ -z "$unit" ]; then
        echo ${value}
      else
        case $unit in
          Ki)
            echo $((value * 1024))
            ;;
          Mi)
            echo $((value * 1024 * 1024))
            ;;
          Gi)
            echo $((value * 1024 * 1024 * 1024))
            ;;
          *)
            echo "ERROR: Unknown memory unit $unit"
            exit 1
            ;;
        esac
      fi
    }

    deployment_name=redis-vault-postings
    default_mem_threshold_ratio=0.8
    mem_threshold_ratio=$1
    if [ -z "$mem_threshold_ratio" ]; then
      echo "INFO: Memory threshold ratio is not provided, using default value ${default_mem_threshold_ratio}"
      mem_threshold_ratio=${default_mem_threshold_ratio}
    fi

    # Check redis container memory usage using kubectl
    pod_name=$(kubectl -n tm-vault get pods -l app=${deployment_name} -o jsonpath='{.items[0].metadata.name}')
    echo "INFO: Redis pod name is ${pod_name}"

    memory_usage=$(kubectl get --raw "/apis/metrics.k8s.io/v1beta1/namespaces/tm-vault/pods/${pod_name}" | jq -r '.containers[] | select(.name == "redis") | .usage.memory')
    echo "INFO: Redis memory usage is ${memory_usage}"
    memory_usage=$(normalise_memory $memory_usage)

    memory_limit=$(kubectl get pods -n tm-vault $pod_name -o=json | jq '.spec.containers[] | select(.name == "redis")|.resources.limits.memory')
    echo "INFO: Redis memory limit is ${memory_limit}"
    memory_limit=$(normalise_memory $memory_limit)

    # compute the memory usage ratio
    memory_usage_ratio=$(echo "scale=2; $memory_usage / $memory_limit" | bc)
    echo "INFO: Memory usage ratio is ${memory_usage_ratio}"
    if [ $(echo "$memory_usage_ratio >= ${mem_threshold_ratio}" | bc) -eq 1 ]; then
      echo "INFO: Memory usage ratio is gte than ${mem_threshold_ratio}, restart the deployment"
      kubectl rollout restart  -n tm-vault deployment ${deployment_name}
    fi
---
# Source: tm-installer/templates/tm-scheduled-scaler-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: tm-scheduled-scaler
  namespace: tm-system
  labels:
    app: tm-scheduled-scaler
    app.kubernetes.io/name: tm-scheduled-scaler
    app.kubernetes.io/instance: tm-scheduled-scaler
    tags.datadoghq.com/service: tm-scheduled-scaler
data:
  run-scaling.sh: |-
    #!/bin/env sh
    set -e

    if ! [ "${ENABLED}" = "true" ]; then
      echo "INFO: Scaling is not enabled"
      exit 0
    fi

    # setup kubectl
    namespace=tm-system
    serviceAccount=tm-installer
    secretName=$(kubectl --namespace $namespace get serviceAccount $serviceAccount -o jsonpath='{.secrets[0].name}')
    KUBE_API_EP="https://$KUBERNETES_PORT_443_TCP_ADDR:443"
    KUBE_API_TOKEN="$(cat /var/run/secrets/kubernetes.io/serviceaccount/token)"
    KUBE_CERT=$(kubectl --namespace $namespace get secret/$secretName -o jsonpath='{.data.ca\.crt}' | base64 -d)

    echo "$KUBE_CERT
    " >deploy.crt
    kubectl config set-cluster k8s --server=$KUBE_API_EP --certificate-authority=deploy.crt --embed-certs=true
    kubectl config set-credentials tm-installer --token=$KUBE_API_TOKEN
    kubectl config set-context k8s --cluster k8s --user tm-installer
    kubectl config use-context k8s

    # Perform scaling by patching HPA
    hpa="${HPA}"
    min_replicas="${MIN_REPLICAS}"
    echo "INFO: Input min replicas for ${hpa} is ${min_replicas}"
    echo "INFO: Getting current max replicas for ${hpa}"
    current_max_replicas=$(kubectl -n tm-vault get hpa "${hpa}" -o jsonpath='{.spec.maxReplicas}')
    echo "INFO: Current max replicas for ${hpa} is ${current_max_replicas}"
    echo "INFO: Getting current min replicas for ${hpa}"
    current_min_replicas=$(kubectl -n tm-vault get hpa "${hpa}" -o jsonpath='{.spec.minReplicas}')
    echo "INFO: Current min replicas for ${hpa} is ${current_min_replicas}"
    if [ "${current_max_replicas}" -ge "${min_replicas}" ]
    then
      echo "INFO: Patching ${hpa} min replicas from ${current_min_replicas} to ${min_replicas}"
      kubectl -n tm-vault patch hpa "${hpa}" --type='json' -p='[{"op": "replace", "path": "/spec/minReplicas", "value": '"${min_replicas}"'}]'
    else
      echo "WARN: ${min_replicas} is greater than ${current_max_replicas} for ${hpa}. Skipping"
    fi
---
# Source: tm-installer/templates/vault-installer-cluster-role.yaml
# ClusterRole required by Vault Installer pod for installing/upgrading Thought Machine Vault instance
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: tm-installer
rules:
  # ---
  - apiGroups:
      - ""
    resources:
      - pods
      - pods/exec
      - configmaps
      - endpoints
      - serviceaccounts
      - services
      - services/proxy
      - services/finalizers
      - secrets
      - namespaces #vault installer patches annotations
    verbs:
      - create
      - delete
      - patch
      - update
  - apiGroups:
      - ""
    resources:
      - pods
      - pods/exec
      - configmaps
      - endpoints
      - serviceaccounts
      - services
      - services/proxy
      - services/finalizers
      - secrets
      - bindings
      - events
      - limitranges
      - deployments
      - namespaces
      - nodes
      - nodes/proxy
      - persistentvolumes
      - persistentvolumeclaims
      - replicationcontrollers
      - resourcequotas
      - statefulsets
    verbs:
      - get
      - list
      - watch
      - patch
  - apiGroups:
      - ""
    resources:
      - pods
      - pods/exec
      - services/proxy
    verbs:
      - deletecollection
  # ---
  - apiGroups:
      - apps
      - extensions
    resources:
      - daemonsets
      - deployments
      - replicasets
      - statefulsets
    verbs:
      - create
      - delete
      - patch
      - update
  - apiGroups:
      - apps
      - extensions
    resources:
      - daemonsets
      - deployments
      - replicasets
      - statefulsets
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - apps
      - extensions
    resources:
      - statefulsets
    verbs:
      - deletecollection
  # ---
  # WTF section
  - apiGroups:
      - apps
      - extensions
    resources:
      - configmaps
      - namespaces
      - pods
      - services
    verbs:
      - create
      - get
      - list
      - watch
  # ---
  - apiGroups:
      - extensions
      - networking.k8s.io
    resources:
      - ingresses
      - networkpolicies
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - extensions
      - networking.k8s.io
    resources:
      - ingresses
    verbs:
      - create
      - delete
      - patch
      - update
  - apiGroups:
      - autoscaling
    resources:
      - horizontalpodautoscalers
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
  - apiGroups:
      - autoscaling.k8s.io
    resources:
      - verticalpodautoscalers
    verbs:
      - list
      - watch
  - apiGroups:
      - batch
    resources:
      - cronjobs
      - jobs
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
  - apiGroups:
      - policy
    resources:
      - poddisruptionbudgets
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
  - apiGroups:
      - rbac.authorization.k8s.io
    resources:
      - clusterroles
      - clusterrolebindings
      - rolebindings
      - roles
    verbs:
      - get
      - list
      - create
      - patch
      - update
      - delete
  - apiGroups:
      - apiextensions.k8s.io
    resources:
      - customresourcedefinitions
    verbs:
      - create
      - get
      - update
      - delete
      - list
      - patch
  - apiGroups:
      - storage.k8s.io
    resources:
      - storageclasses
    verbs:
      - create
      - update
      - patch
      - delete
  - apiGroups:
      - storage.k8s.io
    resources:
      - storageclasses
      - volumeattachments
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - security.istio.io
    resources:
      - authorizationpolicies
      - peerauthentications
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
  - apiGroups:
      - networking.istio.io
    resources:
      - destinationrules
      - sidecars
      - virtualservices #gke only
      - gateways  #tm-installer switcher only
      - serviceentries #gke only
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
  - nonResourceURLs:
      - "/metrics"
    verbs:
      - get
  - apiGroups:
      - apiregistration.k8s.io
    resources:
      - apiservices
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - apiregistration.k8s.io
    resources:
      - apiservices
    verbs:
      - create
      - update
      - patch
      - delete
  - apiGroups:
      - monitoring.coreos.com
    resources:
      - alertmanagers
      - alertmanagers/finalizers
      - alertmanagers/status
      - alertmanagerconfigs
      - probes
      - podmonitors
      - prometheuses
      - prometheusagents
      - prometheusagents/finalizers
      - prometheusagents/status
      - scrapeconfigs
      - prometheuses/finalizers
      - prometheuses/status
      - prometheusrules
      - servicemonitors
      - thanosrulers
      - thanosrulers/finalizers
      - thanosrulers/status
    verbs:
      - create
      - delete
      - patch
      - update
      - get
      - list
      - watch
      - deletecollection
  - apiGroups:
      - custom.metrics.k8s.io
    resources:
      - "*"
    verbs:
      - get
      - list
      - watch
      - create
      - update
      - patch
      - delete
  - apiGroups:
      - metrics.k8s.io
    resources:
      - '*'
    verbs:
      - get
      - list
  - apiGroups:
      - authentication.k8s.io
    resources:
      - tokenreviews
    verbs:
      - create
  - apiGroups:
      - authorization.k8s.io
    resources:
      - subjectaccessreviews
    verbs:
      - create
  - apiGroups:
      - certificates.k8s.io
    resources:
      - certificatesigningrequests
    verbs:
      - list
      - watch
  - apiGroups:
      - admissionregistration.k8s.io
    resources:
      - admissionwebhookconfigurations
      - mutatingwebhookconfigurations
      - validatingwebhookconfigurations
    verbs:
      - get
      - create
      - patch
      - list
      - watch
      - update
      - delete
  - apiGroups:
      - coordination.k8s.io
    resources:
      - leases
    verbs:
      - list
      - watch
      - get
      - update
      - create
      - patch
  - apiGroups:
      - tmachine.io
    resources:
      - disasterrecoveryprocessdefinitions
      - managedwebhooks
      - tmcomponent
      - tmcomponents
      - tmcomponents/status
      - crowns
      - crowns/status
      - crowns/finalizers
    verbs:
      - "*"
  - apiGroups:
      - v1
    resources:
      - configmaps
    verbs:
      - get
  - apiGroups:
      - scheduling.k8s.io
    verbs:
      - delete
      - get
      - list
      - watch
      - update
      - create
      - patch
    resources:
      - priorityclasses
  - apiGroups:
      - ''
    resources:
      - nodes
    verbs:
      - delete
  - apiGroups:
      - ''
    resources:
      - nodes/proxy
    verbs:
      - delete
      - create
      - update
      - delete
  - apiGroups:
      - ''
    resources:
      - persistentvolumeclaims
    verbs:
      - create
      - update
      - delete
  - apiGroups:
      - ''
    resources:
      - pods/log
    verbs:
      - get
      - list
      - watch
      - delete
  - apiGroups:
      - ''
    resources:
      - replicationcontrollers
    verbs:
      - delete
  - apiGroups:
      - admissionregistration.k8s.io
    resources:
      - "*"
    verbs:
      - get
      - create
      - list
      - watch
      - update
      - patch
  - apiGroups:
      - apiextensions.k8s.io
    resources:
      - customresourcedefinitions
    verbs:
      - "*"
  - apiGroups:
      - apps
    resources:
      - deployments/scale
    verbs:
      - get
      - list
      - update
      - watch
      - delete
      - create
      - patch
  - apiGroups:
      - authentication.istio.io
    resources:
      - "*"
    verbs:
      - "*"
  - apiGroups:
      - authentication.k8s.io
    resources:
      - "*"
    verbs:
      - "*"
  - apiGroups:
      - authorization.k8s.io
    resources:
      - "*"
    verbs:
      - "*"
  - apiGroups:
      - certificates.k8s.io
    resources:
      - certificatesigningrequests
    verbs:
      - approve
      - update
      - create
      - get
      - delete
  - apiGroups:
      - certificates.k8s.io
    resources:
      - certificatesigningrequests/approval
    verbs:
      - approve
      - update
      - create
      - get
      - delete
      - watch
  - apiGroups:
      - certificates.k8s.io
    resources:
      - certificatesigningrequests/status
    verbs:
      - approve
      - update
      - create
      - get
      - delete
      - watch
  - apiGroups:
      - certificates.k8s.io
    resources:
      - signers
    verbs:
      - approve
      - update
      - create
      - get
      - delete
      - watch
  - apiGroups:
      - config.istio.io
    resources:
      - "*"
    verbs:
      - "*"
  - apiGroups:
      - discovery.k8s.io
    resources:
      - endpointslices
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - extensions
    resources:
      - deployments/finalizers
    resourceNames:
      - istio-galley
    verbs:
      - update
  - apiGroups:
      - extensions
    resources:
      - deployments/scale
    verbs:
      - get
      - list
      - watch
      - update
      - delete
  - apiGroups:
      - extensions
    resources:
      - ingresses
    verbs:
      - "*"
  - apiGroups:
      - extensions
    resources:
      - ingresses/status
    verbs:
      - "*"
  - apiGroups:
      - extensions
    resources:
      - podsecuritypolicies
    resourceNames:
      - istio
    verbs:
      - use
  - apiGroups:
      - extensions.istio.io
    resources:
      - "*"
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - gateway.networking.k8s.io
    resources:
      - "*"
    verbs:
      - get
      - watch
      - list
      - update
      - patch
  - apiGroups:
      - gateway.networking.k8s.io
    resources:
      - gatewayclasses
    verbs:
      - create
      - delete
  - apiGroups:
      - monitoring.kiali.io
    resources:
      - monitoringdashboards
    verbs:
      - get
      - list
  - apiGroups:
      - multicluster.x-k8s.io
    resources:
      - serviceexports
    verbs:
      - get
      - list
      - watch
      - create
      - delete
  - apiGroups:
      - multicluster.x-k8s.io
    resources:
      - serviceimports
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - networking.istio.io
    resources:
      - "*"
    verbs:
      - "*"
  - apiGroups:
      - networking.k8s.io
    resources:
      - "*"
    verbs:
      - "*"
  - apiGroups:
      - networking.x-k8s.io
    resources:
      - "*"
    verbs:
      - "*"
  - apiGroups:
      - networking.x.k8s.io
    resources:
      - "*"
    verbs:
      - get
      - watch
      - list
  - apiGroups:
      - rbac.authorization.k8s.io
    resources:
      - clusterrolebindings
    verbs:
      - watch
  - apiGroups:
      - rbac.authorization.k8s.io
    resources:
      - clusterroles
    verbs:
      - watch
  - apiGroups:
      - rbac.authorization.k8s.io
    resources:
      - rolebindings
    verbs:
      - watch
  - apiGroups:
      - rbac.authorization.k8s.io
    resources:
      - roles
    verbs:
      - watch
  - apiGroups:
      - rbac.istio.io
    resources:
      - "*"
    verbs:
      - get
      - list
      - watch
      - create
      - delete
      - patch
  - apiGroups:
      - rbac.istio.io
    resources:
      - "*/status"
    verbs:
      - update
  - apiGroups:
      - security.istio.io
    resources:
      - "*"
    verbs:
      - get
      - watch
      - list
      - create
      - delete
      - patch
      - update
  - apiGroups:
      - security.istio.io
    resources:
      - "*/status"
    verbs:
      - update
  - apiGroups:
      - telemetry.istio.io
    resources:
      - "*"
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - ''
    resources:
      - events
    verbs:
      - delete
      - create
      - update
      - patch
  - apiGroups:
      - ''
    resources:
      - namespaces/finalizers
    verbs:
      - update
  - apiGroups:
      - ''
    resources:
      - cronjobs
    verbs:
      - list
  - apiGroups:
      - ''
    resources:
      - jobs
    verbs:
      - list
  - apiGroups:
      - apps
    resources:
      - deployments/finalizers
    resourceNames:
      - tmcomponent
    verbs:
      - update
  - apiGroups:
      - secrets-store.csi.x-k8s.io
    resources:
      - secretproviderclasses
    verbs:
      - get
      - create
      - patch
      - list
      - watch
      - update
      - delete
  - apiGroups:
      - ""
    resources:
      - pods/portforward
    verbs:
      - create
      - update
      - delete
  - apiGroups:
      - k8s.cni.cncf.io
    resources:
      - network-attachment-definitions
    verbs:
      - get
      - update
      - patch
      - create
      - delete
      - watch
      - list
---
# Source: tm-installer/templates/vault-installer-cluster-role-binding.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: tm-installer
subjects:
  - kind: ServiceAccount
    # service account name. should be the same service account which the tm-installer HashiCorp Vault role
    # is set to for successful tm-installer pod and HashiCorp Vault authentication
    name: 'tm-installer'
    # the namespace where the Vault Installer pod is deployed to
    namespace: 'tm-system'
roleRef:
  kind: ClusterRole
  # Allows read/write access to most resources in a namespace, including the ability
  # to create roles and rolebindings within the namespace. It does not allow write
  # access to resource quota or to the namespace itself.
  name: tm-installer
  apiGroup: rbac.authorization.k8s.io
---
# Source: tm-installer/templates/tm-installer-replica-set.yaml
apiVersion: apps/v1
kind: ReplicaSet
metadata:
  name: tm-installer
  annotations:
    sidecar.istio.io/inject: "false"
  namespace: tm-system
spec:
  replicas: 0
  selector:
    matchLabels:
      app: tm-installer
  template:
    metadata:
      annotations:
        sidecar.istio.io/inject: "false"
      labels:
        sidecar.istio.io/inject: "false"
        app: tm-installer
    spec:
      containers:
        - image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/tm/installer:4.6.14-manual
          command:
            - sleep
            - "10d"
          name: tm-installer
          resources:
            requests:
              cpu: 100m
              memory: 256Mi
            limits:
              cpu: 100m
              memory: 256Mi
          env:
            - name: TM_ENV
              value: prd
            - name: AWS_S3_ROLE
              value: arn:aws:iam::************:role/dbmy-prd-tm-s3-installer-role
            - name: S3_ARTEFACT_PATH
              value: dbmy-prd-tm-installer-artefact-s3/prd/4.6.26
            - name: TM_VERSION
              value: 4.6.26
            - name: ACTION
              value: configure_values
            - name: DRYRUN
              value: "false"
            - name: UPDATED_AT
              value: "2025-02-05T17:29:07+00:00"
            - name: TM_DEPLOY_TOKEN
              value: f26332b-********
            - name: TM_APPROVAL_TOKEN
              value: approval-approved-f26332b-********
            - name: TM_APPROVAL_READY_TOKEN
              value: approval-ready-f26332b-********
            - name: TM_REGION_SHORT
              value: ""
      restartPolicy: Always
      serviceAccountName: tm-installer
---
# Source: tm-installer/templates/tm-installer-job.yaml
apiVersion: batch/v1
kind: Job
metadata:
  name: tm-installer
  annotations:
    sidecar.istio.io/inject: "false"
    argocd.argoproj.io/hook: PostSync
    argocd.argoproj.io/hook-delete-policy: HookSucceeded
  namespace: tm-system
  labels:
    app: tm-installer
    app.kubernetes.io/name: tm-installer
    app.kubernetes.io/instance: tm-installer
    tags.datadoghq.com/service: tm-installer
spec:
  template:
    metadata:
      annotations:
        sidecar.istio.io/inject: "false"
        apm.datadoghq.com/env: '{ "DD_SERVICE": "tm-installer" }'
      labels:
        app: tm-installer
        app.kubernetes.io/name: tm-installer
        app.kubernetes.io/instance: tm-installer
        tags.datadoghq.com/service: tm-installer
    spec:
      containers:
        - image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/tm/installer:4.6.14-manual
          command:
            - sh
            - "/config_files/install.sh"
          name: tm-installer
          resources:
            requests:
              cpu: 100m
              memory: 256Mi
            limits:
              cpu: 100m
              memory: 256Mi
          env:
            - name: TM_ENV
              value: prd
            - name: AWS_S3_ROLE
              value: arn:aws:iam::************:role/dbmy-prd-tm-s3-installer-role
            - name: S3_ARTEFACT_PATH
              value: dbmy-prd-tm-installer-artefact-s3/prd/4.6.26
            - name: TM_VERSION
              value: 4.6.26
            - name: ACTION
              value: configure_values
            - name: DRYRUN
              value: "false"
            - name: UPDATED_AT
              value: "2025-02-05T17:29:07+00:00"
            - name: TM_DEPLOY_TOKEN
              value: f26332b-********
            - name: TM_APPROVAL_TOKEN
              value: approval-approved-f26332b-********
            - name: TM_APPROVAL_READY_TOKEN
              value: approval-ready-f26332b-********
            - name: TM_REGION_SHORT
              value: ""
            - name: ENV
              value: prd
          volumeMounts:
            - mountPath: /config_files
              name: config-volume
      restartPolicy: Never
      serviceAccountName: tm-installer
      volumes:
        - name: config-volume
          configMap:
            name: tm-installer
            defaultMode: 0755
---
# Source: tm-installer/templates/cron-scheduled-redis-restarter.yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  labels:
    app.kubernetes.io/instance: scheduled-redis-restarter
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: scheduled-redis-restarter
    tags.datadoghq.com/env: prd
    tags.datadoghq.com/service: scheduled-redis-restarter
  name: scheduled-redis-restarter
spec:
  concurrencyPolicy: Forbid
  jobTemplate:
    metadata:
      labels:
        app.kubernetes.io/instance: scheduled-redis-restarter
        app.kubernetes.io/managed-by: Helm
        app.kubernetes.io/name: scheduled-redis-restarter
        tags.datadoghq.com/env: prd
        tags.datadoghq.com/service: scheduled-redis-restarter
    spec:
      template:
        metadata:
          labels:
            app.kubernetes.io/instance: scheduled-redis-restarter
            app.kubernetes.io/managed-by: Helm
            app.kubernetes.io/name: scheduled-redis-restarter
            sidecar.istio.io/inject: "false"
            tags.datadoghq.com/env: prd
            tags.datadoghq.com/service: scheduled-redis-restarter
        spec:
          containers:
            - command:
                - sh
                - -c
                - ENABLED=true sh /config-files/run-restart.sh 0.80
              image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/tm/installer:4.6.14-manual
              name: scheduled-redis-restarter
              volumeMounts:
                - mountPath: /config-files
                  name: restarter-config
          restartPolicy: OnFailure
          serviceAccountName: tm-installer
          volumes:
            - name: restarter-config
              configMap:
                name: tm-scheduled-redis-restarter
                defaultMode: 0755
  schedule: 30 18 * * *
---
# Source: tm-installer/templates/cron-scheduled-scaler.yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  labels:
    app.kubernetes.io/instance: contract-engine-hpa-out
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: contract-engine-hpa-out
    tags.datadoghq.com/env: prd
    tags.datadoghq.com/service: contract-engine-hpa-out
  name: contract-engine-hpa-out
spec:
  concurrencyPolicy: Forbid
  jobTemplate:
    metadata:
      labels:
        app.kubernetes.io/instance: contract-engine-hpa-out
        app.kubernetes.io/managed-by: Helm
        app.kubernetes.io/name: contract-engine-hpa-out
        tags.datadoghq.com/env: prd
        tags.datadoghq.com/service: contract-engine-hpa-out
    spec:
      template:
        metadata:
          labels:
            app.kubernetes.io/instance: contract-engine-hpa-out
            app.kubernetes.io/managed-by: Helm
            app.kubernetes.io/name: contract-engine-hpa-out
            sidecar.istio.io/inject: "false"
            tags.datadoghq.com/env: prd
            tags.datadoghq.com/service: contract-engine-hpa-out
        spec:
          containers:
            - command:
                - sh
                - -c
                - ENABLED=true HPA=contract-engine-hpa MIN_REPLICAS=18 sh /config-files/run-scaling.sh
              image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/tm/installer:4.6.14-manual
              name: scheduled-scaler
              volumeMounts:
                - mountPath: /config-files
                  name: scaling-config
          restartPolicy: OnFailure
          serviceAccountName: tm-installer
          volumes:
            - name: scaling-config
              configMap:
                name: tm-scheduled-scaler
                defaultMode: 0755
  schedule: 36 15 * * *
---
# Source: tm-installer/templates/cron-scheduled-scaler.yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  labels:
    app.kubernetes.io/instance: contract-engine-hpa-in
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: contract-engine-hpa-in
    tags.datadoghq.com/env: prd
    tags.datadoghq.com/service: contract-engine-hpa-in
  name: contract-engine-hpa-in
spec:
  concurrencyPolicy: Forbid
  jobTemplate:
    metadata:
      labels:
        app.kubernetes.io/instance: contract-engine-hpa-in
        app.kubernetes.io/managed-by: Helm
        app.kubernetes.io/name: contract-engine-hpa-in
        tags.datadoghq.com/env: prd
        tags.datadoghq.com/service: contract-engine-hpa-in
    spec:
      template:
        metadata:
          labels:
            app.kubernetes.io/instance: contract-engine-hpa-in
            app.kubernetes.io/managed-by: Helm
            app.kubernetes.io/name: contract-engine-hpa-in
            sidecar.istio.io/inject: "false"
            tags.datadoghq.com/env: prd
            tags.datadoghq.com/service: contract-engine-hpa-in
        spec:
          containers:
            - command:
                - sh
                - -c
                - ENABLED=true HPA=contract-engine-hpa MIN_REPLICAS=4 sh /config-files/run-scaling.sh
              image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/tm/installer:4.6.14-manual
              name: scheduled-scaler
              volumeMounts:
                - mountPath: /config-files
                  name: scaling-config
          restartPolicy: OnFailure
          serviceAccountName: tm-installer
          volumes:
            - name: scaling-config
              configMap:
                name: tm-scheduled-scaler
                defaultMode: 0755
  schedule: 30 16 * * *
---
# Source: tm-installer/templates/cron-scheduled-scaler.yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  labels:
    app.kubernetes.io/instance: contract-post-balance-update-processor-hpa-out
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: contract-post-balance-update-processor-hpa-out
    tags.datadoghq.com/env: prd
    tags.datadoghq.com/service: contract-post-balance-update-processor-hpa-out
  name: contract-post-balance-update-processor-hpa-out
spec:
  concurrencyPolicy: Forbid
  jobTemplate:
    metadata:
      labels:
        app.kubernetes.io/instance: contract-post-balance-update-processor-hpa-out
        app.kubernetes.io/managed-by: Helm
        app.kubernetes.io/name: contract-post-balance-update-processor-hpa-out
        tags.datadoghq.com/env: prd
        tags.datadoghq.com/service: contract-post-balance-update-processor-hpa-out
    spec:
      template:
        metadata:
          labels:
            app.kubernetes.io/instance: contract-post-balance-update-processor-hpa-out
            app.kubernetes.io/managed-by: Helm
            app.kubernetes.io/name: contract-post-balance-update-processor-hpa-out
            sidecar.istio.io/inject: "false"
            tags.datadoghq.com/env: prd
            tags.datadoghq.com/service: contract-post-balance-update-processor-hpa-out
        spec:
          containers:
            - command:
                - sh
                - -c
                - ENABLED=true HPA=contract-post-balance-update-processor-hpa MIN_REPLICAS=10 sh /config-files/run-scaling.sh
              image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/tm/installer:4.6.14-manual
              name: scheduled-scaler
              volumeMounts:
                - mountPath: /config-files
                  name: scaling-config
          restartPolicy: OnFailure
          serviceAccountName: tm-installer
          volumes:
            - name: scaling-config
              configMap:
                name: tm-scheduled-scaler
                defaultMode: 0755
  schedule: 38 15 * * *
---
# Source: tm-installer/templates/cron-scheduled-scaler.yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  labels:
    app.kubernetes.io/instance: contract-post-balance-update-processor-hpa-in
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: contract-post-balance-update-processor-hpa-in
    tags.datadoghq.com/env: prd
    tags.datadoghq.com/service: contract-post-balance-update-processor-hpa-in
  name: contract-post-balance-update-processor-hpa-in
spec:
  concurrencyPolicy: Forbid
  jobTemplate:
    metadata:
      labels:
        app.kubernetes.io/instance: contract-post-balance-update-processor-hpa-in
        app.kubernetes.io/managed-by: Helm
        app.kubernetes.io/name: contract-post-balance-update-processor-hpa-in
        tags.datadoghq.com/env: prd
        tags.datadoghq.com/service: contract-post-balance-update-processor-hpa-in
    spec:
      template:
        metadata:
          labels:
            app.kubernetes.io/instance: contract-post-balance-update-processor-hpa-in
            app.kubernetes.io/managed-by: Helm
            app.kubernetes.io/name: contract-post-balance-update-processor-hpa-in
            sidecar.istio.io/inject: "false"
            tags.datadoghq.com/env: prd
            tags.datadoghq.com/service: contract-post-balance-update-processor-hpa-in
        spec:
          containers:
            - command:
                - sh
                - -c
                - ENABLED=true HPA=contract-post-balance-update-processor-hpa MIN_REPLICAS=2 sh /config-files/run-scaling.sh
              image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/tm/installer:4.6.14-manual
              name: scheduled-scaler
              volumeMounts:
                - mountPath: /config-files
                  name: scaling-config
          restartPolicy: OnFailure
          serviceAccountName: tm-installer
          volumes:
            - name: scaling-config
              configMap:
                name: tm-scheduled-scaler
                defaultMode: 0755
  schedule: 30 16 * * *
---
# Source: tm-installer/templates/cron-scheduled-scaler.yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  labels:
    app.kubernetes.io/instance: contract-post-posting-processor-hpa-out
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: contract-post-posting-processor-hpa-out
    tags.datadoghq.com/env: prd
    tags.datadoghq.com/service: contract-post-posting-processor-hpa-out
  name: contract-post-posting-processor-hpa-out
spec:
  concurrencyPolicy: Forbid
  jobTemplate:
    metadata:
      labels:
        app.kubernetes.io/instance: contract-post-posting-processor-hpa-out
        app.kubernetes.io/managed-by: Helm
        app.kubernetes.io/name: contract-post-posting-processor-hpa-out
        tags.datadoghq.com/env: prd
        tags.datadoghq.com/service: contract-post-posting-processor-hpa-out
    spec:
      template:
        metadata:
          labels:
            app.kubernetes.io/instance: contract-post-posting-processor-hpa-out
            app.kubernetes.io/managed-by: Helm
            app.kubernetes.io/name: contract-post-posting-processor-hpa-out
            sidecar.istio.io/inject: "false"
            tags.datadoghq.com/env: prd
            tags.datadoghq.com/service: contract-post-posting-processor-hpa-out
        spec:
          containers:
            - command:
                - sh
                - -c
                - ENABLED=true HPA=contract-post-posting-processor-hpa MIN_REPLICAS=8 sh /config-files/run-scaling.sh
              image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/tm/installer:4.6.14-manual
              name: scheduled-scaler
              volumeMounts:
                - mountPath: /config-files
                  name: scaling-config
          restartPolicy: OnFailure
          serviceAccountName: tm-installer
          volumes:
            - name: scaling-config
              configMap:
                name: tm-scheduled-scaler
                defaultMode: 0755
  schedule: 37 15 * * *
---
# Source: tm-installer/templates/cron-scheduled-scaler.yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  labels:
    app.kubernetes.io/instance: contract-post-posting-processor-hpa-in
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: contract-post-posting-processor-hpa-in
    tags.datadoghq.com/env: prd
    tags.datadoghq.com/service: contract-post-posting-processor-hpa-in
  name: contract-post-posting-processor-hpa-in
spec:
  concurrencyPolicy: Forbid
  jobTemplate:
    metadata:
      labels:
        app.kubernetes.io/instance: contract-post-posting-processor-hpa-in
        app.kubernetes.io/managed-by: Helm
        app.kubernetes.io/name: contract-post-posting-processor-hpa-in
        tags.datadoghq.com/env: prd
        tags.datadoghq.com/service: contract-post-posting-processor-hpa-in
    spec:
      template:
        metadata:
          labels:
            app.kubernetes.io/instance: contract-post-posting-processor-hpa-in
            app.kubernetes.io/managed-by: Helm
            app.kubernetes.io/name: contract-post-posting-processor-hpa-in
            sidecar.istio.io/inject: "false"
            tags.datadoghq.com/env: prd
            tags.datadoghq.com/service: contract-post-posting-processor-hpa-in
        spec:
          containers:
            - command:
                - sh
                - -c
                - ENABLED=true HPA=contract-post-posting-processor-hpa MIN_REPLICAS=2 sh /config-files/run-scaling.sh
              image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/tm/installer:4.6.14-manual
              name: scheduled-scaler
              volumeMounts:
                - mountPath: /config-files
                  name: scaling-config
          restartPolicy: OnFailure
          serviceAccountName: tm-installer
          volumes:
            - name: scaling-config
              configMap:
                name: tm-scheduled-scaler
                defaultMode: 0755
  schedule: 30 16 * * *
---
# Source: tm-installer/templates/cron-scheduled-scaler.yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  labels:
    app.kubernetes.io/instance: kernel-balance-hpa-out
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: kernel-balance-hpa-out
    tags.datadoghq.com/env: prd
    tags.datadoghq.com/service: kernel-balance-hpa-out
  name: kernel-balance-hpa-out
spec:
  concurrencyPolicy: Forbid
  jobTemplate:
    metadata:
      labels:
        app.kubernetes.io/instance: kernel-balance-hpa-out
        app.kubernetes.io/managed-by: Helm
        app.kubernetes.io/name: kernel-balance-hpa-out
        tags.datadoghq.com/env: prd
        tags.datadoghq.com/service: kernel-balance-hpa-out
    spec:
      template:
        metadata:
          labels:
            app.kubernetes.io/instance: kernel-balance-hpa-out
            app.kubernetes.io/managed-by: Helm
            app.kubernetes.io/name: kernel-balance-hpa-out
            sidecar.istio.io/inject: "false"
            tags.datadoghq.com/env: prd
            tags.datadoghq.com/service: kernel-balance-hpa-out
        spec:
          containers:
            - command:
                - sh
                - -c
                - ENABLED=true HPA=kernel-balance-hpa MIN_REPLICAS=10 sh /config-files/run-scaling.sh
              image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/tm/installer:4.6.14-manual
              name: scheduled-scaler
              volumeMounts:
                - mountPath: /config-files
                  name: scaling-config
          restartPolicy: OnFailure
          serviceAccountName: tm-installer
          volumes:
            - name: scaling-config
              configMap:
                name: tm-scheduled-scaler
                defaultMode: 0755
  schedule: 32 15 * * *
---
# Source: tm-installer/templates/cron-scheduled-scaler.yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  labels:
    app.kubernetes.io/instance: kernel-balance-hpa-in
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: kernel-balance-hpa-in
    tags.datadoghq.com/env: prd
    tags.datadoghq.com/service: kernel-balance-hpa-in
  name: kernel-balance-hpa-in
spec:
  concurrencyPolicy: Forbid
  jobTemplate:
    metadata:
      labels:
        app.kubernetes.io/instance: kernel-balance-hpa-in
        app.kubernetes.io/managed-by: Helm
        app.kubernetes.io/name: kernel-balance-hpa-in
        tags.datadoghq.com/env: prd
        tags.datadoghq.com/service: kernel-balance-hpa-in
    spec:
      template:
        metadata:
          labels:
            app.kubernetes.io/instance: kernel-balance-hpa-in
            app.kubernetes.io/managed-by: Helm
            app.kubernetes.io/name: kernel-balance-hpa-in
            sidecar.istio.io/inject: "false"
            tags.datadoghq.com/env: prd
            tags.datadoghq.com/service: kernel-balance-hpa-in
        spec:
          containers:
            - command:
                - sh
                - -c
                - ENABLED=true HPA=kernel-balance-hpa MIN_REPLICAS=4 sh /config-files/run-scaling.sh
              image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/tm/installer:4.6.14-manual
              name: scheduled-scaler
              volumeMounts:
                - mountPath: /config-files
                  name: scaling-config
          restartPolicy: OnFailure
          serviceAccountName: tm-installer
          volumes:
            - name: scaling-config
              configMap:
                name: tm-scheduled-scaler
                defaultMode: 0755
  schedule: 30 16 * * *
---
# Source: tm-installer/templates/cron-scheduled-scaler.yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  labels:
    app.kubernetes.io/instance: supervisor-contracts-hpa-out
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: supervisor-contracts-hpa-out
    tags.datadoghq.com/env: prd
    tags.datadoghq.com/service: supervisor-contracts-hpa-out
  name: supervisor-contracts-hpa-out
spec:
  concurrencyPolicy: Forbid
  jobTemplate:
    metadata:
      labels:
        app.kubernetes.io/instance: supervisor-contracts-hpa-out
        app.kubernetes.io/managed-by: Helm
        app.kubernetes.io/name: supervisor-contracts-hpa-out
        tags.datadoghq.com/env: prd
        tags.datadoghq.com/service: supervisor-contracts-hpa-out
    spec:
      template:
        metadata:
          labels:
            app.kubernetes.io/instance: supervisor-contracts-hpa-out
            app.kubernetes.io/managed-by: Helm
            app.kubernetes.io/name: supervisor-contracts-hpa-out
            sidecar.istio.io/inject: "false"
            tags.datadoghq.com/env: prd
            tags.datadoghq.com/service: supervisor-contracts-hpa-out
        spec:
          containers:
            - command:
                - sh
                - -c
                - ENABLED=true HPA=supervisor-contracts-hpa MIN_REPLICAS=10 sh /config-files/run-scaling.sh
              image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/tm/installer:4.6.14-manual
              name: scheduled-scaler
              volumeMounts:
                - mountPath: /config-files
                  name: scaling-config
          restartPolicy: OnFailure
          serviceAccountName: tm-installer
          volumes:
            - name: scaling-config
              configMap:
                name: tm-scheduled-scaler
                defaultMode: 0755
  schedule: 31 15 * * *
---
# Source: tm-installer/templates/cron-scheduled-scaler.yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  labels:
    app.kubernetes.io/instance: supervisor-contracts-hpa-in
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: supervisor-contracts-hpa-in
    tags.datadoghq.com/env: prd
    tags.datadoghq.com/service: supervisor-contracts-hpa-in
  name: supervisor-contracts-hpa-in
spec:
  concurrencyPolicy: Forbid
  jobTemplate:
    metadata:
      labels:
        app.kubernetes.io/instance: supervisor-contracts-hpa-in
        app.kubernetes.io/managed-by: Helm
        app.kubernetes.io/name: supervisor-contracts-hpa-in
        tags.datadoghq.com/env: prd
        tags.datadoghq.com/service: supervisor-contracts-hpa-in
    spec:
      template:
        metadata:
          labels:
            app.kubernetes.io/instance: supervisor-contracts-hpa-in
            app.kubernetes.io/managed-by: Helm
            app.kubernetes.io/name: supervisor-contracts-hpa-in
            sidecar.istio.io/inject: "false"
            tags.datadoghq.com/env: prd
            tags.datadoghq.com/service: supervisor-contracts-hpa-in
        spec:
          containers:
            - command:
                - sh
                - -c
                - ENABLED=true HPA=supervisor-contracts-hpa MIN_REPLICAS=2 sh /config-files/run-scaling.sh
              image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/tm/installer:4.6.14-manual
              name: scheduled-scaler
              volumeMounts:
                - mountPath: /config-files
                  name: scaling-config
          restartPolicy: OnFailure
          serviceAccountName: tm-installer
          volumes:
            - name: scaling-config
              configMap:
                name: tm-scheduled-scaler
                defaultMode: 0755
  schedule: 30 16 * * *
---
# Source: tm-installer/templates/cron-scheduled-scaler.yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  labels:
    app.kubernetes.io/instance: vault-account-hpa-out
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: vault-account-hpa-out
    tags.datadoghq.com/env: prd
    tags.datadoghq.com/service: vault-account-hpa-out
  name: vault-account-hpa-out
spec:
  concurrencyPolicy: Forbid
  jobTemplate:
    metadata:
      labels:
        app.kubernetes.io/instance: vault-account-hpa-out
        app.kubernetes.io/managed-by: Helm
        app.kubernetes.io/name: vault-account-hpa-out
        tags.datadoghq.com/env: prd
        tags.datadoghq.com/service: vault-account-hpa-out
    spec:
      template:
        metadata:
          labels:
            app.kubernetes.io/instance: vault-account-hpa-out
            app.kubernetes.io/managed-by: Helm
            app.kubernetes.io/name: vault-account-hpa-out
            sidecar.istio.io/inject: "false"
            tags.datadoghq.com/env: prd
            tags.datadoghq.com/service: vault-account-hpa-out
        spec:
          containers:
            - command:
                - sh
                - -c
                - ENABLED=true HPA=vault-account-hpa MIN_REPLICAS=25 sh /config-files/run-scaling.sh
              image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/tm/installer:4.6.14-manual
              name: scheduled-scaler
              volumeMounts:
                - mountPath: /config-files
                  name: scaling-config
          restartPolicy: OnFailure
          serviceAccountName: tm-installer
          volumes:
            - name: scaling-config
              configMap:
                name: tm-scheduled-scaler
                defaultMode: 0755
  schedule: 30 15 * * *
---
# Source: tm-installer/templates/cron-scheduled-scaler.yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  labels:
    app.kubernetes.io/instance: vault-account-hpa-in
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: vault-account-hpa-in
    tags.datadoghq.com/env: prd
    tags.datadoghq.com/service: vault-account-hpa-in
  name: vault-account-hpa-in
spec:
  concurrencyPolicy: Forbid
  jobTemplate:
    metadata:
      labels:
        app.kubernetes.io/instance: vault-account-hpa-in
        app.kubernetes.io/managed-by: Helm
        app.kubernetes.io/name: vault-account-hpa-in
        tags.datadoghq.com/env: prd
        tags.datadoghq.com/service: vault-account-hpa-in
    spec:
      template:
        metadata:
          labels:
            app.kubernetes.io/instance: vault-account-hpa-in
            app.kubernetes.io/managed-by: Helm
            app.kubernetes.io/name: vault-account-hpa-in
            sidecar.istio.io/inject: "false"
            tags.datadoghq.com/env: prd
            tags.datadoghq.com/service: vault-account-hpa-in
        spec:
          containers:
            - command:
                - sh
                - -c
                - ENABLED=true HPA=vault-account-hpa MIN_REPLICAS=7 sh /config-files/run-scaling.sh
              image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/tm/installer:4.6.14-manual
              name: scheduled-scaler
              volumeMounts:
                - mountPath: /config-files
                  name: scaling-config
          restartPolicy: OnFailure
          serviceAccountName: tm-installer
          volumes:
            - name: scaling-config
              configMap:
                name: tm-scheduled-scaler
                defaultMode: 0755
  schedule: 30 16 * * *
---
# Source: tm-installer/templates/cron-scheduled-scaler.yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  labels:
    app.kubernetes.io/instance: vault-posting-hpa-out
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: vault-posting-hpa-out
    tags.datadoghq.com/env: prd
    tags.datadoghq.com/service: vault-posting-hpa-out
  name: vault-posting-hpa-out
spec:
  concurrencyPolicy: Forbid
  jobTemplate:
    metadata:
      labels:
        app.kubernetes.io/instance: vault-posting-hpa-out
        app.kubernetes.io/managed-by: Helm
        app.kubernetes.io/name: vault-posting-hpa-out
        tags.datadoghq.com/env: prd
        tags.datadoghq.com/service: vault-posting-hpa-out
    spec:
      template:
        metadata:
          labels:
            app.kubernetes.io/instance: vault-posting-hpa-out
            app.kubernetes.io/managed-by: Helm
            app.kubernetes.io/name: vault-posting-hpa-out
            sidecar.istio.io/inject: "false"
            tags.datadoghq.com/env: prd
            tags.datadoghq.com/service: vault-posting-hpa-out
        spec:
          containers:
            - command:
                - sh
                - -c
                - ENABLED=true HPA=vault-posting-hpa MIN_REPLICAS=10 sh /config-files/run-scaling.sh
              image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/tm/installer:4.6.14-manual
              name: scheduled-scaler
              volumeMounts:
                - mountPath: /config-files
                  name: scaling-config
          restartPolicy: OnFailure
          serviceAccountName: tm-installer
          volumes:
            - name: scaling-config
              configMap:
                name: tm-scheduled-scaler
                defaultMode: 0755
  schedule: 33 15 * * *
---
# Source: tm-installer/templates/cron-scheduled-scaler.yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  labels:
    app.kubernetes.io/instance: vault-posting-hpa-in
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: vault-posting-hpa-in
    tags.datadoghq.com/env: prd
    tags.datadoghq.com/service: vault-posting-hpa-in
  name: vault-posting-hpa-in
spec:
  concurrencyPolicy: Forbid
  jobTemplate:
    metadata:
      labels:
        app.kubernetes.io/instance: vault-posting-hpa-in
        app.kubernetes.io/managed-by: Helm
        app.kubernetes.io/name: vault-posting-hpa-in
        tags.datadoghq.com/env: prd
        tags.datadoghq.com/service: vault-posting-hpa-in
    spec:
      template:
        metadata:
          labels:
            app.kubernetes.io/instance: vault-posting-hpa-in
            app.kubernetes.io/managed-by: Helm
            app.kubernetes.io/name: vault-posting-hpa-in
            sidecar.istio.io/inject: "false"
            tags.datadoghq.com/env: prd
            tags.datadoghq.com/service: vault-posting-hpa-in
        spec:
          containers:
            - command:
                - sh
                - -c
                - ENABLED=true HPA=vault-posting-hpa MIN_REPLICAS=4 sh /config-files/run-scaling.sh
              image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/tm/installer:4.6.14-manual
              name: scheduled-scaler
              volumeMounts:
                - mountPath: /config-files
                  name: scaling-config
          restartPolicy: OnFailure
          serviceAccountName: tm-installer
          volumes:
            - name: scaling-config
              configMap:
                name: tm-scheduled-scaler
                defaultMode: 0755
  schedule: 30 16 * * *
---
# Source: tm-installer/templates/cron-scheduled-scaler.yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  labels:
    app.kubernetes.io/instance: vault-postings-account-postings-processor-hpa-out
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: vault-postings-account-postings-processor-hpa-out
    tags.datadoghq.com/env: prd
    tags.datadoghq.com/service: vault-postings-account-postings-processor-hpa-out
  name: vault-postings-account-postings-processor-hpa-out
spec:
  concurrencyPolicy: Forbid
  jobTemplate:
    metadata:
      labels:
        app.kubernetes.io/instance: vault-postings-account-postings-processor-hpa-out
        app.kubernetes.io/managed-by: Helm
        app.kubernetes.io/name: vault-postings-account-postings-processor-hpa-out
        tags.datadoghq.com/env: prd
        tags.datadoghq.com/service: vault-postings-account-postings-processor-hpa-out
    spec:
      template:
        metadata:
          labels:
            app.kubernetes.io/instance: vault-postings-account-postings-processor-hpa-out
            app.kubernetes.io/managed-by: Helm
            app.kubernetes.io/name: vault-postings-account-postings-processor-hpa-out
            sidecar.istio.io/inject: "false"
            tags.datadoghq.com/env: prd
            tags.datadoghq.com/service: vault-postings-account-postings-processor-hpa-out
        spec:
          containers:
            - command:
                - sh
                - -c
                - ENABLED=true HPA=vault-postings-account-postings-processor-hpa MIN_REPLICAS=10 sh /config-files/run-scaling.sh
              image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/tm/installer:4.6.14-manual
              name: scheduled-scaler
              volumeMounts:
                - mountPath: /config-files
                  name: scaling-config
          restartPolicy: OnFailure
          serviceAccountName: tm-installer
          volumes:
            - name: scaling-config
              configMap:
                name: tm-scheduled-scaler
                defaultMode: 0755
  schedule: 34 15 * * *
---
# Source: tm-installer/templates/cron-scheduled-scaler.yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  labels:
    app.kubernetes.io/instance: vault-postings-account-postings-processor-hpa-in
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: vault-postings-account-postings-processor-hpa-in
    tags.datadoghq.com/env: prd
    tags.datadoghq.com/service: vault-postings-account-postings-processor-hpa-in
  name: vault-postings-account-postings-processor-hpa-in
spec:
  concurrencyPolicy: Forbid
  jobTemplate:
    metadata:
      labels:
        app.kubernetes.io/instance: vault-postings-account-postings-processor-hpa-in
        app.kubernetes.io/managed-by: Helm
        app.kubernetes.io/name: vault-postings-account-postings-processor-hpa-in
        tags.datadoghq.com/env: prd
        tags.datadoghq.com/service: vault-postings-account-postings-processor-hpa-in
    spec:
      template:
        metadata:
          labels:
            app.kubernetes.io/instance: vault-postings-account-postings-processor-hpa-in
            app.kubernetes.io/managed-by: Helm
            app.kubernetes.io/name: vault-postings-account-postings-processor-hpa-in
            sidecar.istio.io/inject: "false"
            tags.datadoghq.com/env: prd
            tags.datadoghq.com/service: vault-postings-account-postings-processor-hpa-in
        spec:
          containers:
            - command:
                - sh
                - -c
                - ENABLED=true HPA=vault-postings-account-postings-processor-hpa MIN_REPLICAS=2 sh /config-files/run-scaling.sh
              image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/tm/installer:4.6.14-manual
              name: scheduled-scaler
              volumeMounts:
                - mountPath: /config-files
                  name: scaling-config
          restartPolicy: OnFailure
          serviceAccountName: tm-installer
          volumes:
            - name: scaling-config
              configMap:
                name: tm-scheduled-scaler
                defaultMode: 0755
  schedule: 30 16 * * *
---
# Source: tm-installer/templates/cron-scheduled-scaler.yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  labels:
    app.kubernetes.io/instance: vault-postings-processor-internal-hpa-out
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: vault-postings-processor-internal-hpa-out
    tags.datadoghq.com/env: prd
    tags.datadoghq.com/service: vault-postings-processor-internal-hpa-out
  name: vault-postings-processor-internal-hpa-out
spec:
  concurrencyPolicy: Forbid
  jobTemplate:
    metadata:
      labels:
        app.kubernetes.io/instance: vault-postings-processor-internal-hpa-out
        app.kubernetes.io/managed-by: Helm
        app.kubernetes.io/name: vault-postings-processor-internal-hpa-out
        tags.datadoghq.com/env: prd
        tags.datadoghq.com/service: vault-postings-processor-internal-hpa-out
    spec:
      template:
        metadata:
          labels:
            app.kubernetes.io/instance: vault-postings-processor-internal-hpa-out
            app.kubernetes.io/managed-by: Helm
            app.kubernetes.io/name: vault-postings-processor-internal-hpa-out
            sidecar.istio.io/inject: "false"
            tags.datadoghq.com/env: prd
            tags.datadoghq.com/service: vault-postings-processor-internal-hpa-out
        spec:
          containers:
            - command:
                - sh
                - -c
                - ENABLED=true HPA=vault-postings-processor-internal-hpa MIN_REPLICAS=7 sh /config-files/run-scaling.sh
              image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/tm/installer:4.6.14-manual
              name: scheduled-scaler
              volumeMounts:
                - mountPath: /config-files
                  name: scaling-config
          restartPolicy: OnFailure
          serviceAccountName: tm-installer
          volumes:
            - name: scaling-config
              configMap:
                name: tm-scheduled-scaler
                defaultMode: 0755
  schedule: 35 15 * * *
---
# Source: tm-installer/templates/cron-scheduled-scaler.yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  labels:
    app.kubernetes.io/instance: vault-postings-processor-internal-hpa-in
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: vault-postings-processor-internal-hpa-in
    tags.datadoghq.com/env: prd
    tags.datadoghq.com/service: vault-postings-processor-internal-hpa-in
  name: vault-postings-processor-internal-hpa-in
spec:
  concurrencyPolicy: Forbid
  jobTemplate:
    metadata:
      labels:
        app.kubernetes.io/instance: vault-postings-processor-internal-hpa-in
        app.kubernetes.io/managed-by: Helm
        app.kubernetes.io/name: vault-postings-processor-internal-hpa-in
        tags.datadoghq.com/env: prd
        tags.datadoghq.com/service: vault-postings-processor-internal-hpa-in
    spec:
      template:
        metadata:
          labels:
            app.kubernetes.io/instance: vault-postings-processor-internal-hpa-in
            app.kubernetes.io/managed-by: Helm
            app.kubernetes.io/name: vault-postings-processor-internal-hpa-in
            sidecar.istio.io/inject: "false"
            tags.datadoghq.com/env: prd
            tags.datadoghq.com/service: vault-postings-processor-internal-hpa-in
        spec:
          containers:
            - command:
                - sh
                - -c
                - ENABLED=true HPA=vault-postings-processor-internal-hpa MIN_REPLICAS=2 sh /config-files/run-scaling.sh
              image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/tm/installer:4.6.14-manual
              name: scheduled-scaler
              volumeMounts:
                - mountPath: /config-files
                  name: scaling-config
          restartPolicy: OnFailure
          serviceAccountName: tm-installer
          volumes:
            - name: scaling-config
              configMap:
                name: tm-scheduled-scaler
                defaultMode: 0755
  schedule: 30 16 * * *
---
# Source: tm-installer/templates/tm-scheduled-failed-job-monitor.yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  name: tm-scheduled-failed-job-monitor
  namespace: tm-system
spec:
  concurrencyPolicy: Replace
  jobTemplate:
    spec:
      template:
        metadata:
          annotations:
            checksum/config: 74a2e11ed748ea6e96c33cc395be5914b89caccfa5bbabd9732905661d0000ba
            sidecar.istio.io/inject: "false"
            vault.hashicorp.com/agent-init-first: "false"
            vault.hashicorp.com/agent-inject: "false"
          labels:
            app.kubernetes.io/instance: tm-scheduled-failed-job-monitor
            app.kubernetes.io/managed-by: Helm
            app.kubernetes.io/name: tm-scheduled-failed-job-monitor
            app.kubernetes.io/version: f30ee9e
            helm.sh/chart: tm-installer-0.1.0
            tags.datadoghq.com/env: prd
            tags.datadoghq.com/service: tm-scheduled-failed-job-monitor
            tags.datadoghq.com/version: f26332b-********
        spec:
          containers:
          - command:
            - sh
            - /config_files/init.sh
            env:
            - name: DOGSTATSD_HOST
              valueFrom:
                fieldRef:
                  fieldPath: status.hostIP
            - name: DD_ENTITY_ID
              valueFrom:
                fieldRef:
                  fieldPath: metadata.uid
            - name: DB_HOST
              value: dbmy-prd-tm-postgres-rds-postgres.ccu5sfln5zu7.ap-southeast-1.rds.amazonaws.com
            - name: HOME
              value: /home/<USER>
            - name: JOB_SCHEDULE_START_TIME
              value: null
            - name: TM_OPS_PORTAL_HOST
              value: ops.tm.prd.g-bank.app
            image: bitnami/python:3.10.14
            imagePullPolicy: Always
            name: tm-installer
            resources:
              limits:
                memory: 256Mi
              requests:
                cpu: 100m
                memory: 256Mi
            securityContext: {}
            volumeMounts:
            - mountPath: /config_files
              name: config-volume
            - mountPath: /home/<USER>
              name: home-dir
            - mountPath: /vault/secrets
              name: vault-secrets
          initContainers:
          - command:
            - sh
            - -c
            - mkdir -p /home/<USER>
            image: busybox
            name: init-home-dir
            securityContext:
              runAsGroup: 65532
              runAsUser: 65532
            volumeMounts:
            - mountPath: /home/<USER>
              name: home-dir
          - command:
            - sh
            - -c
            - |
              # Login to Vault using the Kubernetes auth method
              export VAULT_TOKEN=$(vault write -field=token auth/k8s-prd-tm-01/login role=tm-scheduled-monitoring jwt=$(cat /var/run/secrets/kubernetes.io/serviceaccount/token))

              # Fetch secrets from Vault and write them to a file
              vault kv get -version=2 -format=json database/creds/dbmy-prd-tm-postgres-rds-postgres-ro > /vault/secrets/db_creds.json
              vault kv get -version=1 -format=json secret/tm/alertmanager > /vault/secrets/slack.json
            env:
            - name: VAULT_ADDR
              value: http://vault-active.vault.svc.cluster.local:8200
            image: hashicorp/vault:1.8.0
            name: init-vault-secrets
            volumeMounts:
            - mountPath: /vault/secrets
              name: vault-secrets
          restartPolicy: Never
          securityContext:
            fsGroup: 65532
            runAsGroup: 65532
            runAsNonRoot: true
            runAsUser: 65532
          serviceAccountName: tm-installer
          volumes:
          - configMap:
              defaultMode: 493
              name: tm-scheduled-failed-job-monitor-config
            name: config-volume
          - emptyDir: {}
            name: home-dir
          - emptyDir: {}
            name: vault-secrets
      ttlSecondsAfterFinished: 600
  schedule: 0 2 * * *
  suspend: false
---
# Source: tm-installer/templates/tm-scheduled-monitor.yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  name: tm-scheduled-monitor
  namespace: tm-system
spec:
  concurrencyPolicy: Replace
  jobTemplate:
    spec:
      template:
        metadata:
          annotations:
            checksum/config: 158a5bbee755d632d92b552218fb4aecf535fa94f8e8a5b6c7f9d26227be656a
            sidecar.istio.io/inject: "false"
            vault.hashicorp.com/agent-init-first: "false"
            vault.hashicorp.com/agent-inject: "false"
          labels:
            app.kubernetes.io/instance: tm-scheduled-monitor
            app.kubernetes.io/managed-by: Helm
            app.kubernetes.io/name: tm-scheduled-monitor
            app.kubernetes.io/version: f30ee9e
            helm.sh/chart: tm-installer-0.1.0
            tags.datadoghq.com/env: prd
            tags.datadoghq.com/service: tm-scheduled-monitor
            tags.datadoghq.com/version: f26332b-********
        spec:
          containers:
          - command:
            - sh
            - /config_files/init.sh
            env:
            - name: DOGSTATSD_HOST
              valueFrom:
                fieldRef:
                  fieldPath: status.hostIP
            - name: DD_ENTITY_ID
              valueFrom:
                fieldRef:
                  fieldPath: metadata.uid
            - name: DB_HOST
              value: dbmy-prd-tm-postgres-rds-postgres.ccu5sfln5zu7.ap-southeast-1.rds.amazonaws.com
            - name: HOME
              value: /home/<USER>
            image: bitnami/python:3.10.14
            imagePullPolicy: Always
            name: tm-installer
            resources:
              limits:
                memory: 256Mi
              requests:
                cpu: 100m
                memory: 256Mi
            securityContext: {}
            volumeMounts:
            - mountPath: /config_files
              name: config-volume
            - mountPath: /home/<USER>
              name: home-dir
            - mountPath: /vault/secrets
              name: vault-secrets
          initContainers:
          - command:
            - sh
            - -c
            - mkdir -p /home/<USER>
            image: busybox
            name: init-home-dir
            securityContext:
              runAsGroup: 65532
              runAsUser: 65532
            volumeMounts:
            - mountPath: /home/<USER>
              name: home-dir
          - command:
            - sh
            - -c
            - |
              # Login to Vault using the Kubernetes auth method
              export VAULT_TOKEN=$(vault write -field=token auth/k8s-prd-tm-01/login role=tm-scheduled-monitoring jwt=$(cat /var/run/secrets/kubernetes.io/serviceaccount/token))

              # Fetch secrets from Vault and write them to a file
              vault kv get -version=2 -format=json database/creds/dbmy-prd-tm-postgres-rds-postgres-ro > /vault/secrets/db_creds.json
            env:
            - name: VAULT_ADDR
              value: http://vault-active.vault.svc.cluster.local:8200
            image: hashicorp/vault:1.8.0
            name: init-vault-secrets
            volumeMounts:
            - mountPath: /vault/secrets
              name: vault-secrets
          restartPolicy: Never
          securityContext:
            fsGroup: 65532
            runAsGroup: 65532
            runAsNonRoot: true
            runAsUser: 65532
          serviceAccountName: tm-installer
          volumes:
          - configMap:
              defaultMode: 493
              name: tm-scheduled-monitor-config
            name: config-volume
          - emptyDir: {}
            name: home-dir
          - emptyDir: {}
            name: vault-secrets
      ttlSecondsAfterFinished: 600
  schedule: 0 18 * * *
  suspend: false
---
# Source: tm-installer/templates/virtual-service.yaml
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: access-control-api
  namespace: istio-ingress
spec:
  hosts:
  - "access-control-api.tm.prd.g-bank.app"
  gateways:
  - tm-gateway
  http:
  - route:
    - destination:
        port:
          number: 8080
        host: access-control-api-gateway.tm-vault.svc.cluster.local
---
# Source: tm-installer/templates/virtual-service.yaml
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: audit-api-gateway
  namespace: istio-ingress
spec:
  hosts:
  - "audit-api.tm.prd.g-bank.app"
  gateways:
  - tm-gateway
  http:
  - route:
    - destination:
        port:
          number: 8080
        host: audit-api-gateway.tm-vault.svc.cluster.local
---
# Source: tm-installer/templates/virtual-service.yaml
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: core-api-gateway
  namespace: istio-ingress
spec:
  hosts:
  - "core-api.tm.prd.g-bank.app"
  gateways:
  - tm-gateway
  http:
  - route:
    - destination:
        port:
          number: 8080
        host: core-api-gateway.tm-vault.svc.cluster.local
---
# Source: tm-installer/templates/virtual-service.yaml
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: data-loader-api-gateway
  namespace: istio-ingress
spec:
  hosts:
  - "data-loader-api.tm.prd.g-bank.app"
  gateways:
  - tm-gateway
  http:
  - route:
    - destination:
        port:
          number: 8080
        host: data-loader-api-gateway.tm-vault.svc.cluster.local
---
# Source: tm-installer/templates/virtual-service.yaml
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: documentation
  namespace: istio-ingress
spec:
  hosts:
  - "documentation.tm.prd.g-bank.app"
  gateways:
  - tm-gateway
  http:
  - route:
    - destination:
        port:
          number: 80
        host: documentation.tm-vault.svc.cluster.local
---
# Source: tm-installer/templates/virtual-service.yaml
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: documents
  namespace: istio-ingress
spec:
  hosts:
  - "documents.tm.prd.g-bank.app"
  gateways:
  - tm-gateway
  http:
  - route:
    - destination:
        port:
          number: 32666
        host: vault-documents-webserver-internal.tm-vault.svc.cluster.local
---
# Source: tm-installer/templates/virtual-service.yaml
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: products-api-gateway
  namespace: istio-ingress
spec:
  hosts:
  - "products-api.tm.prd.g-bank.app"
  gateways:
  - tm-gateway
  http:
  - route:
    - destination:
        port:
          number: 8080
        host: products-api-gateway.tm-vault.svc.cluster.local
---
# Source: tm-installer/templates/virtual-service.yaml
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: saml-idp
  namespace: istio-ingress
spec:
  hosts:
  - "saml-idp.tm.prd.g-bank.app"
  gateways:
  - tm-gateway
  http:
  - route:
    - destination:
        port:
          number: 8000
        host: saml-idp.tm-vault.svc.cluster.local
---
# Source: tm-installer/templates/virtual-service.yaml
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: admin-website
  namespace: istio-ingress
spec:
  hosts:
  - "ops.tm.prd.g-bank.app"
  gateways:
  - tm-gateway
  http:
  - route:
    - destination:
        port:
          number: 6008
        host: vault-admin-website.tm-vault.svc.cluster.local
---
# Source: tm-installer/templates/virtual-service.yaml
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: workflow-simulator
  namespace: istio-ingress
spec:
  hosts:
  - "workflow-simulator.tm.prd.g-bank.app"
  gateways:
  - tm-gateway
  http:
  - route:
    - destination:
        port:
          number: 8080
        host: workflow-simulator.tm-vault.svc.cluster.local
---
# Source: tm-installer/templates/virtual-service.yaml
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: workflows-api-gateway
  namespace: istio-ingress
spec:
  hosts:
  - "workflows-api.tm.prd.g-bank.app"
  gateways:
  - tm-gateway
  http:
  - route:
    - destination:
        port:
          number: 8080
        host: workflows-api-gateway.tm-vault.svc.cluster.local
---
# Source: tm-installer/templates/virtual-service.yaml
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: grafana
  namespace: istio-ingress
spec:
  hosts:
  - "grafana.tm.prd.g-bank.app"
  - "tm-grafana.tm.prd.g-bank.app"
  gateways:
  - tm-gateway
  http:
  - route:
    - destination:
        port:
          number: 8080
        host: tm-grafana.tm-monitoring.svc.cluster.local
---
# Source: tm-installer/templates/virtual-service.yaml
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: thanos-query
  namespace: istio-ingress
spec:
  hosts:
    - "metrics.tm.prd.g-bank.app"
    - "tm-metrics.tm.prd.g-bank.app"
  gateways:
    - tm-gateway
  http:
    - route:
      - destination:
          port:
            number: 8080
          host: thanos-query.tm-monitoring.svc.cluster.local
---
# Source: tm-installer/templates/virtual-service.yaml
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: alertmanager
  namespace: istio-ingress
spec:
  hosts:
  - "alertmanager.tm.prd.g-bank.app"
  - "tm-alertmanager.tm.prd.g-bank.app"
  gateways:
  - tm-gateway
  http:
  - route:
    - destination:
        port:
          number: 9093
        host: alertmanager.tm-monitoring.svc.cluster.local
