env: stg
serviceAccount:
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks/cluster/stg-tm-01/tm-installer
virtualService:
  alertmanager:
    port: 9093
  grafana:
    port: 8080
  thanosQuery:
    port: 8080
vaultAnnotation:
  #  we will manage the annotation in dedicated templates
  disabled: true
scheduleScaling:
  vault-account-hpa:
    scaleOut:
      enabled: "true"
      schedule: "30 15 * * *"
      minReplicas: 20
    scaleIn:
      enabled: "true"
      schedule: "15 16 * * *"
      minReplicas: 7
  supervisor-contracts-hpa:
    scaleOut:
      enabled: "true"
      schedule: "31 15 * * *"
      minReplicas: 10
    scaleIn:
      enabled: "true"
      schedule: "15 16 * * *"
      minReplicas: 2
  kernel-balance-hpa:
    scaleOut:
      enabled: "true"
      schedule: "32 15 * * *"
      minReplicas: 10
    scaleIn:
      enabled: "true"
      schedule: "15 16 * * *"
      minReplicas: 4
  vault-posting-hpa:
    scaleOut:
      enabled: "true"
      schedule: "33 15 * * *"
      minReplicas: 10
    scaleIn:
      enabled: "true"
      schedule: "15 16 * * *"
      minReplicas: 4
  vault-postings-account-postings-processor-hpa:
    scaleOut:
      enabled: "true"
      schedule: "34 15 * * *"
      minReplicas: 10
    scaleIn:
      enabled: "true"
      schedule: "15 16 * * *"
      minReplicas: 2
  vault-postings-processor-internal-hpa:
    scaleOut:
      enabled: "true"
      schedule: "35 15 * * *"
      minReplicas: 7
    scaleIn:
      enabled: "true"
      schedule: "15 16 * * *"
      minReplicas: 2
  contract-engine-hpa:
    scaleOut:
      enabled: "false"
      schedule: "36 15 * * *"
      minReplicas: 18
    scaleIn:
      enabled: "false"
      schedule: "30 16 * * *"
      minReplicas: 4
  contract-post-posting-processor-hpa:
    scaleOut:
      enabled: "false"
      schedule: "37 15 * * *"
      minReplicas: 8
    scaleIn:
      enabled: "false"
      schedule: "30 16 * * *"
      minReplicas: 2
  contract-post-balance-update-processor-hpa:
    scaleOut:
      enabled: "false"
      schedule: "38 15 * * *"
      minReplicas: 10
    scaleIn:
      enabled: "false"
      schedule: "30 16 * * *"
      minReplicas: 2
scheduleRedisRestart:
  enabled: "true"
  schedule: "30 18 * * *"
  memoryThreshold: "0.80"
scheduledMonitor:
  requests:
    memory: 256Mi
    cpu: 100m
  limits:
    memory: 256Mi
  schedule: "0 * * * *"
  suspend: false
  enabled: true
  vaultRole: "tm-scheduled-monitoring"
  vaultDbRole: "dbmy-stg-tm-postgres-rds-postgres-ro"
  dbHost: "dbmy-stg-tm-postgres-rds-postgres.clvph685h5u7.ap-southeast-1.rds.amazonaws.com"
scheduledFailedJobMonitor:
  requests:
    memory: 256Mi
    cpu: 100m
  limits:
    memory: 256Mi
  schedule: "0 * * * *"
  suspend: false
  enabled: true
  vaultRole: "tm-scheduled-monitoring"
  vaultDbRole: "dbmy-stg-tm-postgres-rds-postgres-ro"
  dbHost: "dbmy-stg-tm-postgres-rds-postgres.clvph685h5u7.ap-southeast-1.rds.amazonaws.com"
  envVars:
    - name: JOB_SCHEDULE_START_TIME
      # time format (must be utc): 2024-05-13T16:00:00Z, setting this to empty will default to beginning of the day
      value: ""
    - name: TM_OPS_PORTAL_HOST
      value: "ops.tm.stg.g-bank.app"
scheduledConfigMonitor:
  requests:
    memory: 256Mi
    cpu: 100m
  limits:
    memory: 256Mi
  vaultRole: "tm-scheduled-monitoring"
  schedule: "0 * * * *"
  suspend: false
  enabled: true
  envVars:
    - name: SEND_TO_SLACK
      value: "\"true\""