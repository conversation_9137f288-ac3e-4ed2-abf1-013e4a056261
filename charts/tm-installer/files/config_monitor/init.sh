
exit_code=0
echo "INFO: Installing dependency"
pip install --user "requests>=2.28.2" "kubernetes>=33.1.0" "jmespath>=1.0.1" "pyyaml>=6.0.0" || exit_code=$?

if [ $exit_code -ne 0 ]; then
    echo "ERROR: Failed to install requests, kubernetes, jmespath and pyyaml"
    exit 0
fi

echo "INFO: Running Thought Machine config monitoring script"
slack_flag=""
if [ "$SEND_TO_SLACK" = "true" ]; then
    slack_flag="--slack"
fi

if [ "$DEBUG_MODE" = "true" ]; then
    sleep 7200
    exit 0
fi

python /config_files/monitor.py --config /config_files/config.json ${slack_flag} || exit_code=$?


if [ $exit_code -ne 0 ]; then
    echo "ERROR: Failed to run monitor.py"
    exit 0
fi

echo "INFO: Thought Machine config monitor script completed successfully, exiting..."
