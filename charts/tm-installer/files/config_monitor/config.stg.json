{"tm_config": [{"name": "Deployment contract-engine-account-scheduling batch size", "resource_type": "deployment", "resource_name": "contract-engine-account-scheduling", "namespace": "tm-vault", "json_path": "spec.template.spec.containers[?name=='main']|[0].command[?@=='--batch_size=200']|[0]", "expected_value": "--batch_size=200", "enabled": true}, {"name": "Cron job audit-cleanup batch size", "resource_type": "cronjob", "resource_name": "audit-cleanup", "namespace": "tm-vault", "json_path": "spec.jobTemplate.spec.template.spec.containers[?name=='cleanup']|[0].env[?name=='BATCH_SIZE']|[0].value", "expected_value": "200", "enabled": true}, {"name": "Cron job audit-cleanup cron schedule", "resource_type": "cronjob", "resource_name": "audit-cleanup", "namespace": "tm-vault", "json_path": "spec.schedule", "expected_value": "4 0 * * *", "enabled": true}, {"name": "Deployment scheduler-job-poller batch size", "resource_type": "deployment", "resource_name": "scheduler-job-poller", "namespace": "tm-vault", "json_path": "spec.template.spec.containers[?name=='main']|[0].env[?name=='MAX_TPS']|[0].value", "expected_value": "900", "enabled": true}, {"name": "Config map account-processor-config", "resource_type": "configmap", "resource_name_prefix": "account-processor-config-vault-core-pkg", "namespace": "tm-vault", "json_path": "config.yaml", "expected_value": 1000, "config_json_path": "dispatcher.account.converter_page_size", "enabled": true}, {"name": "Config map account-processor-republisher-config-vault-core-pkg", "resource_type": "configmap", "resource_name_prefix": "account-processor-republisher-config-vault-core-pkg", "namespace": "tm-vault", "json_path": "config.yaml", "expected_value": 300, "config_json_path": "republisher.retry_step", "enabled": true}, {"name": "Config map vault-ledger-balances-accumulator-processor-config-vault-ledger-balances-pkg", "resource_type": "configmap", "resource_name_prefix": "vault-ledger-balances-accumulator-processor-config-vault-ledger-balances-pkg", "namespace": "tm-vault", "json_path": "config.yaml", "expected_value": 100000, "config_json_path": "cache_size", "enabled": true}, {"name": "Pod disruption budget ca-injector-pdb-tm-vault", "resource_type": "poddisruptionbudget", "resource_name": "ca-injector-pdb-tm-vault", "namespace": "tm-vault", "json_path": "spec.maxUnavailable", "expected_value": "50%", "enabled": true}, {"name": "Pod disruption budget ca-injector-pdb-tm-vault", "resource_type": "poddisruptionbudget", "resource_name": "ca-injector-pdb-tm-vault", "namespace": "tm-vault", "json_path": "spec.minAvailable", "expected_value": null, "enabled": true}, {"name": "Pod disruption budget vault-payment-order-events-orchestrator", "resource_type": "poddisruptionbudget", "resource_name": "vault-payment-order-events-orchestrator", "namespace": "tm-vault", "json_path": "spec.maxUnavailable", "expected_value": "50%", "enabled": true}, {"name": "Pod disruption budget vault-payment-order-events-orchestrator", "resource_type": "poddisruptionbudget", "resource_name": "vault-payment-order-events-orchestrator", "namespace": "tm-vault", "json_path": "spec.minAvailable", "expected_value": null, "enabled": true}, {"name": "Pod disruption budget saml-idp", "resource_type": "poddisruptionbudget", "resource_name": "saml-idp", "namespace": "tm-vault", "json_path": "spec.maxUnavailable", "expected_value": "50%", "enabled": true}, {"name": "Pod disruption budget saml-idp", "resource_type": "poddisruptionbudget", "resource_name": "saml-idp", "namespace": "tm-vault", "json_path": "spec.minAvailable", "expected_value": null, "enabled": true}]}