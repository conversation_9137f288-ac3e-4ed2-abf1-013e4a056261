import argparse
import json
import logging
import os
from typing import Any, Dict, Optional

import jmespath
import requests
import yaml
from kubernetes import client, config
from kubernetes.client.rest import ApiException

try:
    from colorlog import ColoredFormatter

    HAS_COLORLOG = True
except ImportError:
    HAS_COLORLOG = False

CACHE = {}

IS_LOCAL = os.getenv('IS_LOCAL') == 'true'
SLACK_WEBHOOK_URL = ""
# Resource API mapping for Kubernetes resources
RESOURCE_API_MAP = {
    'deployment': ('apps_v1_api', 'read_namespaced_deployment', 'list_namespaced_deployment'),
    'statefulset': ('apps_v1_api', 'read_namespaced_stateful_set', 'list_namespaced_stateful_set'),
    'daemonset': ('apps_v1_api', 'read_namespaced_daemon_set', 'list_namespaced_daemon_set'),
    'replicaset': ('apps_v1_api', 'read_namespaced_replica_set', 'list_namespaced_replica_set'),
    'service': ('core_v1_api', 'read_namespaced_service', 'list_namespaced_service'),
    'pod': ('core_v1_api', 'read_namespaced_pod', 'list_namespaced_pod'),
    'cronjob': ('batch_v1_api', 'read_namespaced_cron_job', 'list_namespaced_cron_job'),
    'configmap': ('core_v1_api', 'read_namespaced_config_map', 'list_namespaced_config_map'),
    'secret': ('core_v1_api', 'read_namespaced_secret', 'list_namespaced_secret'),
    'persistentvolumeclaim': ('core_v1_api', 'read_namespaced_persistent_volume_claim',
                              'list_namespaced_persistent_volume_claim'),
    'pvc': ('core_v1_api', 'read_namespaced_persistent_volume_claim', 'list_namespaced_persistent_volume_claim'),
    'hpa': ('autoscaling_v2_api', 'read_namespaced_horizontal_pod_autoscaler',
            'list_namespaced_horizontal_pod_autoscaler'),
    'horizontalpodautoscaler': ('autoscaling_v2_api', 'read_namespaced_horizontal_pod_autoscaler',
                                'list_namespaced_horizontal_pod_autoscaler'),
    'ingress': ('networking_v1_api', 'read_namespaced_ingress', 'list_namespaced_ingress'),
    'networkpolicy': ('networking_v1_api', 'read_namespaced_network_policy', 'list_namespaced_network_policy'),
    'poddisruptionbudget': ('policy_v1_api', 'read_namespaced_pod_disruption_budget',
                            'list_namespaced_pod_disruption_budget'),
}


class SlackClient:
    def __init__(self, slack_webhook_url: str, mock: bool = False):
        self.mock = mock
        self.slack_webhook_url = slack_webhook_url

    def notify_error(self, error_message: str):
        if self.mock:
            logger.info("Slack notification is disabled")
            return

        message = {
            "blocks": [
                {
                    "type": "header",
                    "text": {
                        "type": "plain_text",
                        "text": "🚨 TM Config Monitor Error"
                    }
                },
                {
                    "type": "section",
                    "text": {
                        "type": "mrkdwn",
                        "text": f"Error:\n```{error_message}```"
                    }
                }
            ]
        }
        response = requests.post(self.slack_webhook_url, json=message, timeout=10)
        if response.status_code != 200:
            logger.error(f"Failed to send message to Slack: {response.text}")
            return

    def notify_slack(self, results: list[dict]):
        """
        Notify the results to slack
        :param results:
        :return:
        """
        # send the results to slack using block kit
        # report the summary
        # for the error checks, send the detail to slack
        if self.mock:
            logger.info("Slack notification is disabled")
            return

        title = "TM Config Monitor Report"
        message = {
            "blocks": [
                {
                    "type": "header",
                    "text": {
                        "type": "plain_text",
                        "text": title
                    }
                }
            ]
        }
        # send summary count of pass, skipped and errors
        passed_count = sum(1 for r in results if r["success"])
        skipped_count = sum(1 for r in results if r["skipped"])
        failed_count = sum(1 for r in results if not r["success"] and not r["skipped"])
        message["blocks"].append({
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": f"*Summary:*\nPassed: {passed_count}\nSkipped: {skipped_count}\nFailed: {failed_count}"
            }
        })
        passed_checks = [r for r in results if r["success"]]
        skipped_checks = [r for r in results if r["skipped"]]
        failed_checks = [r for r in results if not r["success"] and not r["skipped"]]
        message["blocks"].append({
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": f"✅ *Passed Checks ({passed_count}):*"
            }
        })
        for r in passed_checks:
            message["blocks"].append({
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"• {r['name']}"
                }
            })
        message["blocks"].append({
            "type": "divider"
        })
        message["blocks"].append({
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": f"⏩ *Skipped Checks ({skipped_count}):*"
            }
        })
        for r in skipped_checks:
            message["blocks"].append({
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"• {r['name']}"
                }
            })

        # for the error checks, just dump the error message to json and send as code block in each block
        if failed_count > 0:
            message["blocks"].append({
                "type": "divider"
            })
            message["blocks"].append({
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"🚨 *Failed Checks ({failed_count}):*"
                }
            })
            # add the name and error message for each failed check
            for r in failed_checks:
                if not r["success"] and not r["skipped"]:
                    message["blocks"].append({
                        "type": "section",
                        "text": {
                            "type": "mrkdwn",
                            "text": f"• {r['name']}"
                        }
                    })
                    message["blocks"].append({
                        "type": "section",
                        "text": {
                            "type": "mrkdwn",
                            "text": f"Error:\n```{r['error']}```"
                        }
                    })
                    message["blocks"].append({
                        "type": "section",
                        "text": {
                            "type": "mrkdwn",
                            "text": f"```{json.dumps(r, indent=2)}```"
                        }
                    })
                    message["blocks"].append({
                        "type": "divider"
                    })
        response = requests.post(self.slack_webhook_url, json=message, timeout=10)
        if response.status_code != 200:
            logger.error(f"Failed to send message to Slack: {response.text}")
            return


def setup_logger():
    """
    Set up logging with conditional formatting based on IS_LOCAL environment variable.

    If IS_LOCAL=true: Use colored console output (if colorlog is available)
    If IS_LOCAL=false or not set: Use standard logging formatter

    Returns:
        logging.Logger: Configured logger instance
    """
    logger = logging.getLogger("[tm-config-monitor]")
    logger.setLevel(logging.INFO)

    # Remove existing handlers to avoid duplicates
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # Create console handler
    console_handler = logging.StreamHandler()

    # Configure formatter based on environment
    is_local = os.getenv('IS_LOCAL', '').lower() == 'true'

    if is_local and HAS_COLORLOG:
        formatter = ColoredFormatter(
            '%(log_color)s%(levelname)s:%(name)s:%(message)s',
            log_colors={
                'DEBUG': 'cyan',
                'INFO': 'green',
                'WARNING': 'yellow',
                'ERROR': 'red',
                'CRITICAL': 'bold_red',
            }
        )
    else:
        formatter = logging.Formatter('%(levelname)s:%(name)s:%(message)s')

    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    return logger


# Initialize logger at module level
logger = setup_logger()


def setup_kubernetes_client():
    """
    Set up the Kubernetes client with proper authentication.

    This function attempts to load the Kubernetes configuration in the following order:
    1. In-cluster configuration (if running inside a pod)
    2. Kubeconfig file from default locations

    Returns:
        kubernetes.client.ApiClient: Configured Kubernetes API client

    Raises:
        Exception: If unable to load Kubernetes configuration
    """

    # setup kube config that's equivalent to the above bash script

    try:
        context = os.getenv('KUBE_CONTEXT')
        if context is not None and context != "":
            logger.info(f"Kube context detected. Loading config for context: {context}")
            # we assume the kube config file is referenced by KUBECONFIG env var or in default location
            config.load_config(context=context)
        else:
            # logging in-cluster config
            logger.info("Kube context not detected. Loading in-cluster config")
            config.load_config()
    except config.ConfigException as e:
        raise Exception(f"Unable to load Kubernetes configuration: {e}")

    return client.ApiClient()


def extract_value_with_jmespath(resource_dict: Dict[str, Any], jmes_path: str, is_collection: bool = False) -> Optional[
    str]:
    """
    Extract a value from a Kubernetes resource dictionary using JMESPath.

    Args:
        resource_dict (dict): The Kubernetes resource as a dictionary
        jsonpath (str): JMESPath

    Returns:
        str: The extracted value as a string, or None if not found
        :param jmes_path:
    """
    try:
        # Use JMESPath to extract the value
        result = jmespath.search(jmes_path, resource_dict)

        # Convert the result to string if it exists
        if result is not None:
            if is_collection:
                return result
            if not isinstance(result, list):
                return result
            return result[0] if len(result) == 1 else result
        else:
            return result

    except Exception as e:
        logger.debug(f"Failed to extract value with JMESPath '{jmes_path}': {e}")
        return None


def dispatch_api_client(api_client_name: str, k8s_client: client.ApiClient) -> Any:
    api_instance = None
    if api_client_name == 'apps_v1_api':
        api_instance = client.AppsV1Api(k8s_client)
    elif api_client_name == 'core_v1_api':
        api_instance = client.CoreV1Api(k8s_client)
    elif api_client_name == 'autoscaling_v2_api':
        api_instance = client.AutoscalingV2Api(k8s_client)
    elif api_client_name == 'networking_v1_api':
        api_instance = client.NetworkingV1Api(k8s_client)
    elif api_client_name == 'batch_v1_api':
        api_instance = client.BatchV1Api(k8s_client)
    elif api_client_name == 'policy_v1_api':
        api_instance = client.PolicyV1Api(k8s_client)
    return api_instance


def run_kubectl_check(check_config, k8s_client=None):
    """
    Runs a Kubernetes API check based on the provided check configuration
    and compares the output to the expected value.

    Args:
        check_config (dict): A dictionary containing the check details:
            - name (str): A descriptive name for the check.
            - resource_type (str): e.g., "deployment", "statefulset", "hpa".
            - resource_name (str): The name of the Kubernetes resource.
            - resource_name_prefix (str): The prefix of the Kubernetes resource name. If specified, will look for the first resource that starts with this prefix.
            - namespace (str): The namespace of the resource.
            - json_path (str): The JSONPath expression to extract the value.
            - config_json_path (str): The JSONPath expression to extract the value from the k8s config file
            - expected_value (str): The expected value (as a string).
        k8s_client: Kubernetes API client instance (optional, will create if not provided)

    Returns:
        dict: A dictionary containing the check result:
            - name (str): The name of the check.
            - success (bool): True if the check passed, False otherwise.
            - command (str): The equivalent kubectl command for reference.
            - actual_value (str): The actual value obtained from the API.
            - expected_value (str): The expected value.
            - error (str, optional): Error message if the API call failed.
    """
    result = {
        "name": check_config["name"],
        "success": False,
        "actual_value": None,
        "expected_value": check_config["expected_value"],
        "command": None,
        "error": None,
        "skipped": False
    }
    if check_config.get("enabled", False) in ["false", False, None]:
        result["skipped"] = True
        return result

    try:
        # Set up Kubernetes client if not provided
        if k8s_client is None:
            k8s_client = setup_kubernetes_client()

        # Get the appropriate API client based on resource type
        resource_type = check_config["resource_type"].lower()
        resource_name_prefix = check_config.get("resource_name_prefix")
        resource_name = check_config.get("resource_name")
        if resource_name_prefix is None and resource_name is None:
            result["error"] = f"Resource name or name prefix not specified"
            return result
        namespace = check_config["namespace"]

        # Use the module-level resource API mapping
        if resource_type not in RESOURCE_API_MAP:
            result["error"] = f"Unsupported resource type: {resource_type}"
            return result

        api_client_name, method_name, list_method_name = RESOURCE_API_MAP[resource_type]

        api_instance = dispatch_api_client(api_client_name, k8s_client)
        if api_instance is None:
            result["error"] = f"Unknown API client: {api_client_name}"
            return result
        name_prefix = check_config.get("resource_name_prefix")

        resource_cache_key = f'{namespace}.{resource_type}.{resource_name}.{method_name}'
        resource_listing_cache_key = f'{namespace}.{resource_type}.{list_method_name}'

        # the exact name is not given, we need to list all resources and find the first one that matches the prefix
        if name_prefix:
            logger.info(f"Listing resources with prefix {name_prefix} in namespace {namespace}")
            list_method = getattr(api_instance, list_method_name)
            if resource_listing_cache_key in CACHE:
                logger.info(f"Using cached resource list for {resource_listing_cache_key}")
                resource_list = CACHE[resource_listing_cache_key]
            else:
                resource_list = list_method(namespace=namespace)
                CACHE[resource_listing_cache_key] = resource_list
            resource_name = next(
                (item.metadata.name for item in resource_list.items if item.metadata.name.startswith(name_prefix)),
                None)

        if resource_name is None:
            logger.error(f"Resource '{resource_name_prefix}' not found in namespace '{namespace}'")
            result["error"] = f"Resource '{resource_name_prefix}' not found in namespace '{namespace}'"
            return result

        if resource_cache_key in CACHE:
            logger.info(f"Using cached resource for {resource_cache_key}")
            resource_dict = CACHE[resource_cache_key]
        else:
            # Call the appropriate API method
            api_method = getattr(api_instance, method_name)
            resource = api_method(name=resource_name, namespace=namespace)
            resource_dict = api_instance.api_client.sanitize_for_serialization(resource)
            CACHE[resource_cache_key] = resource_dict


        expected_value = check_config["expected_value"]
        json_path = check_config.get("json_path")
        config_json_path = check_config.get("config_json_path")
        # Extract the value using JMESPath
        if resource_type == "configmap":
            resource_dict = resource_dict["data"].get(check_config["json_path"])
            if resource_dict is None:
                result["error"] = f"Key '{check_config['json_path']}' not found in configmap '{resource_name}'"
                return result
            resource_dict = yaml.safe_load(resource_dict)
            json_path = config_json_path

        actual_value_raw = extract_value_with_jmespath(resource_dict, json_path,
                                                       is_collection=isinstance(expected_value, list))

        if actual_value_raw is None:
            result[
                "error"] = f"JSMEPath '{config_json_path or check_config['json_path']}' did not match any value in the resource"
            result["actual_value"] = "null"
            return result

        # Process the value (remove suffix if specified)
        value_suffix = check_config.get("value_suffix")
        if value_suffix and actual_value_raw.endswith(value_suffix):
            actual_value = actual_value_raw[:-len(value_suffix)]
        else:
            actual_value = actual_value_raw

        result["actual_value"] = actual_value
        if actual_value == expected_value:
            result["success"] = True
        else:
            result["error"] = f"Mismatch: Actual='{actual_value}', Expected='{expected_value}'"

    except ApiException as e:
        if e.status == 404:
            result["error"] = f"Resource '{resource_name}' not found in namespace '{namespace}'"
        else:
            result["error"] = f"Kubernetes API error: {e.reason} (status: {e.status})"
        result["actual_value"] = "error"
    except Exception as e:
        result["error"] = f"An unexpected error occurred: {str(e)}"
        result["actual_value"] = "error"

    return result


def init_cred(slack: bool):
    logger.debug("loading creds")
    if slack:
        slack_creds = None
        if not IS_LOCAL:
            slack_cred_path = '/vault/secrets/slack.json'
            with open(slack_cred_path, 'r') as f:
                slack_creds = json.load(f)

        global SLACK_WEBHOOK_URL
        SLACK_WEBHOOK_URL = slack_creds["slack_bot_token"] if not IS_LOCAL else os.getenv("SLACK_WEBHOOK_URL")


def main():
    """
    Main function to load checks from config and run them.
    """
    parser = argparse.ArgumentParser(description="Run checks from a configuration file.")
    parser.add_argument('--config',
                        type=str,
                        default='charts/tm-installer/files/config_monitor/config.dev.json',
                        help='Path to the configuration file.')
    parser.add_argument("--slack",
                        action="store_true",
                        help='Send report to slack.')
    args = parser.parse_args()
    config_file = args.config
    send_to_slack = args.slack
    init_cred(slack=args.slack)
    results = []
    slack_client = SlackClient(slack_webhook_url=SLACK_WEBHOOK_URL, mock=IS_LOCAL)

    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
    except FileNotFoundError:
        error_message = f"Configuration file '{config_file}' not found."
        logger.error(error_message)
        slack_client.notify_error(error_message)
        return
    except json.JSONDecodeError:
        error_message = f"Invalid JSON in '{config_file}'."
        logger.error(error_message)
        slack_client.notify_error(error_message)
        return

    if not config_data:
        error_message = f"No checks found in configuration file '{config_file}'."
        logger.error(error_message)
        slack_client.notify_error(error_message)
        return
    checks = config_data.get('tm_config', None)
    if not checks:
        error_message = f"No checks found in configuration file '{config_file}'."
        logger.error(error_message)
        slack_client.notify_error(error_message)
        return

    logger.info(f"Loading checks from '{config_file}'...")

    # Initialize Kubernetes client once
    try:
        k8s_client = setup_kubernetes_client()
        logger.info("Successfully connected to Kubernetes cluster")
    except Exception as e:
        error_message = f"Failed to connect to Kubernetes cluster: {e}"
        logger.error(error_message)
        slack_client.notify_error(error_message)
        return

    for i, check_config in enumerate(checks):
        name = check_config.get('name', 'Unnamed Check')
        logger.info(f"Running check {i + 1}/{len(checks)}: {name}...")
        check_result = run_kubectl_check(check_config, k8s_client)
        results.append(check_result)

        if check_result["success"]:
            logger.info(f"Check PASSED: {check_result['name']}")
            logger.info(f"   Command: {check_result['command']}")
            logger.info(f"   Actual: {check_result['actual_value']}, Expected: {check_result['expected_value']}")
        elif check_result["skipped"]:
            logger.info(f"Check SKIPPED: {check_result['name']}")
        else:
            logger.error(f"Check FAILED: {check_result['name']}")
            logger.error(f"   Command: {check_result['command']}")
            logger.error(f"   Expected: {check_result['expected_value']}")
            logger.error(f"   Actual: {check_result['actual_value']}")
            if check_result['error'] and "Mismatch" not in check_result['error']:
                logger.error(f"   Error: {check_result['error']}")

    logger.info("--- Summary ---")
    passed_count = sum(1 for r in results if r["success"])
    skipped_count = sum(1 for r in results if r["skipped"])
    failed_count = len(results) - passed_count - skipped_count

    logger.info(f"Total checks: {len(results)}")
    logger.info(f"Passed: {passed_count}")
    logger.info(f"Skipped: {skipped_count}")
    logger.info(f"Failed: {failed_count}")

    if failed_count > 0:
        logger.info("Failed checks details:")
        for r in results:
            if not r["success"] and not r["skipped"]:
                logger.error(
                    f"- {r['name']}: Expected '{r['expected_value']}', Got '{r['actual_value']}'. Error: {r.get('error', 'N/A')}")
    logger.info("Sending notification to slack...")
    if send_to_slack:
        slack_client.notify_slack(results)
    else:
        logger.info("Slack notification is disabled")


if __name__ == "__main__":
    main()
