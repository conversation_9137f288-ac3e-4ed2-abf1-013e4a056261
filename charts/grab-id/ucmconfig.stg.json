{"host": "staging", "rawConfig": {"PartnerAuthRequestPeriod": {"template": "{{int .value}}", "data": {"value": 3600}, "hash": ":global.4TtQZsEuZeiJvPvNzc-zx76QrSM=:global.stg.wdu50zxnYt1l86vUmBwTWdIkaEc=:template.dBPGNuzAiX2SUdqyZn35eIqNGcw="}, "S3DownloadErrorsKey": {"template": " {{ string .value }}", "data": {"value": "NotFound"}, "hash": ":global.CanVTS11z7BUL2kG6nPj52KfHiE=:global.stg.6_4VVmEZKaMSCZxtK9dc7BGEUWU=:template.lLsUUmeDT82HHcXso2A3dNpteF4="}, "_serviceInfo:conveyor_publish": {"template": "# when set to true, the service would be able to publish configurations only through conveyor\n{\n enabled: {{ bool .enabled false}}\n}\n", "data": {"enabled": false}, "hash": ":global.u7rtdAoluHslXQkgm-83jGOS6C8=:global.stg.:template.AnR-6L1JEC40bel2IZnD9hQ-6wM="}, "_serviceInfo:deploy": {"template": "#### Service Release Tag / Artifact Name\nreleaseTag: '{{ .releaseTag }}'\n\n#### Deployment Serial Percentage\nserialPercentage: {{ .serialPercentage }}\n\n#### Deploy job URL\njob: '{{ .job }}'\n\n#### LDAP Group\nldap: '{{ .ldap }}'\n\n#### Slack deployment notification channel\nalert: '{{ .alert }}'", "data": {"alert": "#iam-team", "job": "https://deploy.myteksi.net/job/stg-deploy/job/grab-id/", "ldap": "Engineering-grab-id", "releaseTag": "grab-id", "serialPercentage": 25}, "hash": ":global.HwDgouqiR1qeg3ShUJXmECJF7ds=:global.stg.79BmvvNI7HuFxOBBVsocSBB4psM=:template.ul6UA6yT0s5abujinxQr4tbPc6s="}, "_serviceInfo:metadata": {"template": "#### DO NOT EDIT THIS TEMPLATE ##\n#### Never change anything to this Template pane. \n#### These Service Meta-Information are defined only by SPARTAN team members. \n#### Go to the values pane and update any value.\n\n#### Service Name\nserviceName: '{{ .serviceName }}'\n\n#### Service Key\nserviceKey: '{{ .serviceKey }}'\n\n#### Service Criticality\ncriticality: '{{ .criticality }}'\n\n#### Description of Service\ndescription: \"{{ .description }}\"\n\n#### Service Wiki URL\nserviceWiki: '{{ .serviceWiki }}'\n\n#### Tech Family/Team\ntechFamily: '{{ .techFamily }}'\n\n#### Project Slack Channel\nserviceSlack: '{{ .serviceSlack }}'\n\n#### Mailing List\nmailingList: '{{ .mailingList }}'\n\n#### Pagerduty team name\npagerduty: '{{ .pagerduty }}'\n\n#### Changelog URL\nchangeLog: '{{ .changeLog }}'\n\n#### Using Configurations on UCM\nucmConfig: {{ .ucmConfig }}\n\n#### Gandalf URL\ngandalf: '{{ .gandalf }}'\n\n#### Runbook URL\nrunbook: '{{ .runbook }}'\n\n#### Oncall tag on slack\noncallTag: '{{ .oncallTag }}'\n\n#### ServiceLocations\n#### Locations\nlocations: [\n    {{ range $idx, $val := .locations }}\n    '{{ $val }}',\n    {{ end }}\n]\n\n#### Code Base URLs\ncodeBase: {\n    {{ range $idx, $val := .codeBase }}\n        '{{ $val.name }}': '{{ $val.url }}',\n    {{ end }}\n}\n\n#### Datadog Dashboards\ndatadog: {\n    {{ range $idx, $val := .datadog }}\n        '{{ $val.name }}': '{{ $val.url }}',\n    {{ end }}\n}\n\n##### PagerDuty Settings\npagerdutySettings:\n    team: '{{ .pagerdutySettings.team }}'\n    service: '{{ .pagerdutySettings.service }}'\n    datadogIntegration: '{{ .pagerdutySettings.datadogIntegration }}'\n", "data": {"codeBase": [{"name": "grab-id", "url": "https://gitlab.myteksi.net/gophers/go/tree/master/dispatcher/grab-id"}], "criticality": "critical", "datadog": [{"name": "Primary", "url": "https://app.datadoghq.com/dash/98263/grabid"}, {"name": "OnCall", "url": "https://app.datadoghq.com/dash/252893/grab-id-oncall"}], "gandalf": "https://gandalf-ui.stg-myteksi.com/test-cases?environment=staging&page=1&services=grabidv1", "locations": ["Seattle"], "mailingList": "<EMAIL>", "oncallTag": "@oncall-grab-id", "pagerduty": "Grab-ID", "pagerdutySettings": {"datadogIntegration": "", "service": "grab-id", "team": "Grab-ID"}, "runbook": "https://wiki.grab.com/display/SRE/GrabID+Runbooks", "serviceKey": "grab-id", "serviceName": "grab-id", "serviceSlack": "#grab-id", "serviceWiki": "https://wiki.grab.com/display/GRABID/GrabID+Home", "techFamily": "Trust, Identity, Safety & Security (TISS)"}, "hash": ":global.ibYtYOVnO1Mir7lF_eDM43J6164=:global.stg.:template.IGe5-uSem7qQde0uWcEA1sTTen4="}, "_serviceInfo:permission": {"template": "## To find out how to add members,\n## see https://wiki.grab.com/display/SPARTAN/Service+Permissions\n{{ $user := .user }}\n{{ range $name, $val := $user }}\n{{ $val.name }}:\n    owner: {{ bool $val.owner false }}\n    write: {{ bool $val.write false }}\n    publish: {{ bool $val.publish false }}\n{{ end }}\n", "data": {"user": [{"name": "fei.chen", "publish": true, "write": true}, {"name": "bill.li", "publish": true, "write": true}, {"name": "renshi.bao", "owner": true}, {"name": "jianguo.wang", "publish": true, "write": true}, {"name": "pras<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "owner": true}, {"name": "sky.faber", "owner": true}, {"name": "jacob.yu", "publish": true, "write": true}, {"name": "varun.kansal", "publish": true, "write": true}, {"name": "yang.xiang", "publish": true, "write": true}, {"name": "bo.wang", "publish": true, "write": true}, {"name": "phil.trinh", "publish": true, "write": true}, {"name": "linhan.yong", "publish": true, "write": true}, {"name": "nitesh.thali", "publish": true, "write": true}]}, "hash": ":global.e6XmiayvxH6lIcRhOWyfo_6nmwY=:template.XauZNYS5CACMXgbuNKGM9MMUvF8="}, "always_recycle_phone_number_group": {"template": "{{ string .phoneList }}\n\n", "data": {"phoneList": "14804949802,14255050974,14256156142,12063493245,60187704120,60163073488,14257532418,14254490893,87134863,14256918110"}, "hash": ":global._Q1j4azOBuKy0aOgsp9GZeWSA0U=:global.stg.J3PRgrHhsVyL68ALvjDWun049EM=:template.pCKRoaKt0vAOSGLit3j1kAMyAyc="}, "canSendDriverRemarkOnLowQualityProfilePic": {"template": "{{ bool .mode }}", "data": {"mode": true}, "hash": ":global.PjthdWZ_qd_nklav7GGLTLQVCJA=:global.stg.y3z_0SsnMPGAlWUgZGWvseE9HdQ=:template.pFC3E9JAg-19JiBGOg7cmUwRE-A="}, "captcha:v2:allowed_actions_config": {"template": "action_cfgs: {{ range $idx, $action_cfg := .action_cfgs }}   \n    '{{ $action_cfg.name }}':\n        desc: '{{ $action_cfg.desc }}'\n        rollout_percent: {{ $action_cfg.rollout_percent }}      \n{{ end }}\n", "data": {"action_cfgs": [{"desc": "captcha V2 config for ux testing for endpoint /v1/phone/otp", "name": "8c40aab965e9449483b9ff3aabcfaa79./v1/phone/otp", "rollout_percent": 100}, {"desc": "captcha V2 config for ux testing for endpoint /v1/phone/otp", "name": "08044981144746ec80a870c605fe705b./v1/phone/otp", "rollout_percent": 100}, {"desc": "captcha V2 config for Grab Food client for endpoint /v1/phone/otp", "name": "21f57cc3249144bb92377f3a4d9a6d63./v1/phone/otp", "rollout_percent": 100}, {"desc": "captcha V2 config for ux testing for endpoint /v1/oauth2/otp", "name": "8c40aab965e9449483b9ff3aabcfaa79./v1/oauth2/otp", "rollout_percent": 100}, {"desc": "captcha V2 config for ux testing for endpoint /v1/oauth2/otp", "name": "08044981144746ec80a870c605fe705b./v1/oauth2/otp", "rollout_percent": 100}, {"desc": "captcha V2 config for Grab Food client for endpoint /v1/oauth2/otp", "name": "21f57cc3249144bb92377f3a4d9a6d63./v1/oauth2/otp", "rollout_percent": 0}]}, "hash": ":global.eIH-e0KoAVQZ91FGsa6nXTczRqM=:global.stg.1bQeTM6hux6RerqniNFv5n4nRyE=:template.-jYbZUcGZYDMBEZjnhUf7mvYfc0="}, "captcha:v2:domain": {"template": "{{ string .value }}", "data": {"value": "stg-myteksi.com"}, "hash": ":global.CanVTS11z7BUL2kG6nPj52KfHiE=:global.stg.8vpwvhKeGQ_rVxrvwpa7IsGwx5A=:template.KYaLQxFAf7eAFTln96KWIABn9Dg="}, "captcha:v2:enabled": {"template": "{{ bool .enable }}", "data": {"enable": true}, "hash": ":global.NCtXh84dcSGz4zwsvLF1cRWma9Y=:global.stg.-GAIbeoPwdheDfxt-QSGelCK9BY=:template.JiOcS0p7bNVseSIKgTM8R2Njth8="}, "captcha:v2:secret": {"template": "{{ decryptWith .kms .enc }}", "data": {"enc": "dev-grab-id|NP+BAwEBB3BheWxvYWQB/4IAAQMBA0tleQEKAAEFTm9uY2UB/4QAAQdNZXNzYWdlAQoAAAAZ/4MBAQEJWzI0XXVpbnQ4Af+EAAEGATAAAP4BDf+CAf+nAQEBAHjjot9jacqRz+QcCAjhvC4qz18k6ZBdyzqOHIipm7mchAAAAH4wfAYJKoZIhvcNAQcGoG8wbQIBADBoBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDHeswSuJvnavm5eP4wIBEIA7m702T/TmcpKV9+QGZ9RKYGd9eK0IY9Zwq7oWfABC7EEFi7rniGBITvcy4AS8SKnjnlgmJQzVOtkllRwBGP/q/+7/gf/cJv+ZXzZGev/L/58a/9UO/4z/9/+xEP+XaiZFVgE4pO35XvNGnvTHlC/d1W3sQ82n82NobiKgkBZJb5f6F3QepGaeTjLU40s++o2xU5Els1njQRRUblwA", "kms": "dev-grab-id"}, "hash": ":global.bHYmVn8eIyHC8DMDFVMKC5ShxyA=:global.stg.Ha6UFZdcxRr-lHDbiMUMAXYuGhU=:template.x1Xu41LT8565wT_X4iiNHoKKiMQ="}, "captcha:v2:sitekey": {"template": "{{ string .value }}", "data": {"value": ""}, "hash": ":global.CanVTS11z7BUL2kG6nPj52KfHiE=:global.stg.:template.KYaLQxFAf7eAFTln96KWIABn9Dg="}, "captcha:v3:domain": {"template": "{{ string .value }}", "data": {"value": "stg-myteksi.com"}, "hash": ":global.CanVTS11z7BUL2kG6nPj52KfHiE=:global.stg.8vpwvhKeGQ_rVxrvwpa7IsGwx5A=:template.KYaLQxFAf7eAFTln96KWIABn9Dg="}, "captcha:v3:enabled": {"template": "{{ bool .enable }}", "data": {"enable": true}, "hash": ":global.NCtXh84dcSGz4zwsvLF1cRWma9Y=:global.stg.-GAIbeoPwdheDfxt-QSGelCK9BY=:template.JiOcS0p7bNVseSIKgTM8R2Njth8="}, "captcha:v3:secret": {"template": "{{ decryptWith .kms .enc }}", "data": {"enc": "dev-grab-id|NP+BAwEBB3BheWxvYWQB/4IAAQMBA0tleQEKAAEFTm9uY2UB/4QAAQdNZXNzYWdlAQoAAAAZ/4MBAQEJWzI0XXVpbnQ4Af+EAAEGATAAAP4BDf+CAf+nAQEBAHjjot9jacqRz+QcCAjhvC4qz18k6ZBdyzqOHIipm7mchAAAAH4wfAYJKoZIhvcNAQcGoG8wbQIBADBoBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDKBTVIEvEsHiSirjTgIBEIA7E8XskHtJqDF07NwbFRFzDrQS9m8bATdr8IdPQdQYRuJazKuoSxMLvLiGD7Wkp1cmJ4IOQZww7SN8pgIBGAD/ov/2Kz13BWNz/49o/6L/7//mPv+q/6v/nQZf/8n/hP/WHgE4WRKEi5A8dh9bNoB1VB7lgzT+AE402tiYvEofiG3dehZAvqZOdeXcG3BghnRqCSTEwjNMr1R5UkIA", "kms": "dev-grab-id"}, "hash": ":global.bHYmVn8eIyHC8DMDFVMKC5ShxyA=:global.stg.Ob9iHKGzqmG4I3bN5rGMixHtzg8=:template.x1Xu41LT8565wT_X4iiNHoKKiMQ="}, "captcha:v3:sitekey": {"template": "{{ string .value }}", "data": {"value": ""}, "hash": ":global.CanVTS11z7BUL2kG6nPj52KfHiE=:global.stg.:template.KYaLQxFAf7eAFTln96KWIABn9Dg="}, "deactivateAccountConfig": {"template": "{\n  \"producerThrottledBatchCount\" : {{ int .producerThrottledBatchCount }},\n  \"producerThrottledBatchTime\" : {{ int .producerThrottledBatchTime }},\n  \"deactivateShadowMode\" :  {{ bool .deactivateShadowMode }},\n  \"deactivateCacheTTLSeconds\" : {{ int .deactivateCacheTTLSeconds }},\n  \"deactivateMaxMessageAttempts\" : 2,\n  \"deactivateNextMessageDurationSeconds\" : {{ int .deactivateNextMessageDurationSeconds }},\n  \"deactivateMaxFailedAttempts\" : 3,\n  \"schedulerRoutineCount\" : 8,\n  \"maxNumberOfMessagesSQS\" : 10,\n  \"waitTimeSecondsSQS\" : 20,\n  \"visibilityTimeoutSQS\" : 60\n}", "data": {"deactivateCacheTTLSeconds": 7200, "deactivateNextMessageDurationSeconds": 300, "deactivateShadowMode": false, "producerThrottledBatchCount": 10000, "producerThrottledBatchTime": 180}, "hash": ":global.4apdPiikV0vOImKR4uZKTPHRDiU=:global.stg.A9I6JU1g4t9E1mq8CHg0mRydtOg=:template.i8mM5g5NvA8m-zeWCDO334h3KAQ="}, "deactivateAccountEnabledFeature": {"template": "{{ bool .enabled }}\n", "data": {"enabled": true}, "hash": ":global.KavstVz5q3T209i8fVHJE7hIjik=:global.stg.-xMPX7B2U52h6D75KByRJt_VyVg=:template.5kcwlTU5NXnvs6tyjiWFwFEufNY="}, "enable-email-verification": {"template": "{{ bool .value}}", "data": {"value": true}, "hash": ":global.O6LNNbEehXAfXlWEsMlaNW7XX_g=:global.stg.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "enable-grabkit": {"template": "{{ bool .enable }}\n", "data": {"enable": true}, "hash": ":global.NCtXh84dcSGz4zwsvLF1cRWma9Y=:global.stg.-GAIbeoPwdheDfxt-QSGelCK9BY=:template.16YkFby1TdM0GuPkmbyuDuOpTxY="}, "enable_sync_pax_phone": {"template": "{{ bool .enable }}", "data": {"enable": true}, "hash": ":global.NCtXh84dcSGz4zwsvLF1cRWma9Y=:global.stg.-GAIbeoPwdheDfxt-QSGelCK9BY=:template.JiOcS0p7bNVseSIKgTM8R2Njth8="}, "enable_vn_dup_prevention": {"template": "{{ bool .value}}", "data": {"value": true}, "hash": ":global.O6LNNbEehXAfXlWEsMlaNW7XX_g=:global.stg.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "face_recognition_rollout": {"template": "{{ int .value }}\n", "data": {"value": 100}, "hash": ":global.3xiRTwCSbSvU-TwuVI8qlk6qToc=:global.stg.syjBrIyB5cdMmItaCCJ50jJcX9k=:template.ogjREJWeuP2798osN9j7OrlL9Xk="}, "face_recognition_s3_upload_enabled": {"template": "{{ bool .value}}\n", "data": {"value": true}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:template.39WX-LGyC4itcwCCsLpjTzZIKbY="}, "face_recognition_shadow_rollout": {"template": "{{ int .value }}", "data": {"value": 0}, "hash": ":global.3xiRTwCSbSvU-TwuVI8qlk6qToc=:global.stg.bKI6f4LEFLtdNwyqI2E1z2IRcrA=:template.RZf0MUURZFAeaEIiT4lKEwPO7-w="}, "faceauth_match_threshold_key": {"template": "{{string .value}}", "data": {"value": "'1e-5'\n"}, "hash": ":global.1sYmwkocZnir_IXZbcn5FJLl5CY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.WvCTNQLBxJgPZwSa5xyvy_JSnpM="}, "fallbackKMSKey": {"template": "{{ string .value }}", "data": {"value": "arn:aws:kms:ap-southeast-1:109025824511:key/5f9bb86f-2c46-4c0a-aeaf-28e0b42c1698"}, "hash": ":global.yUaPSYxFLZxmp9vRDOjmzyzRGXU=:global.stg.MCUHRMP5CfmX_9WK-mdgCwSPahI=:template.KYaLQxFAf7eAFTln96KWIABn9Dg="}, "fallbackKMSRegion": {"template": "{{ string .value}}", "data": {"value": "ap-southeast-1"}, "hash": ":global.35JDqa8I23IYBf_103PMj8h8pss=:global.stg.jK4N2-aIB7JGsMiivpMAUYBVsZI=:template.lsXd29zLUYpIDxMMByvD9Du4zzI="}, "fb_uat_fresh_check_enabled": {"template": "{{ bool .enable }}\n", "data": {"enable": false}, "hash": ":global.SisfFvW4TR2CgLvsecwF0yfl8J0=:global.stg.NCtXh84dcSGz4zwsvLF1cRWma9Y=:template.16YkFby1TdM0GuPkmbyuDuOpTxY="}, "gid:login:check_social_login_user_id": {"template": "{{ bool .check }}\n", "data": {"check": false}, "hash": ":global.5TsWfZsUKTZV0itq7O-TUF9HKts=:global.stg.:template.Wl8laD-d4wQuJkb-1KSGe6p7ymA="}, "gid:token:issued_at_time_correction_sec": {"template": "{{ int .correction_sec }}\n", "data": {"correction_sec": 3}, "hash": ":global.qcE9DmvD0_bxjoJ5bjGlUN9CvGQ=:global.stg.uIVHlY8lWQgfdmdYPHS3VDJAJZo=:template.ZkWVt_2fmZytoo5CixuSNmQvbiU="}, "gid_admin_deactivate_account_shadow_mode": {"template": "{{ bool .value }}\n", "data": {"value": false}, "hash": ":global.5zvIaMb8CawbgQJ3yIPOld5RK6g=:global.stg.5zvIaMb8CawbgQJ3yIPOld5RK6g=:template.-ngQ1LFLAtyCKSyY4wCxNnBmqAk="}, "gid_admin_deactivate_account_tables": {"template": "{{ range $idx, $t := .tables }}\n  - {{$t.name}}\n{{end}}\n", "data": {"tables": [{"name": "messaging_email_verifications|user_id"}, {"name": "messaging_email_verifications_history|user_id"}, {"name": "users_metadata|user_id"}, {"name": "google_info|user_id"}, {"name": "google_info_history|user_id"}, {"name": "phone_numbers|user_id"}, {"name": "phone_number_verifications|user_id"}, {"name": "phone_numbers_history|user_id"}, {"name": "emails|user_id"}, {"name": "emails_history|user_id"}, {"name": "facebook_info|user_id"}, {"name": "facebook_info_history|user_id"}, {"name": "services|user_id|service_id"}]}, "hash": ":global.G6_8YXCyDpg7PMyxtmNsjLslSDQ=:global.stg.:template.uel1EJwoCdM7_NDOJt3QDbXVElU="}, "gid_admin_services_email_request_verify_shadow_mode": {"template": "{{ bool .enable }}\n\n\n", "data": {"enable": false}, "hash": ":global.LGJ_-XZVD1JkkJswDF7b4LstPYs=:global.stg.6oP8xrHfo_fyXhklzCFmDdkJMA8=:template.2SQRGD1WNoYT4W3kGxWNbkQD0l4="}, "gid_admin_services_send_deactivation_email_shadow_mode": {"template": "{{ bool .enable }}\n", "data": {"enable": false}, "hash": ":global.5iHufFPAlJbIcDJE8I_7KPnNL68=:global.stg.NCtXh84dcSGz4zwsvLF1cRWma9Y=:template.16YkFby1TdM0GuPkmbyuDuOpTxY="}, "gid_admins": {"template": "{{ range $idx, $admin := .admins }}\n  {{$admin.name}} : {{$admin.key}}\n{{end}}", "data": {"admins": [{"key": "LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUE3QlVVby9VSFJQUHkrZHlDc0Qzcwp1UlEyNlVDWXAxaU0xSVYyUC9uTDJVd1RvbmJPc3JuclJEZ1hXUng4SVZuMmtHTEhjbjZGY1FyeHBnMkFERU5ICkZtZlhWZFZpT0RISjV3R2t6WmMyY1NiSEJscEdqOC9BRlVZc3N0QUxCdGp5TzlGL1VVZ2dXQ2s4UDJPdktEQ0kKbktiTWdLZi9EdzdUU3pkOG1KcXNBYXJBcFFTTDU4VW5YbmhDM01iSXZZWXg1R0hBRUt1RTNXaW0xMnlaRnZsUApEK2h6aTUxSWRocEg0eHRhTGpjMnlsaU80OEZ1MVcyUG42RXRtYlV5MzhlcW1qYjFXcmdRMm9NZ1plSVhMUTIrCnU1NERxU3hrN3RjSUFqNnRGTDhrYWtlWkQ0WTdIZ1NzZEJEbU0vdFVrd0YvUFpyWThDQzhtUFFrZjRoaWlnbmsKMlFJREFRQUIKLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0tCg==", "name": "fei.chen"}, {"key": "LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUFsVi9JLzJSMnRMdHpXUGd6ODBzVApSSHlkZ0pDOUcvd25kSk9pNjRnM1h3Zm5yVlN3Qk1tSzVWSXBwZWNwcjR1eDAyYXRxSHZlbUFOVDlPTThRczFPCjc5RlpBZ0hEZjIxdlZmc3dKSFcwVmZ5aUFxUE1XaG5oNmp4UG5QeStJdk5Vbmh0RmdQeGV1VWhHcnpPN2lGNGkKOEFiaENubnNFOFlFcVpUanYzdjN5eDR0ZmhROUFCQklQbENteUlSNm1TK3FhYThmTjRnanNpQmJ2VlRucEttTQp6S2c0VTFXZDNhQ3Z4T3ZWT2NOYlpGMU5MS2NDQS9CNGtlT3pQZ0VPekZaM2syZDkraHRtOTlWQkNYK2tqMm1tCnplM05JUm5Qcy9OYWV3MmxSTjJZd0M0WnNDdEVNU1k5U0lvZGpYMDR3ZzlDUVNiU2gvRnVkMStONE50aC9tWHIKYXdJREFRQUIKLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0tCg==", "name": "renshi.bao"}, {"key": "LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUF5VDh4eXBwc0srQ2xiTHEzM0lNawpWQWdHczBPRkNtY1FoMFJWaTFpZURDQm8xbEdOdGp5cXhYYjhYeTcycE1oTTZzcVp6bi9oUkVjN0xobjdJQlA5CmZ2M2U5bTJscHJjRS93TlpjeVR2TkE4WTdseHovWnpYL2gyYUZ4SXRDYUY3U0VXL2l2b0QxQnh1U0JYOHhXbi8KNEtiUFk4KzlqU1FEYlVnQjVYclZHME9oaWZ0RmxvNDBMaWI1dUFSUzJmV0cxazd4OTY5cEtRL1pIRUR4bUc4RQp3djVob3FHdW5KMFlXUUdjSVZXL2NtWWZIQTI3Si9DWlNMRU00dElkM2RkWE9YNytpMTdJbmlJejl6cEdEV1VFClVJRWkyUTFRSFNnR05Xbmx4KzVFWDl2aFYybHA3cEdsSGZscDN3a2JuS0xsYjBkQTZ2VVhXUGp3eEwrZmZsTDEKYlFJREFRQUIKLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0tCg==", "name": "junming.khong"}]}, "hash": ":global.h2NT5Alfc_GVEBae2I45sW_qskw=:global.stg.tRfONyY6zrdGwnhqE3fD3XAFXus=:template.c1yS8_pojo6c0sSaQ4UTeSkcCXk="}, "gid_audit_strategy_disable_elastic_search": {"template": "{{bool .value}}", "data": {"value": true}, "hash": ":global.UXPNRiDwwr7k_e28BkBXW2657Fk=:global.stg.:template.1KkG44n-u7AJnzgLwkNYZ0wa3YY="}, "gid_biometric_auth_allowed_signing_seconds_skew": {"template": "{{ int .value }}", "data": {"value": 3600}, "hash": ":global.wdu50zxnYt1l86vUmBwTWdIkaEc=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.RZf0MUURZFAeaEIiT4lKEwPO7-w="}, "gid_biometric_auth_enabled": {"template": "{{ bool .value}}", "data": {"value": true}, "hash": ":global.O6LNNbEehXAfXlWEsMlaNW7XX_g=:global.stg.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_block_emoji_enabled": {"template": "{{ bool .value}}", "data": {"value": true}, "hash": ":global.O6LNNbEehXAfXlWEsMlaNW7XX_g=:global.stg.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_block_request_with_emoji": {"template": "{{bool .value}}", "data": {"value": false}, "hash": ":global.O6LNNbEehXAfXlWEsMlaNW7XX_g=:global.stg.O6LNNbEehXAfXlWEsMlaNW7XX_g=:template.1KkG44n-u7AJnzgLwkNYZ0wa3YY="}, "gid_cache_double_write_cluster_config": {"template": "addr: {{ string .addr }}\npoolSize: {{ int .poolSize }}\nreadTimeoutInSec: {{ int .readTimeoutInSec }}\nwriteTimeoutInSec: {{ int .writeTimeoutInSec }}\nidleTimeoutInSec: {{ int .idleTimeoutInSec }}\nreadOnlyFromSlaves: {{ bool .readOnlyFromSlaves }}", "data": {"addr": "grab-id-redis.identity.stg.g-bank.app:6379", "idleTimeoutInSec": 2, "poolSize": 300, "readOnlyFromSlaves": true, "readTimeoutInSec": 2, "writeTimeoutInSec": 2}, "hash": ":global.YKSAvgJNj8Ej9pncGj0ejX5wri8=:global.stg.ZZB-lA5QfHnsKk65-JyUNCg3rZg=:template.kraZnkdyYEjiijMxY33vwdaSD0o="}, "gid_cache_enable_double_write": {"template": "{{bool .value}}", "data": {"value": false}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.5zvIaMb8CawbgQJ3yIPOld5RK6g=:template.1KkG44n-u7AJnzgLwkNYZ0wa3YY="}, "gid_cache_enable_replay": {"template": "{{bool .value}}", "data": {"value": false}, "hash": ":global.O6LNNbEehXAfXlWEsMlaNW7XX_g=:global.stg.:template.1KkG44n-u7AJnzgLwkNYZ0wa3YY="}, "gid_cache_init_db_replay_provider": {"template": "{{bool .value}}", "data": {"value": false}, "hash": ":global.O6LNNbEehXAfXlWEsMlaNW7XX_g=:global.stg.:template.1KkG44n-u7AJnzgLwkNYZ0wa3YY="}, "gid_cache_init_double_write_cache_provider": {"template": "{{bool .value}}", "data": {"value": false}, "hash": ":global.O6LNNbEehXAfXlWEsMlaNW7XX_g=:global.stg.5zvIaMb8CawbgQJ3yIPOld5RK6g=:template.1KkG44n-u7AJnzgLwkNYZ0wa3YY="}, "gid_catwalk_client_config": {"template": "host: '{{ string .host }}'\ntimeoutInMs: {{ int .timeoutInMs }}\nmaxConcurrent: {{ int .maxConcurrent }}\nerrorThresholdPercent: {{ int .errorThresholdPercent }}\n", "data": {"errorThresholdPercent": 50, "host": "http://grab-identity.catwalk-k8s.stg-myteksi.com", "maxConcurrent": 100, "timeoutInMs": 2000}, "hash": ":global.q-jIjqdqpNl96Sz2znlo213UYWM=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.bc2QQ_YmiAHK5L8tUaGQBCQrN44="}, "gid_challenge_id_mapper_expiry_in_seconds": {"template": "{{ int .value }}", "data": {"value": 300}, "hash": ":global.4TtQZsEuZeiJvPvNzc-zx76QrSM=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.RZf0MUURZFAeaEIiT4lKEwPO7-w="}, "gid_cloud_db_credential_url": {"template": "{{string .value}}", "data": {"value": "https://stg-vaultapi01.corp.grabpay.com/v1/database/creds?t=rdse"}, "hash": ":global._jampiCe49_akxHxZ0i1Xx4oNnw=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.WvCTNQLBxJgPZwSa5xyvy_JSnpM="}, "gid_cloud_name": {"template": "{{string .value}}", "data": {"value": "digibank"}, "hash": ":global.Juqn5SxX8VuusxZoFiokjVlF8xA=:global.stg.Juqn5SxX8VuusxZoFiokjVlF8xA=:template.WvCTNQLBxJgPZwSa5xyvy_JSnpM="}, "gid_conflict_on_partner_app_error": {"template": "{{ bool .value}}\n", "data": {"value": true}, "hash": ":global.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:global.stg.:template.39WX-LGyC4itcwCCsLpjTzZIKbY="}, "gid_counter_service_client_config": {"template": "serverAddress: '{{ string .serverAddress }}'\nserviceDiscoveryEnabled: {{ bool .serviceDiscoveryEnabled }}\npoolMaxOpen: {{ int .poolMaxOpen }}\ndialTimeoutInMs: {{ int .dialTimeoutInMs }}\ndefaultHealthCheckFrequencyInSec: {{ int .defaultHealthCheckFrequencyInSec }}\ndefaultHealthCheckTimeoutInSec: {{ int .defaultHealthCheckTimeoutInSec }}\ntimeout: {{ int .timeout }}\nmax_concurrent_requests: {{ int .max_concurrent_requests }}\nerror_percent_threshold: {{ int .error_percent_threshold }}\n", "data": {"defaultHealthCheckFrequencyInSec": 2, "defaultHealthCheckTimeoutInSec": 2, "dialTimeoutInMs": 2000, "error_percent_threshold": 50, "max_concurrent_requests": 100, "poolMaxOpen": 10, "serverAddress": "trust-counter-service.stg-myteksi.com:8087", "serviceDiscoveryEnabled": false, "timeout": 2000}, "hash": ":global.BkCSyKO68MYJqnddDZJNuu8bvvA=:global.stg.XkuBL1gqXDjgG1lKFdcTHDB13co=:template.101eFivi824Uop7mdnaCQMMbcgY="}, "gid_counter_template_id:pax_all_bookings": {"template": "{{ int .id }}", "data": {"id": 25}, "hash": ":global.aJg6QBe9HLpOVvS0cIM5s1F38XI=:global.stg.-ROCeVzwKn1pFAmhRM4lHkQ5f64=:template.f5MRsocm0B8zVmedcTjOUEkgGig="}, "gid_counter_template_id:pax_all_bookings_diff_country": {"template": "{{ int .id }}", "data": {"id": 15}, "hash": ":global.3ZNiDwdlID9HY5muZVpvYebgPqk=:global.stg.LxARtV9X6XOI4Ysf4G2s6_yF34I=:template.f5MRsocm0B8zVmedcTjOUEkgGig="}, "gid_dax_selfie_rollout_by_country": {"template": "{{ bool .enable }}", "data": {"enable": true}, "hash": ":global.NCtXh84dcSGz4zwsvLF1cRWma9Y=:global.stg.-GAIbeoPwdheDfxt-QSGelCK9BY=:template.JiOcS0p7bNVseSIKgTM8R2Njth8="}, "gid_dax_selfie_rollout_countries": {"template": "{{ string .value}}", "data": {"value": "5,6"}, "hash": ":global.pIP-2yJkUYO--V2KKBQVSYSgpHk=:global.stg.Em-k78j25LmuEBJnOeMnjtPxu_M=:template.lsXd29zLUYpIDxMMByvD9Du4zzI="}, "gid_ddb_client_cfg_pax_activity": {"template": "table: '{{ string .table }}'\ntimeout_ms: {{ int .timeout_ms }}\nerror_percent: {{ int .error_percent }}\nmax_concurrent_request: {{ int .max_concurrent_request }}\nmax_vol_percent_threshold: {{ int .max_vol_percent_threshold }}\nsleep_window_ms: {{ int .sleep_window_ms }}\nmax_queue_size: {{ int .max_queue_size }}\n", "data": {"error_percent": 50, "max_concurrent_request": 100, "max_queue_size": 1000, "max_vol_percent_threshold": 20, "sleep_window_ms": 5000, "table": "stg-pax-grab-id-last-activity", "timeout_ms": 500}, "hash": ":global.IRMTlY9HP7Tvz-T-ouWX5_9K268=:global.stg.zcWiKFmlk2BZ11jC6vDEWZ4_JDo=:template.crmhDx-PJ_gqKwlJfyUHubVJLqw="}, "gid_defer_deactivate_endpoint": {"template": "{{ string .value }}", "data": {"value": "https://weblogin.stg-myteksi.com/emails/defer-deactivate?token=%v"}, "hash": ":global.bGhNuczLdbgXLSYhhjAHR737UwU=:global.stg.vWSoiiLh6B5Fph8DnCIyK7UvQ4Y=:template.KYaLQxFAf7eAFTln96KWIABn9Dg="}, "gid_disable_otp_num_digits": {"template": "{{ bool .value }}", "data": {"value": false}, "hash": ":global.KK2SLsgVFUWYrLtJMgzpT0dcV_E=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.agqciiThdyC8wUwL4ZJbPn3dsso="}, "gid_down": {"template": "{{ bool .down }}\n", "data": {"down": false}, "hash": ":global.7hgS0zt4cw2EA4UvQ_g8QNm3mdw=:global.stg.7hgS0zt4cw2EA4UvQ_g8QNm3mdw=:template.i6PCBU2CJs6SMNBUgKuSSlQ2bXs="}, "gid_email_blocked_domain_list": {"template": "{{ .strList }}", "data": {"strList": "[    \"mailinator.com\",    \"pookmail.com\",    \"spamgourmet.com\",    \"jetable.org\",    \"dodgeit.com\",    \"10minutemail.com\",    \"trashmail.net\",    \"temporaryinbox.com\",    \"sneakemail.com\",    \"mailexpire.com\",    \"dodgit.com\",    \"sizlopedia.com\",    \"slopsbox.com\",    \"guerrillamail.com\",    \"gishpuppy.com\",    \"makemetheking.com\",    \"whyspam.me\",    \"kasmail.com\",    \"meltmail.com\",    \"spambox.us\",    \"e4ward.com\",    \"mailcatch.com\",    \"yousendit.com\",    \"tempinbox.com\",    \"spammotel.com\",    \"tempemail.net\",    \"bugmenot.com\",    \"nervmich.net\",    \"whspr.me\",    \"spambog.com\",    \"dispostable.com\",    \"tempalias.com\",    \"filzmail.com\",    \"yopmail.com\",    \"tinymail.me\",    \"bluebottle.com\",    \"akapost.com\",    \"scr.im\",    \"sitesell.com\",    \"recaptcha.net\",    \"endjunk.com\",    \"sendanonymousemail.net\",    \"mail2web.com\",    \"spamcop.net\",    \"hushmail.com\",    \"mailbigfile.com\",    \"otherinbox.com\",    \"thanksno.com\",    \"boxbe.com\",    \"contactify.com\",    \"soodonims.com\",    \"explodemail.com\",    \"senderbase.org\",    \"hivelogic.com\",    \"mailtoencoder.com\",    \"openspf.org\",    \"spamhaus.org\",    \"nuclearelephant.com\",    \"spamihilator.com\",    \"spamfighter.com\",    \"makeuseof.com\",    \"jottings.com\",    \"bigstring.com\",    \"mxtoolbox.com\",    \"fuzzmail.org\",    \"spampoison.com\",    \"wbwip.com\",    \"lettermelater.com\",    \"codehouse.com\",    \"postini.com\",    \"destructingmessage.com\",    \"goodmailsystems.com\",    \"spamarrest.com\",    \"6url.com\",    \"knujon.com\",    \"mailingcheck.com\",    \"mierdamail.com\",    \"fastmail.fm\",    \"spypig.com\",    \"spamspan.com\",    \"junkbusters.com\"]"}, "hash": ":global.fsUw6ORwMZT9Fh946G7kcfQCAYk=:global.stg.9lC4Y_YMbB0o4uqUCG_XPy8BA8Y=:template.cFtG4yV2_oaH1zhEEnx5lL6g9ds="}, "gid_email_verification_add_social_email": {"template": "{{bool .value}}", "data": {"value": true}, "hash": ":global.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:global.stg.:template.1KkG44n-u7AJnzgLwkNYZ0wa3YY="}, "gid_email_verification_enabled": {"template": "{{bool .value}}", "data": {"value": true}, "hash": ":global.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:global.stg.:template.1KkG44n-u7AJnzgLwkNYZ0wa3YY="}, "gid_email_verification_send_email": {"template": "{{bool .value}}", "data": {"value": true}, "hash": ":global.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:global.stg.:template.1KkG44n-u7AJnzgLwkNYZ0wa3YY="}, "gid_email_verification_send_email_report": {"template": "{{bool .value }}", "data": {"value": true}, "hash": ":global.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:global.stg.:template.JiWK9emWNKKDIOXrITVkumwxLt8="}, "gid_enable_concedo_authorization": {"template": "{{ bool .value }}\n", "data": {"value": true}, "hash": ":global.O6LNNbEehXAfXlWEsMlaNW7XX_g=:global.stg.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:template.-ngQ1LFLAtyCKSyY4wCxNnBmqAk="}, "gid_enable_crudds_append_remark": {"template": "{{ bool .enabled }}", "data": {"enabled": true}, "hash": ":global.uLx5K071t3uKEem390c38dWPXPY=:global.stg.-xMPX7B2U52h6D75KByRJt_VyVg=:template.V9zi_N_gYu1eQdfUImZv__yWmFQ="}, "gid_enable_dax_selfie_auth": {"template": "{{ bool .value}}", "data": {"value": true}, "hash": ":global.O6LNNbEehXAfXlWEsMlaNW7XX_g=:global.stg.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_enable_face_genuine_detection": {"template": "{{ bool .value}}", "data": {"value": true}, "hash": ":global.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:global.stg.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_enable_ml_recycle_check": {"template": "{{ bool .enable }}", "data": {"enable": true}, "hash": ":global.NCtXh84dcSGz4zwsvLF1cRWma9Y=:global.stg.-GAIbeoPwdheDfxt-QSGelCK9BY=:template.JiOcS0p7bNVseSIKgTM8R2Njth8="}, "gid_enable_new_login_flow_for_drivers": {"template": "{{ bool .enable }}", "data": {"enable": true}, "hash": ":global.5h2YY3vSmX2nQ093pqnVsRN1P-U=:global.stg.5h2YY3vSmX2nQ093pqnVsRN1P-U=:template.JiOcS0p7bNVseSIKgTM8R2Njth8="}, "gid_enable_pax_activity_tracker": {"template": "{{ bool .enable }}", "data": {"enable": true}, "hash": ":global.NCtXh84dcSGz4zwsvLF1cRWma9Y=:global.stg.-GAIbeoPwdheDfxt-QSGelCK9BY=:template.JiOcS0p7bNVseSIKgTM8R2Njth8="}, "gid_enable_tracking_dax_selfie_by_country": {"template": "{{ bool .value}}", "data": {"value": true}, "hash": ":global.O6LNNbEehXAfXlWEsMlaNW7XX_g=:global.stg.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_enforce_dax_selfie_auth": {"template": "{{ bool .enable }}", "data": {"enable": true}, "hash": ":global.NCtXh84dcSGz4zwsvLF1cRWma9Y=:global.stg.-GAIbeoPwdheDfxt-QSGelCK9BY=:template.JiOcS0p7bNVseSIKgTM8R2Njth8="}, "gid_enforce_dax_selfie_auth_rollout_percentage": {"template": "{{ int .value }}", "data": {"value": 100}, "hash": ":global.bKI6f4LEFLtdNwyqI2E1z2IRcrA=:global.stg.syjBrIyB5cdMmItaCCJ50jJcX9k=:template.RZf0MUURZFAeaEIiT4lKEwPO7-w="}, "gid_enforce_empty_jwt_lid_claim": {"template": "{{bool .enable}}\n", "data": {"enable": true}, "hash": ":global.LGJ_-XZVD1JkkJswDF7b4LstPYs=:global.stg.5iHufFPAlJbIcDJE8I_7KPnNL68=:template.QlP39xw5ZJdxciduBuJYDczu394="}, "gid_enforce_image_orientation": {"template": "{{ bool .value}}", "data": {"value": true}, "hash": ":global.O6LNNbEehXAfXlWEsMlaNW7XX_g=:global.stg.R0GMwneE80rH_whPt095ke0_S5E=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_enforce_six_digit_otp": {"template": "{{ bool .enable}}", "data": {"enable": false}, "hash": ":global.6oP8xrHfo_fyXhklzCFmDdkJMA8=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.IMGrkHSS_8kDaf3waNzEYCyDwW8="}, "gid_eventquery_fieldcontrol": {"template": "'{{ string .value }}'", "data": {"value": "{\"eventFields\":[{\"eventType\":\"LOGIN_EVENT\",\"fields\":[\"ipAddress\",\"loginMethod\",\"clientType\",\"clientID\",\"deviceID\",\"phoneExisting\",\"phoneRecycled\",\"phoneNewAccountEligible\",\"phoneBanned\"]},{\"eventType\":\"LOGOUT_EVENT\",\"fields\":[\"logoutReason\"]},{\"eventType\":\"MFA_EVENT\",\"fields\":[\"ipAddress\",\"deviceID\",\"countryCode\",\"MFAType\",\"state\",\"description\",\"clientSelfieEnabled\",\"isMocaOtp\",\"valid\",\"pinActive\"]},{\"eventType\":\"OTP_EVENT\",\"fields\":[\"authType\",\"clientType\",\"clientID\",\"existing\",\"recycled\",\"newAccountEligible\",\"banned\"]},{\"eventType\":\"PIN_EVENT\",\"fields\":[\"action\",\"pinActive\"]},{\"eventType\":\"PROFILE_EVENT\",\"fields\":[\"profileAction\"]},{\"eventType\":\"REGISTER_EVENT\",\"fields\":[\"ipAddress\",\"loginMethod\",\"clientType\",\"clientID\"]}]}"}, "hash": ":global.CanVTS11z7BUL2kG6nPj52KfHiE=:global.stg._RmVHivlsoDnDXcZY21Wo-wg93Y=:template.4LTVYHygXHcNstr1WqJHh7taIdQ="}, "gid_eventquery_queryby_usersafeid": {"template": "{{ string .value}}", "data": {"value": "LOGOUT_EVENT, PIN_EVENT, PROFILE_EVENT"}, "hash": ":global.s4G-no1JYZRi1BriSH_07L8cg1Q=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.lsXd29zLUYpIDxMMByvD9Du4zzI="}, "gid_eventquery_size": {"template": "{{ int .value }}", "data": {"value": 1000}, "hash": ":global.Id2t6QTA7Itgpj6d9Dc35M4Ion8=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.RZf0MUURZFAeaEIiT4lKEwPO7-w="}, "gid_face_service_errors": {"template": "{{ string .errors }}\n", "data": {"errors": "INVALID_IMAGE_SIZE"}, "hash": ":global.UhXjt9UVn9JWYywj_TMAO_wcZek=:global.stg.UhXjt9UVn9JWYywj_TMAO_wcZek=:template.GKMzBvsHVB5CqMOijuxZr6-GS8A="}, "gid_fail_selfie_check_if_invalid_s3_image": {"template": "{{ bool .value }}\n", "data": {"value": true}, "hash": ":global.O6LNNbEehXAfXlWEsMlaNW7XX_g=:global.stg.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:template.-ngQ1LFLAtyCKSyY4wCxNnBmqAk="}, "gid_fail_selfie_check_if_s3_image_not_found": {"template": "{{ bool .enable }}", "data": {"enable": true}, "hash": ":global.NCtXh84dcSGz4zwsvLF1cRWma9Y=:global.stg.-GAIbeoPwdheDfxt-QSGelCK9BY=:template.JiOcS0p7bNVseSIKgTM8R2Njth8="}, "gid_fake_JWT_public_keys": {"template": "{{ range $idx, $key := .keys }}\n  {{$key.kid}} : {{$key.value}}\n{{end}}", "data": {"keys": [{"kid": "alexander<PERSON>y<PERSON><PERSON>ov", "value": "LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUEyTnZYVXlEYjVJQ3dwS2ZzUTEvUApETnVXY0I3MGFrdGIvZmlaQ292dnVNdVQ4N1NYaUJzOXl4Um1LRDYvUG1aVzA3RHBYcDVLL3FiSVpQd09HaUFpCldwVzBmeTNqWGdCWGRlbXN4T0hWSlFaQ0didC9abDcxMUtRR0IrNjMyN2FVNnRET2tWRUllVldsMDBDYVVsTXIKNloxeUpkcjdLR1IvSzQ2ZWxodDJJWnJ5YjdIWk1hcXFtR2VpN29XbCtlQnlFbnc3UjBqb1pJdERaa3ljd0VtZApvejFJVGk0ZWlkNy9YTmQvZ2lvTmhrOEpOTm9uY2toc0doenpsYllPMUVNMTdyTWI4M3RkQXdFeFI3alNmTkZTCjkyVWF2K1VheXNvV3FOajlzMVN2TzN1TjBDcGtrREZ2WDdsT0dnWG1ET2dORTVhdHlKZmQxQjdGOHZ0b1ZrTkkKNFFJREFRQUIKLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0tCg=="}, {"kid": "jianguo.wang", "value": "LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUExUFhyVWlEZ1AvS2VscnVmN3l2Tgp3YTJ1NmM4b3gvNW1kcmF6ZnhNSlRHdnVaYk96Wk9GZlNpUWlVdzMwV0tqa1NFQUF4NU9XdzhDQi9tTU5qeDlxCnIyVkFPN3kzNEwyajZYQWovNzlnbHlCWVhiNkJydXNHVFJRcE1wQ3J1WGRGL1poU1E2cy9RY0NzT1N5cGlzUTAKTGw5dlRoZ0pScHlxazhZTlkva2xrdXFYWXd6VjY2MUlFcWFFbytrQUhBZS9NNE9sVERTZW1xalZaSGZhTjhkMgpqRENWYzQxeGFJaTBSZHo1aGZYYTBhL21vaWVDZVBjelNRb3FGVTc2SUN1b3JFOVNYOHVxSVJwMXE4RUJ4RGFTCjdGZDJ6MWV4UVowYW94M1RrNGpFazVPaFYwWGlNdE5uRS95bEdCUUJDcVZ6TjVBVnRZRFRaNUFsMnI1N1JJNkgKUndJREFRQUIKLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0tCg=="}, {"kid": "jiahao.huang", "value": "LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0NCk1JSUJJakFOQmdrcWhraUc5dzBCQVFFRkFBT0NBUThBTUlJQkNnS0NBUUVBeFdkUFNtU2dSZTNoY1lQcElUakMNCnNENU03NUhxY3c5dkdpdEFSNUgxUWFGT1NNUHhRRGdwVEtGeTlXemVlWWRWbXMxeHJHVklhdmQreE5WYXhSTFMNCnZpeDA5ODU4b2tvOUc4NVA4ZGNCNkl2aEFQb2lLRFF0aXZTQTlmZXhjeU5wa3NXVFBJVVZpNnB3NTN5VnU2ZDENCmNFaWRSbmZIVTFVaVpXT2kvZHU3S2dtekRNVUthK2pkeTYrNkJFMGlWQWQ4Sy90VUdUN1VCdlJrVW5tL2FqWjcNCnJETERZeGpjUEZBdUdVeVAzL3A5Q0FKQm1lQ1lFNmR2WjRzQVhiOTV3NFVDd1JqV3dDOEpHRkp0Tk8zN0lPYlcNCkQyVk5UZXhDbGw5TEJrM0ZqZkRocENkaUs3WmNyMmdTb0o3bkMvN3lkdVhLOVZTcWtDZWc4Z0tqdjZDd2FsN1UNCnZ3SURBUUFCDQotLS0tLUVORCBQVUJMSUMgS0VZLS0tLS0="}, {"kid": "load.test", "value": "LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUEyTnZYVXlEYjVJQ3dwS2ZzUTEvUApETnVXY0I3MGFrdGIvZmlaQ292dnVNdVQ4N1NYaUJzOXl4Um1LRDYvUG1aVzA3RHBYcDVLL3FiSVpQd09HaUFpCldwVzBmeTNqWGdCWGRlbXN4T0hWSlFaQ0didC9abDcxMUtRR0IrNjMyN2FVNnRET2tWRUllVldsMDBDYVVsTXIKNloxeUpkcjdLR1IvSzQ2ZWxodDJJWnJ5YjdIWk1hcXFtR2VpN29XbCtlQnlFbnc3UjBqb1pJdERaa3ljd0VtZApvejFJVGk0ZWlkNy9YTmQvZ2lvTmhrOEpOTm9uY2toc0doenpsYllPMUVNMTdyTWI4M3RkQXdFeFI3alNmTkZTCjkyVWF2K1VheXNvV3FOajlzMVN2TzN1TjBDcGtrREZ2WDdsT0dnWG1ET2dORTVhdHlKZmQxQjdGOHZ0b1ZrTkkKNFFJREFRQUIKLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0tCg=="}, {"kid": "dax.backend.keyID1", "value": "LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUF5TktWR2JUclZXNi9ZbFNCY0ZZYQpNSm9ZVEwwYSs3MXRNanFJNVU5ZGhPVk5TaGl3UnJNYUFCOUpEYWRLcXJhMFhZT0QyaFZDWUkvRCtDd3FTQ1JEClJoY0RTcGdWQ0twOEI1enBVTThubGlKY1hpYmJHYWt5WXpPS0x4VUh2Y1BEVWJaVnBPeGwySVlodThrcDdUbVQKYUdmeEJwakJWOE03bkVWcGZ3dDhoYStOSlJwbHFwWHcveG0zbWF5N0dKalZOczVYSTRNTForaGswUkplSWJ3TgphME1DMVlIaldITjNQcVpITk53K01WOXRZK3pZMHBxMElEUFdUUkw4RGE4ZHpNaXMrZytFMktTUVNlUGFDZyt2CnhDSlB5VEI1dEhCU1YwS0U4dERqeER3S3FzeWhwNUh1N2hOTzcxblZQUWZrUnI1cUppdzBYekwvWEhBa0V3dHEKT3dJREFRQUIKLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0tCg=="}]}, "hash": ":global.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:global.stg.Bl0ChdATxXfnb4b8-mmG17Mn0Hk=:template.V3h1aUT4E9JK-vyeEtfPCkAGzUA="}, "gid_fake_google_public_keys": {"template": "{{ range $idx, $key := .keys }}\n  {{$key.kid}} : {{$key.value}}\n{{end}}", "data": {"keys": [{"kid": "alexander<PERSON>y<PERSON><PERSON>ov", "value": "LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUEyTnZYVXlEYjVJQ3dwS2ZzUTEvUApETnVXY0I3MGFrdGIvZmlaQ292dnVNdVQ4N1NYaUJzOXl4Um1LRDYvUG1aVzA3RHBYcDVLL3FiSVpQd09HaUFpCldwVzBmeTNqWGdCWGRlbXN4T0hWSlFaQ0didC9abDcxMUtRR0IrNjMyN2FVNnRET2tWRUllVldsMDBDYVVsTXIKNloxeUpkcjdLR1IvSzQ2ZWxodDJJWnJ5YjdIWk1hcXFtR2VpN29XbCtlQnlFbnc3UjBqb1pJdERaa3ljd0VtZApvejFJVGk0ZWlkNy9YTmQvZ2lvTmhrOEpOTm9uY2toc0doenpsYllPMUVNMTdyTWI4M3RkQXdFeFI3alNmTkZTCjkyVWF2K1VheXNvV3FOajlzMVN2TzN1TjBDcGtrREZ2WDdsT0dnWG1ET2dORTVhdHlKZmQxQjdGOHZ0b1ZrTkkKNFFJREFRQUIKLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0tCg=="}, {"kid": "jianguo.wang", "value": "LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUExUFhyVWlEZ1AvS2VscnVmN3l2Tgp3YTJ1NmM4b3gvNW1kcmF6ZnhNSlRHdnVaYk96Wk9GZlNpUWlVdzMwV0tqa1NFQUF4NU9XdzhDQi9tTU5qeDlxCnIyVkFPN3kzNEwyajZYQWovNzlnbHlCWVhiNkJydXNHVFJRcE1wQ3J1WGRGL1poU1E2cy9RY0NzT1N5cGlzUTAKTGw5dlRoZ0pScHlxazhZTlkva2xrdXFYWXd6VjY2MUlFcWFFbytrQUhBZS9NNE9sVERTZW1xalZaSGZhTjhkMgpqRENWYzQxeGFJaTBSZHo1aGZYYTBhL21vaWVDZVBjelNRb3FGVTc2SUN1b3JFOVNYOHVxSVJwMXE4RUJ4RGFTCjdGZDJ6MWV4UVowYW94M1RrNGpFazVPaFYwWGlNdE5uRS95bEdCUUJDcVZ6TjVBVnRZRFRaNUFsMnI1N1JJNkgKUndJREFRQUIKLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0tCg=="}, {"kid": "jiahao.huang", "value": "LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0NCk1JSUJJakFOQmdrcWhraUc5dzBCQVFFRkFBT0NBUThBTUlJQkNnS0NBUUVBeFdkUFNtU2dSZTNoY1lQcElUakMNCnNENU03NUhxY3c5dkdpdEFSNUgxUWFGT1NNUHhRRGdwVEtGeTlXemVlWWRWbXMxeHJHVklhdmQreE5WYXhSTFMNCnZpeDA5ODU4b2tvOUc4NVA4ZGNCNkl2aEFQb2lLRFF0aXZTQTlmZXhjeU5wa3NXVFBJVVZpNnB3NTN5VnU2ZDENCmNFaWRSbmZIVTFVaVpXT2kvZHU3S2dtekRNVUthK2pkeTYrNkJFMGlWQWQ4Sy90VUdUN1VCdlJrVW5tL2FqWjcNCnJETERZeGpjUEZBdUdVeVAzL3A5Q0FKQm1lQ1lFNmR2WjRzQVhiOTV3NFVDd1JqV3dDOEpHRkp0Tk8zN0lPYlcNCkQyVk5UZXhDbGw5TEJrM0ZqZkRocENkaUs3WmNyMmdTb0o3bkMvN3lkdVhLOVZTcWtDZWc4Z0tqdjZDd2FsN1UNCnZ3SURBUUFCDQotLS0tLUVORCBQVUJMSUMgS0VZLS0tLS0="}, {"kid": "load.test", "value": "LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUEyTnZYVXlEYjVJQ3dwS2ZzUTEvUApETnVXY0I3MGFrdGIvZmlaQ292dnVNdVQ4N1NYaUJzOXl4Um1LRDYvUG1aVzA3RHBYcDVLL3FiSVpQd09HaUFpCldwVzBmeTNqWGdCWGRlbXN4T0hWSlFaQ0didC9abDcxMUtRR0IrNjMyN2FVNnRET2tWRUllVldsMDBDYVVsTXIKNloxeUpkcjdLR1IvSzQ2ZWxodDJJWnJ5YjdIWk1hcXFtR2VpN29XbCtlQnlFbnc3UjBqb1pJdERaa3ljd0VtZApvejFJVGk0ZWlkNy9YTmQvZ2lvTmhrOEpOTm9uY2toc0doenpsYllPMUVNMTdyTWI4M3RkQXdFeFI3alNmTkZTCjkyVWF2K1VheXNvV3FOajlzMVN2TzN1TjBDcGtrREZ2WDdsT0dnWG1ET2dORTVhdHlKZmQxQjdGOHZ0b1ZrTkkKNFFJREFRQUIKLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0tCg=="}]}, "hash": ":global.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:global.stg.ssajtRTsFhVA6hiioqOz9esC18o=:template.V3h1aUT4E9JK-vyeEtfPCkAGzUA="}, "gid_fake_phone_numbers": {"template": "{{ string .value}}", "data": {"value": "65000000000000-65000099999999,6580000000-6581617145,6581617147-6583808231,6583808233-6584287692,6584287694-6584519704,6584519706-6586213270,6586213272-6587429200,6587429202-6588765191,6588765193-6590513518,6590513520-6591492977,6591492979-6591573360,6591573362-6591786539,6591786541-6592409530,6592409532-6598336387,6598336389-6599999999"}, "hash": ":global.H7ndwtiulvugG-MUMrz741CkIjE=:global.stg.ZR5c7Y68j4odEM_nYgd0lv6cXB4=:template.lsXd29zLUYpIDxMMByvD9Du4zzI="}, "gid_fraud_uri": {"template": "{{ string .value }}", "data": {"value": "https://grab-fraud.stg-myteksi.com"}, "hash": ":global.CanVTS11z7BUL2kG6nPj52KfHiE=:global.stg.IxMpggRvfXsI2NOk9xZ67MFA8hs=:template.KYaLQxFAf7eAFTln96KWIABn9Dg="}, "gid_grab_comm_endpoint": {"template": "{{ string .var }}", "data": {"var": "https://grab-comm-int.stg-myteksi.com"}, "hash": ":global.OOZ4itDOd2xVdZ1wTGF9ofLepMU=:global.stg.mzcaHHemEcmQDKCAGbeDc6bf-Wk=:template.TRk86KGp1gqxSfYSVA2hX98U3B0="}, "gid_grabapi_circuitbreakers": {"template": "[\n\t{{ range $idx, $val := .settings }}\n    '{{ $val }}',\n    {{ end }}\n]\n", "data": {"settings": ["{\"tag\": \"grabid-grabapi-fraud\", \"timeoutInMs\":3000, \"errorPercentThreshold\":50, \"maxConcurrentReq\": 200}", "{\"tag\": \"grabid-grabapi-paysi\", \"timeoutInMs\":3000, \"errorPercentThreshold\":50, \"maxConcurrentReq\": 200}"]}, "hash": ":global.Bl9e039OmNTtR17mztWz3dxJ6s8=:global.stg.:template.4g_1VzFk2Qkc8AYcr3Ag5CVupYA="}, "gid_hedwig_template_ids": {"template": "{{ range $idx, $t := .email_template }}\n    '{{ $t.name }}' : '{{ $t.id }}'\n{{ end }}", "data": {"email_template": [{"id": "423d10fb-9a70-45e8-807b-efc3d8b62f96", "name": "grab_resend_user_verification_email"}, {"id": "59735d5f-8b73-4f9b-9457-5b3ba2c01ad1", "name": "grab_user_verification_email"}, {"id": "99c21940-a14f-407f-be95-9a6beda43714", "name": "grab_deactivation_email_1"}, {"id": "0dc33d5f-8eec-42df-8f3a-c1cca34cb8f4", "name": "grab_deactivation_email_2"}, {"id": "905c18da-b1fc-4413-a755-6f8c3ebe3f4c", "name": "grab_deactivation_email_3"}, {"id": "6427d3d3-2171-4ab1-b822-7dafc87043ac", "name": "grab_report_new_email"}, {"id": "8179a89d-d096-4408-a183-3954a5f2cca2", "name": "grab_user_pin_reset_email"}, {"id": "af961d9e-47a4-44a3-971d-b9ba16c0c2b0", "name": "phone_recycle_changed_number"}, {"id": "03340a3a-4789-4045-a58f-f6e74cd10598", "name": "TBD_grab_user_otp_code"}, {"id": "bd539e50-9a8f-4fba-982e-a49aa6f8a4f8", "name": "phone_recycle_verify"}, {"id": "db0e5ad0-5700-497b-9f0e-0b5fc00a6cb6", "name": "oauth2_otp_pushchainedinbox"}, {"id": "7a2643d8-559d-4bd7-bca0-8d00461ac278", "name": "grab_security_alert_email"}]}, "hash": ":global.g56VeivICHE11lu1Ujhrt-FOga8=:global.stg.l8ShGIMx0lVtkLR3QUVCVx_rvUQ=:template.nhNFCf1S3r5XsavJf5JOqVhuS3g="}, "gid_hellfire_enabled": {"template": "{{ bool .value }}", "data": {"value": false}, "hash": ":global.O6LNNbEehXAfXlWEsMlaNW7XX_g=:global.stg.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:template.agqciiThdyC8wUwL4ZJbPn3dsso="}, "gid_hellfire_enforce_nonce_presence": {"template": "{{ bool .value }}", "data": {"value": false}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.:template.agqciiThdyC8wUwL4ZJbPn3dsso="}, "gid_hellfire_log_absent_nonce": {"template": "{{ bool .value }}", "data": {"value": true}, "hash": ":global.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:global.stg.:template.agqciiThdyC8wUwL4ZJbPn3dsso="}, "gid_hellfire_otp_no_nonce_enforcement_rate": {"template": "'{{ string .value }}'", "data": {"value": " { \"/v1/phone/login|rw\": 100, \"/v1/phone/register|rw\": 100, \"/v1/phone/token|rw\": 100, \"/v1/phone/otp|allcountry\": 0,  \"/v1/phone/flashcall|allcountry\": 100,   \"default\": 0 } "}, "hash": ":global.Xel7S-vsKwRX9EkEooJfi2RWIfo=:global.stg.zoy-s-Gfc4li0PHIUQCk7OPOiA8=:template.4LTVYHygXHcNstr1WqJHh7taIdQ="}, "gid_hellfire_private_key": {"template": "{{ decryptWith .kms .enc }}", "data": {"enc": "dev-grab-id|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", "kms": "dev-grab-id"}, "hash": ":global.bHYmVn8eIyHC8DMDFVMKC5ShxyA=:global.stg.QbGIePTosHPNZ8M5qoDpFAwJIyk=:template.x1Xu41LT8565wT_X4iiNHoKKiMQ="}, "gid_hellfire_replay_ttl_in_seconds": {"template": "{{ int .value }}", "data": {"value": 172800}, "hash": ":global.9WWQJCtbq69CsqYXQfAykBUrC5Q=:global.stg._XDO2GQCPWrZAJ6u1gcAWb36cmE=:template.RZf0MUURZFAeaEIiT4lKEwPO7-w="}, "gid_hellfire_stale_after_in_seconds": {"template": "{{ int .value }}", "data": {"value": 360000}, "hash": ":global.4TtQZsEuZeiJvPvNzc-zx76QrSM=:global.stg.vHoWFT3gqu0oGiGxOKTCGQean_E=:template.RZf0MUURZFAeaEIiT4lKEwPO7-w="}, "gid_hellfire_stream_rollout": {"template": "'{{ string .value }}'", "data": {"value": "{ \"write_to_hfnstream_percentage\": 100 }"}, "hash": ":global.CanVTS11z7BUL2kG6nPj52KfHiE=:global.stg.HptUWpPWD54zQEedqVZoJcjpfzI=:template.4LTVYHygXHcNstr1WqJHh7taIdQ="}, "gid_hellfire_stream_writer": {"template": "'{{ string .value }}'", "data": {"value": "{\"enabled\": true,\"fnstream_writer\": {\"kafkaConfig\": {\"brokers\": [\"kafka-ins-store-01.stg-myteksi.com:9092\",\"kafka-ins-store-02.stg-myteksi.com:9092\",\"kafka-ins-store-03.stg-myteksi.com:9092\"],\"clientID\": \"stg-hellfiresdk-writer\",\"enableRetry\": true,\"enabled\": true},\"kafkaOnly\": true,\"hellFireNonceStream\": \"stg-hellfire-nonce-stream\"}}"}, "hash": ":global.CanVTS11z7BUL2kG6nPj52KfHiE=:global.stg.pOccLHsK_5N4JRoVCYVEfitaD0E=:template.4LTVYHygXHcNstr1WqJHh7taIdQ="}, "gid_hystrix_circuit_breaker_helper_timeout": {"template": "{{ int .timeout }}\n", "data": {"timeout": 30000}, "hash": ":global.Ro328dEc2D6tCoSRx_qEvBBk9BE=:global.stg.:template.vbwlcYqADKAhMNR2S3tQqs7TgFc="}, "gid_hystrix_master_db_config_concurrency": {"template": "{{int .value}}", "data": {"value": 5000}, "hash": ":global.Tbh1E8U9SpQ_U3c8qICHwPUzg3s=:global.stg.:template.dBPGNuzAiX2SUdqyZn35eIqNGcw="}, "gid_hystrix_slave_db_config_concurrency": {"template": "{{int .value}}", "data": {"value": 5000}, "hash": ":global.Tbh1E8U9SpQ_U3c8qICHwPUzg3s=:global.stg.:template.dBPGNuzAiX2SUdqyZn35eIqNGcw="}, "gid_idtoken_enable_generate_local_jwt": {"template": "{{ .enable }}", "data": {"enable": true}, "hash": ":global.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:global.stg.5iHufFPAlJbIcDJE8I_7KPnNL68=:template.8-30y6FpwltSh4LdW4D8asy-j2c="}, "gid_idtoken_public_key": {"template": "publicKey: \"{{ string .publicKey }}\"", "data": {"publicKey": "-----B<PERSON><PERSON> PUBLIC KEY-----\\nMIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEA2euwOrudk+EdR7OSTgJP\\nNyAdtuoA0hQ1orbDEYBUJ3uvE6T+sLydMevbtyeo5Z3bc/Jpdw8eJBAJ02DIt+6k\\n22NQH6XEJ5r2oL1wMHbSlWbRKVXj+eBSfQtbKaA1CsawBPwrZQ29SvmV0EP18dSH\\nnJiBH+GN0PGjOZZDFZrtiNQjrCkwGfyFgh75Ala5YQXrR8ty7KMsYnih27Dl+MFY\\n/KyNOY9wTODov7ExgCf/xJy5A84TvAAlAy2gJ5WRL/n8HNCoFYobUY5yopw6lIxj\\nS1mwjl53/Yt0dfPH9g3EzWzmztu+xEHS472wtxCQEPdo07uZweVIq+aokZXvHI9I\\nduS0+3qVmzRs1ItdnLag3K87j2K8iwp8sJLwomkN12xPKOFG7LtB4injjrZT7Ht4\\n8kaO2grXP4ikq/bOkmvTVEXzrAUgR/K+jryhweBQ+ms7MeDBPCOyqmqoA8fJarXy\\nOf9t/0+c46mhONEe8CmzhEEzQvaD2Q4DWKi234nRW0oYfeevJnmruY9I3odTjbqx\\n7viQA+EAvM6CRjY4GAlwErtGBqlDrSVq0ovonSdDLICLTUWQDoD6e2NhgR+x3M5C\\nsDyeh7hqiWGWWq1H4Szpk5sz2AVjRzMzJ0vKDgb1C/9+CDRUgMPwhz8n96cQQBGt\\n2Lpf026dy1d+oOCdDuxRWKkCAwEAAQ==\\n-----END PUBLIC KEY-----"}, "hash": ":global.t99E990TI3gJVuggtVVwOfjaEgE=:global.stg.cAH8W0AuTx246yjse4_pjNroJhk=:template.P3au6wK5cEK7tVLraTsaT7t9xx0="}, "gid_ignore_file_too_large_enabled_countries": {"template": "{{ range $idx, $country := .country }}\n  {{$country.name}} : {{$country.value}}\n{{end}}", "data": {"country": [{"name": "ID", "value": true}, {"name": "VN", "value": true}, {"name": "TH", "value": true}]}, "hash": ":global.HcqfmJUYsOKone-kp7P5Dq2iWRc=:global.stg.lZc8-Bt9bE_1aNWPQFXhrMOmKbo=:template.yuL1VfdPJvKhNk5LKH617rYFpuU="}, "gid_ignore_file_too_large_override": {"template": "{{bool .value }}\n", "data": {"value": true}, "hash": ":global.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:global.stg.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:template.CTPDBu4PaBGvMQDv7YoY3ahTsPs="}, "gid_internal_api_users_list": {"template": "{{ .strList}}", "data": {"strList": "[\"<EMAIL>\",\"<EMAIL>\"]"}, "hash": ":global.VZYzJY1hIfnE3NsSfE4YIQQfocg=:global.stg.EsKlHPaJn3OMx7xBuNQ7qopedvg=:template.vQWA28A23k89lB3eJzkfBUkFt-8="}, "gid_kingpin_client_config": {"template": "serverAddress: '{{ string .serverAddress }}'\nserviceDiscoveryEnabled: {{ bool .serviceDiscoveryEnabled }}\npoolMaxOpen: {{ int .poolMaxOpen }}\ndialTimeoutInMs: {{ int .dialTimeoutInMs }}\ndefaultHealthCheckFrequencyInSec: {{ int .defaultHealthCheckFrequencyInSec }}\ndefaultHealthCheckTimeoutInSec: {{ int .defaultHealthCheckTimeoutInSec }}\ntimeout: {{ int .timeout }}\nmax_concurrent_requests: {{ int .max_concurrent_requests }}\nerror_percent_threshold: {{ int .error_percent_threshold }}\n", "data": {"defaultHealthCheckFrequencyInSec": 2, "defaultHealthCheckTimeoutInSec": 2, "dialTimeoutInMs": 2000, "error_percent_threshold": 50, "max_concurrent_requests": 100, "poolMaxOpen": 10, "serverAddress": "", "serviceDiscoveryEnabled": false, "timeout": 2000}, "hash": ":global.BkCSyKO68MYJqnddDZJNuu8bvvA=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.101eFivi824Uop7mdnaCQMMbcgY="}, "gid_login_check_social_login_user_id": {"template": "{{ bool .check }}\n", "data": {"check": true}, "hash": ":global.vPgJHo45to61k4258IT3urCRDJg=:global.stg.:template.Wl8laD-d4wQuJkb-1KSGe6p7ymA="}, "gid_login_event:elasticsearch:config": {"template": "endpoint: {{ string .endpoint }}\nindex: {{ string .index }}\nusername: {{ string .username }}\npassword: {{ decryptWithVault .role .path .key }}", "data": {"endpoint": "https://grab-id.ece-v2.stg-myteksi.com:9243/", "index": "gid_login", "key": "value", "path": "stg/es/credentials/stg-grab-id/grab-id", "role": "dev-grab-id", "username": "grab-id"}, "hash": ":global.JvFzQF4HVz1P_Q1K-kzVfNH-ufg=:global.stg.yF8cNmVkt8MS7KSaPdBxZRaEh4Y=:template.rmkgsVpJKyrl2kn0ziiY81RyBug="}, "gid_login_event:elasticsearch:enabled": {"template": "{{ bool .value}}", "data": {"value": true}, "hash": ":global.O6LNNbEehXAfXlWEsMlaNW7XX_g=:global.stg.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_login_event:enabled": {"template": "{{ bool .value}}", "data": {"value": false}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.O6LNNbEehXAfXlWEsMlaNW7XX_g=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_login_event:stream:consumer:config": {"template": "kafkaOnly: {{ bool .kafkaOnly }}\ngrabIDLoginStream: {{ string .grabIDLoginStream }}\n\nkafkaConfig:\n  enabled: {{ bool .enabled }}\n  clientID: {{ string .clientID }}\n  offsetType: {{ string .offsetType }}\n  brokers: \n{{ range $val := .brokers }}\n  - {{ $val }}\n{{ end }}\n", "data": {"brokers": ["end-kafka-loc-01.stg-myteksi.com:9092", "end-kafka-loc-02.stg-myteksi.com:9092", "end-kafka-loc-03.stg-myteksi.com:9092", "end-kafka-loc-04.stg-myteksi.com:9092", "end-kafka-loc-05.stg-myteksi.com:9092", "end-kafka-loc-06.stg-myteksi.com:9092", "end-kafka-loc-07.stg-myteksi.com:9092", "end-kafka-loc-08.stg-myteksi.com:9092"], "clientID": "gidlogin", "enabled": true, "grabIDLoginStream": "stg-grabid-login", "kafkaOnly": true, "offsetType": "oldest"}, "hash": ":global.L16t3nzdN-oPOBCqeq3wzrzycvs=:global.stg.FyxuisgX1u4xl3VlF4wSY2KLSCo=:template.THvm8xazPfZkfpZ_vyudWELoRRM="}, "gid_login_event:stream:consumer:enabled": {"template": "{{ bool .value}}", "data": {"value": false}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_login_event:stream:producer:config": {"template": "kafkaOnly: {{ bool .kafkaOnly }}\ngrabIDLoginStream: {{ string .grabIDLoginStream }}\n\nkafkaConfig:\n  brokers: \n{{ range $val := .brokers }}\n  - {{ $val }}\n{{ end }}\n\nconfig:\n  region: {{ string .region }}\n  accessKey: {{ string .accessKey }}\n  secretId: {{ string .secretId }}", "data": {"accessKey": "", "brokers": ["b-1.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "grabIDLoginStream": "stg-grabid-login", "kafkaOnly": true, "region": "ap-southeast-3", "secretId": ""}, "hash": ":global.nSzGWyBK-PXgNIz-ilP5uh5ZaFE=:global.stg.ETU5Dot4ImyavGwcp1rWxm4lKu0=:template.0lsUG-D0mI_qBKEY8zE7oBbB2Hg="}, "gid_login_event:stream:producer:enabled": {"template": "{{ bool .value}}\n", "data": {"value": true}, "hash": ":global.O6LNNbEehXAfXlWEsMlaNW7XX_g=:global.stg.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:template.39WX-LGyC4itcwCCsLpjTzZIKbY="}, "gid_login_event_stream_consumer_allowed_hosts": {"template": "{{string .value}}", "data": {"value": "************"}, "hash": ":global.CanVTS11z7BUL2kG6nPj52KfHiE=:global.stg.FUg2j14iE7_jLDSdLAZPsex5L6Q=:template.WvCTNQLBxJgPZwSa5xyvy_JSnpM="}, "gid_logout_verdict_disable_401_check": {"template": "{{ bool .value}}", "data": {"value": false}, "hash": ":global.O6LNNbEehXAfXlWEsMlaNW7XX_g=:global.stg.O6LNNbEehXAfXlWEsMlaNW7XX_g=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_logout_verdict_disable_403_check": {"template": "{{ bool .value}}", "data": {"value": false}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_service_user_id_mapping_enabled": {"template": "{{ bool .value}}", "data": {"value": true}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_logout_verdict_force_rollout_percent_401": {"template": "{{ int .value}} \n", "data": {"value": 100}, "hash": ":global.3xiRTwCSbSvU-TwuVI8qlk6qToc=:global.stg.syjBrIyB5cdMmItaCCJ50jJcX9k=:template.k7k4LGLPGcdtKbP71HvidCbo9kM="}, "gid_logout_verdict_force_rollout_percent_403": {"template": "{{ int .value}}", "data": {"value": 100}, "hash": ":global.bKI6f4LEFLtdNwyqI2E1z2IRcrA=:global.stg.syjBrIyB5cdMmItaCCJ50jJcX9k=:template.uUjClgiahYQrWw6QuusxaIQ1mFM="}, "gid_mail_migration_status_enabled": {"template": "{{bool .value}}", "data": {"value": true}, "hash": ":global.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:global.stg.:template.1KkG44n-u7AJnzgLwkNYZ0wa3YY="}, "gid_mailer_use_sms_v2_pct": {"template": "{{int .value}}", "data": {"value": 100}, "hash": ":global.bKI6f4LEFLtdNwyqI2E1z2IRcrA=:global.stg.syjBrIyB5cdMmItaCCJ50jJcX9k=:template.dBPGNuzAiX2SUdqyZn35eIqNGcw="}, "gid_mfa_token_expiry": {"template": "{{int .value }}", "data": {"value": 604800}, "hash": ":global.pXzn1KI4neo1bSie7wlWk2N1lz0=:global.stg.:template.cPtAI32TN1_SwYsk_FLy2l-r-o0="}, "gid_mfaphoneotp_max_attempts": {"template": "{{ int .value }}", "data": {"value": 5}, "hash": ":global.Z9_sVezh8WtByjBqXJvB8Nlq9Hk=:global.stg.U_DZyi5uUf-oyhlMoNPTnNBxcew=:template.RZf0MUURZFAeaEIiT4lKEwPO7-w="}, "gid_missandei_enabled": {"template": "{{bool .value }}", "data": {"value": true}, "hash": ":global.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:global.stg.:template.JiWK9emWNKKDIOXrITVkumwxLt8="}, "gid_ml_check_config": {"template": "{{ range $idx, $cfg := .cfgs }}\n  {{$cfg.country}}: \n    currency: {{$cfg.currency}}\n    balance_threshold: {{$cfg.balance_threshold}}\n    rollout_percent: {{$cfg.percent}}\n{{end}}", "data": {"cfgs": [{"balance_threshold": 1000, "country": "US", "currency": "USD", "percent": 100}, {"balance_threshold": 1000, "country": "TH", "currency": "THB", "percent": 100}, {"balance_threshold": 1000, "country": "MY", "currency": "MYR", "percent": 100}, {"balance_threshold": 1000, "country": "SG", "currency": "SGD", "percent": 100}]}, "hash": ":global.LAW6wVrZTeZ0X59Bv3bZvaVKIfw=:global.stg.UoZYrbndsqpvS-l2bF-wG-CES_0=:template.S8mM812_nvctjm3-YJujMUIhGak="}, "gid_moca_country_code": {"template": "{{ string .value }}", "data": {"value": "vn"}, "hash": ":global.CanVTS11z7BUL2kG6nPj52KfHiE=:global.stg.BW7hU0ZaBOcMCtPh3aQjKLcEC58=:template.KYaLQxFAf7eAFTln96KWIABn9Dg="}, "gid_moca_otp_client_token": {"template": "{{ decryptWithVault .role .path .key }}", "data": {"key": "c8f47c4520475139aaac4e5f6ff15befc38b21a379e9f9ec5f", "path": "stg/service/grab-id/moca_otp", "role": "dev-grab-id"}, "hash": ":global.mpYegIgLkQL3qrWDsfoJYBgfO0k=:global.stg.psyLlqtdYNIyVBYn5igqfzqe6ko=:template.1RY6zFGQl7IVCy7I8_fOndpvEdg="}, "gid_moca_otp_config": {"template": "'{{ string .value }}'", "data": {"value": "{ \"generate_otp_url\": \"https://hht-dev01.moca.vn/up/v1/otp/generate\", \"verify_otp_url\": \"https://hht-dev01.moca.vn/up/v1/otp/verify\", \"client_id\": \"1\", \"http_client_timeout_in_seconds\": 5, \"cb_setting\": {  \"tag\": \"grabid:mocaotp\",\"timeoutInMs\": 3000,\"maxConcurrentReq\": 1000,  \"errorPercentThreshold\": 50, \"volumePercentThreshold\": 20, \"sleepWindowInMs\": 100, \"maxQueueSize\": 5000  }}"}, "hash": ":global.CanVTS11z7BUL2kG6nPj52KfHiE=:global.stg.NtUo6-xfT6UfAinVJ505_l68Zxk=:template.4LTVYHygXHcNstr1WqJHh7taIdQ="}, "gid_msg_email_check_duplication": {"template": "{{bool .value }}", "data": {"value": true}, "hash": ":global.UXPNRiDwwr7k_e28BkBXW2657Fk=:global.stg.:template.JiWK9emWNKKDIOXrITVkumwxLt8="}, "gid_msg_email_email_clash_domain_filter": {"template": "{{string .value}}", "data": {"value": "gmail.com"}, "hash": ":global.vgBYm7S9FFImuZBYAySs3_8SoJo=:global.stg.:template.WvCTNQLBxJgPZwSa5xyvy_JSnpM="}, "gid_msg_email_email_clash_enabled": {"template": "{{bool .value}}", "data": {"value": true}, "hash": ":global.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:global.stg.:template.1KkG44n-u7AJnzgLwkNYZ0wa3YY="}, "gid_msg_email_email_clash_enabled_countries": {"template": "{{ range $idx, $country := .country }}\n  {{$country.name}} : {{$country.value}}\n{{end}}\n", "data": {"country": [{"name": "MY", "value": true}, {"name": "SG", "value": true}, {"name": "ID", "value": true}, {"name": "PH", "value": true}, {"name": "VN", "value": true}, {"name": "TH", "value": true}, {"name": "MM", "value": true}, {"name": "KH", "value": true}, {"name": "US", "value": true}]}, "hash": ":global.EymqDIxgfVxoaiCK9avtTBxnN-Q=:global.stg.9pgg8mHrCTGIwz3PG8jsNjxa25E=:template.blQMA3zRZNP6EeOcjq6V98DDbYk="}, "gid_msg_email_email_clash_enabled_services": {"template": "{{ range $idx, $service := .services }}\n  {{$service.name}} : {{$service.value}}\n{{end}}", "data": {"services": [{"name": "PASSENGER", "value": true}]}, "hash": ":global.xsmdxElfts8IR6OnMn8QpDZwnZg=:global.stg.:template.Jen5wd4_-XDtuoURz-GF1t7DTTc="}, "gid_msg_email_email_clash_invalid_chars": {"template": "{{string .value}}", "data": {"value": "."}, "hash": ":global.22WwPzW_kIUNHo-tPB36u0PxSVg=:global.stg.:template.WvCTNQLBxJgPZwSa5xyvy_JSnpM="}, "gid_msg_email_validation_enabled": {"template": "{{bool .value}}", "data": {"value": true}, "hash": ":global.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:global.stg.:template.1KkG44n-u7AJnzgLwkNYZ0wa3YY="}, "gid_normalize_existing_pax_phone": {"template": "{{ bool .enable }}", "data": {"enable": true}, "hash": ":global.NCtXh84dcSGz4zwsvLF1cRWma9Y=:global.stg.-GAIbeoPwdheDfxt-QSGelCK9BY=:template.JiOcS0p7bNVseSIKgTM8R2Njth8="}, "gid_normalize_phone_number_for_otp_challenge": {"template": "{{ bool .enable }}", "data": {"enable": true}, "hash": ":global.SisfFvW4TR2CgLvsecwF0yfl8J0=:global.stg.-GAIbeoPwdheDfxt-QSGelCK9BY=:template.JiOcS0p7bNVseSIKgTM8R2Njth8="}, "gid_oauth2_allow_http_trusted_redirect_URI": {"template": "{{bool .value }}", "data": {"value": true}, "hash": ":global.O6LNNbEehXAfXlWEsMlaNW7XX_g=:global.stg.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:template.JiWK9emWNKKDIOXrITVkumwxLt8="}, "gid_oauth2_allow_ip_address_redirect_URI": {"template": "{{bool .value }}", "data": {"value": true}, "hash": ":global.O6LNNbEehXAfXlWEsMlaNW7XX_g=:global.stg.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:template.JiWK9emWNKKDIOXrITVkumwxLt8="}, "gid_oauth2_allowed_client_list_for_basic_oauth": {"template": "{{ string .value}}", "data": {"value": "f5bc6abbb97b4a0ca48686f312540f2d"}, "hash": ":global.pIP-2yJkUYO--V2KKBQVSYSgpHk=:global.stg.PD9RPVDTx-8MK-AoalMofVdF6r0=:template.lsXd29zLUYpIDxMMByvD9Du4zzI="}, "gid_oauth2_authn_cookie_domain": {"template": "{{ string .value}}\n", "data": {"value": "stg-myteksi.com"}, "hash": ":global.CanVTS11z7BUL2kG6nPj52KfHiE=:global.stg.8vpwvhKeGQ_rVxrvwpa7IsGwx5A=:template.bTk5jJ5DY2CHfcM2HCvtLrXCZkM="}, "gid_oauth2_authn_cookie_path": {"template": "{{ string .value }}", "data": {"value": "/"}, "hash": ":global.ooGU8g_qY37FKA63m4JC5lfGYZo=:global.stg.1mD5653zBc0MjNuGQtUCBhpjJC8=:template.KYaLQxFAf7eAFTln96KWIABn9Dg="}, "gid_oauth2_authn_gateway_hostname_mapping": {"template": "{{ range $idx, $gateway := .gateways }}\n  '{{ $gateway.gwHostname}}' : {{$gateway.gwValue}}\n{{end}}", "data": {"gateways": [{"gwHostname": "", "gwValue": "agw"}, {"gwHostname": "api-gateway.stg-myteksi.com", "gwValue": "agw"}, {"gwHostname": "partner-gateway.stg-myteksi.com", "gwValue": "pgw"}, {"gwHostname": "partner-api.stg-myteksi.com", "gwValue": "pgw"}, {"gwHostname": "new-gateway.stg-myteksi.com", "gwValue": "pgw"}, {"gwHostname": "stg-paysi.moca.vn", "gwValue": "moca"}]}, "hash": ":global.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:global.stg.J0LNmCbESkFFagNnCOHw-f_rnoA=:template.wMuw0NlKK5C7ETel9jDKTs2VLpA="}, "gid_oauth2_authn_set_http_only_cookie": {"template": "{{ bool .mode }}", "data": {"mode": true}, "hash": ":global.bropxfxD4ovyMGupxePq01wfiQw=:global.stg.y3z_0SsnMPGAlWUgZGWvseE9HdQ=:template.pFC3E9JAg-19JiBGOg7cmUwRE-A="}, "gid_oauth2_authn_set_secure_cookie": {"template": "{{ bool .mode }}", "data": {"mode": true}, "hash": ":global.XrmYOQvarWnx4hDehA0gcvJvwJs=:global.stg.y3z_0SsnMPGAlWUgZGWvseE9HdQ=:template.pFC3E9JAg-19JiBGOg7cmUwRE-A="}, "gid_oauth2_authz_endpoint": {"template": "{{ string .value}}\n", "data": {"value": "https://partners.stg.g-bank.app/identity/v1/oauth2/authorize"}, "hash": ":global.TWZLcxpPP9OjWbJb0noYxhtbQTk=:global.stg.RMtvgpQERFu3TMIh-etwt-x4Znc=:template.bTk5jJ5DY2CHfcM2HCvtLrXCZkM="}, "gid_oauth2_bind_request_scope": {"template": "{{ string .value}}", "data": {"value": "3c0962ae3d8e4882ad4fa9db03fb8632"}, "hash": ":global.pIP-2yJkUYO--V2KKBQVSYSgpHk=:global.stg.KGiW2RrVqmwZEr-8yEyRgJ_308Q=:template.lsXd29zLUYpIDxMMByvD9Du4zzI="}, "gid_oauth2_client_additional_params_check": {"template": "{{ string .value }}", "data": {"value": "Email,ProductName"}, "hash": ":global.pIP-2yJkUYO--V2KKBQVSYSgpHk=:global.stg.Lt6bjgRtS4hxTDDix_G7BFCYxCo=:template.KYaLQxFAf7eAFTln96KWIABn9Dg="}, "gid_oauth2_client_public_info_endpoint": {"template": "{{ string .value}}\n", "data": {"value": "https://partners.stg.g-bank.app/identity/v1/oauth2/clients/{client_id}/public"}, "hash": ":global.pIP-2yJkUYO--V2KKBQVSYSgpHk=:global.stg.hNwhSHPVKullK_0QP3R6B34gBfQ=:template.bTk5jJ5DY2CHfcM2HCvtLrXCZkM="}, "gid_oauth2_core_authn_uri": {"template": "{{ string .value}}", "data": {"value": "https://weblogin.stg-myteksi.com/auth"}, "hash": ":global.CanVTS11z7BUL2kG6nPj52KfHiE=:global.stg.jeF4DrNPMPhlLS7YQFfuZ_8dXBw=:template.lsXd29zLUYpIDxMMByvD9Du4zzI="}, "gid_oauth2_custom_protocols_list": {"template": "{{ range $index, $p := .protocol }}\n  {{$p.key}} : '{{$p.value}}'\n{{end}}", "data": {"protocol": [{"key": "PASSENGER", "value": "{\"protocol_adr\": \"grab://open?screenType=PARTNERLOGIN\",\"minversion_adr\": \"5.105.0\",\"package_adr\": \"com.grabtaxi.passenger\",\"protocol_ios\": \"grab://open?screenType=PARTNERLOGIN\",\"protocol_pax_ios\":\"grabconnect2:\"}"}, {"key": "DRIVER", "value": "unsupported"}, {"key": "APPSTORE", "value": "{\"appstore_link_adr\": \"market://details?id=com.grabtaxi.passenger\",\"appstore_link_ios\": \"itms-apps://itunes.apple.com/app/id647268330\"}"}]}, "hash": ":global.p64JSE5q0a-l2wGCMyIFN5iqdIU=:global.stg.pkbAkV9OgJzouCHcLBhfV5IYc3M=:template.XRjdLDMguMFVbN_SvnA43bQyWJI="}, "gid_oauth2_delete_id_token_after_authn_use": {"template": "{{bool .value}}", "data": {"value": true}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:template.1KkG44n-u7AJnzgLwkNYZ0wa3YY="}, "gid_oauth2_enable_id_token_ref_query": {"template": "{{ bool .enabled }}", "data": {"enabled": false}, "hash": ":global.4lA-86YRL7rg3ny3XSt-_pqR16o=:global.stg.u7rtdAoluHslXQkgm-83jGOS6C8=:template.V9zi_N_gYu1eQdfUImZv__yWmFQ="}, "gid_oauth2_enable_moca_openid_config": {"template": "{{ bool .enabled }}\n", "data": {"enabled": false}, "hash": ":global.uLx5K071t3uKEem390c38dWPXPY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.5kcwlTU5NXnvs6tyjiWFwFEufNY="}, "gid_oauth2_enable_token_verify_ban_check": {"template": "{{ string .clients }}", "data": {"clients": "'*'"}, "hash": ":global.yMl8rpEZNVDxqWEeXyao1_OMU5I=:global.stg.8qf_3SoVFLSRW0qEzM250wVbsRo=:template.3wrU7PdA76QlnRdBFk6_SNn2Zsk="}, "gid_oauth2_enable_user_agent_check_generate_token": {"template": "{{ bool .mode }}", "data": {"mode": true}, "hash": ":global.vvPkNkXau7SXgkvpVzvi3IJMPUM=:global.stg.y3z_0SsnMPGAlWUgZGWvseE9HdQ=:template.pFC3E9JAg-19JiBGOg7cmUwRE-A="}, "gid_oauth2_enable_user_agent_check_verify_access_token": {"template": "{{ bool .mode }}", "data": {"mode": true}, "hash": ":global.vvPkNkXau7SXgkvpVzvi3IJMPUM=:global.stg.y3z_0SsnMPGAlWUgZGWvseE9HdQ=:template.pFC3E9JAg-19JiBGOg7cmUwRE-A="}, "gid_oauth2_external_consent_submit_moca_gw_uri": {"template": "{{ string .value}}", "data": {"value": "https://stg-paysi.moca.vn/grabid/v1/oauth2/consent"}, "hash": ":global.CanVTS11z7BUL2kG6nPj52KfHiE=:global.stg.E5hDrsIeMj5olzV50BGFacUqfl4=:template.lsXd29zLUYpIDxMMByvD9Du4zzI="}, "gid_oauth2_external_consent_submit_partner_gw_uri": {"template": "{{ string .value}}", "data": {"value": "https://partner-api.stg-myteksi.com/grabid/v1/oauth2/consent"}, "hash": ":global.bGhNuczLdbgXLSYhhjAHR737UwU=:global.stg.dYdLkypGcmbTs7kpBqpUbFVnzwo=:template.lsXd29zLUYpIDxMMByvD9Du4zzI="}, "gid_oauth2_external_consent_submit_uri": {"template": "{{ string .value }}", "data": {"value": "https://api.stg-myteksi.com/grabid/v1/oauth2/consent"}, "hash": ":global.CanVTS11z7BUL2kG6nPj52KfHiE=:global.stg.cNkYjqUhFrTvJBQ8JACQV81I2_A=:template.KYaLQxFAf7eAFTln96KWIABn9Dg="}, "gid_oauth2_fragment_cutoff_date": {"template": "{{ string .value }}", "data": {"value": "2019-05-01T00:00:00Z"}, "hash": ":global.52Xgpk9XMmkxwGTJ9lae40pld18=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.KYaLQxFAf7eAFTln96KWIABn9Dg="}, "gid_oauth2_id_token_ref_cache_ttl_minutes": {"template": "{{ int .value}}\n", "data": {"value": 600}, "hash": ":global.LiLHTcUIoHJzB8W1dglxDOLn92k=:global.stg.WsMahfhRMAUfZm5QbMPS-DzaimE=:template.G5bShaxXQixlss__E6ZQiNPmkvA="}, "gid_oauth2_id_token_ref_cookie_domain": {"template": "{{ string .value}}", "data": {"value": "stg-myteksi.com"}, "hash": ":global.bGhNuczLdbgXLSYhhjAHR737UwU=:global.stg.8vpwvhKeGQ_rVxrvwpa7IsGwx5A=:template.lsXd29zLUYpIDxMMByvD9Du4zzI="}, "gid_oauth2_id_token_ref_cookie_path": {"template": "{{ string .value }}", "data": {"value": "/"}, "hash": ":global.ooGU8g_qY37FKA63m4JC5lfGYZo=:global.stg.:template.KYaLQxFAf7eAFTln96KWIABn9Dg="}, "gid_oauth2_id_token_verify_endpoint": {"template": "{{ string .value}}", "data": {"value": "https://partners.stg.g-bank.app/identity/v1/oauth2/id_tokens/token_info"}, "hash": ":global.J9sJEw4EaZLwXTKYQFmPzRyY1vU=:global.stg.eFJGQ6lXbn29fDU4wgycFf5Vhgk=:template.lsXd29zLUYpIDxMMByvD9Du4zzI="}, "gid_oauth2_impl_pattern_endpoint": {"template": "{{ string .value}}", "data": {"value": "https://partners.stg.g-bank.app/identity/v1/oauth2/scope_defs/name/impl_pattern"}, "hash": ":global.QkVahJnaIqnFC0AfAvIi7pXGIZs=:global.stg.uXiibUOFBizj6e5o1PKozITr6jM=:template.lsXd29zLUYpIDxMMByvD9Du4zzI="}, "gid_oauth2_internal_consent_submit_partner_gw_uri": {"template": "{{ string .value }}", "data": {"value": "https://partner-api.stg-myteksi.com/grabid/v1/oauth2/default_consent"}, "hash": ":global.CanVTS11z7BUL2kG6nPj52KfHiE=:global.stg.NVLnPSscN8b5JHFvFcBsCMVmv58=:template.KYaLQxFAf7eAFTln96KWIABn9Dg="}, "gid_oauth2_internal_consent_submit_uri": {"template": "{{ string .value}}", "data": {"value": "https://api.stg-myteksi.com/grabid/v1/oauth2/default_consent"}, "hash": ":global.CanVTS11z7BUL2kG6nPj52KfHiE=:global.stg.F6YbGyGTtyqDUSheLqY885us5Uc=:template.lsXd29zLUYpIDxMMByvD9Du4zzI="}, "gid_oauth2_internal_consent_uri": {"template": "{{ string .value}}", "data": {"value": "https://weblogin.stg-myteksi.com/consent"}, "hash": ":global.CanVTS11z7BUL2kG6nPj52KfHiE=:global.stg.Q2WSygbqAeZuyA_XNhbDg3b7r1k=:template.lsXd29zLUYpIDxMMByvD9Du4zzI="}, "gid_oauth2_moca_authn_uri": {"template": "{{ string .var }}", "data": {"var": "https://weblogin.stg-paysi.moca.vn/auth"}, "hash": ":global.UvaBVPpIB4cHj2JQmslUs0xWgAo=:global.stg.BJrOUNrEs7OPFTswQdJeztTrZJs=:template.TRk86KGp1gqxSfYSVA2hX98U3B0="}, "gid_oauth2_partner_id_whitelist": {"template": "{{ string .value}}", "data": {"value": ""}, "hash": ":global.pIP-2yJkUYO--V2KKBQVSYSgpHk=:global.stg.pIP-2yJkUYO--V2KKBQVSYSgpHk=:template.lsXd29zLUYpIDxMMByvD9Du4zzI="}, "gid_oauth2_public_key_Info_endpoint": {"template": "{{ string .value}}", "data": {"value": "https://partners.stg.g-bank.app/identity/v1/oauth2/public_keys"}, "hash": ":global.2f_3dPMWmJ_hY2o3oFEdIDQt4ek=:global.stg.-qcj6m_jD2LkX3ZUvh58vAB1aVk=:template.lsXd29zLUYpIDxMMByvD9Du4zzI="}, "gid_oauth2_revoke_endpoint": {"template": "{{ string .value}}", "data": {"value": "https://partners.stg.g-bank.app/identity/v1/oauth2/revoke"}, "hash": ":global.9qG_CN8Ul__FEvLhqkUabkm7luo=:global.stg.V3hc1dS6wvX4W-P7OcUFxXhnXqo=:template.lsXd29zLUYpIDxMMByvD9Du4zzI="}, "gid_oauth2_salesforce_partner_id": {"template": "'{{ string .partnerID }}'", "data": {"partnerID": "eb1e5363-0687-4d4d-969c-f750c455dadd"}, "hash": ":global.G9FlIveSdWOnzVFyT4EkFkCtnWw=:global.stg.zNpqNSlFHMH-ncm3Ky-6CmQSDUE=:template.AlUinKUEaTPlZ5sp7K7Tk56Qzik="}, "gid_oauth2_token_endpoint": {"template": "{{ string .value}}", "data": {"value": "https://partners.stg.g-bank.app/identity/v2/oauth2/token"}, "hash": ":global.q74UaD58bpYsI2XJG1JeQPIWJ7s=:global.stg.PfsQ5m5ptsrkqqtEBVBWPe_gbGk=:template.lsXd29zLUYpIDxMMByvD9Du4zzI="}, "gid_oauth2_token_verification_service_is_enabled": {"template": "{{ bool .value}}", "data": {"value": true}, "hash": ":global.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:global.stg.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_oauth2_user_agent_check_clients_exclusion_list": {"template": "{{ string .value}}\n", "data": {"value": "55088aaf8322403eae1177928f961a45,08044981144746ec80a870c605fe705b"}, "hash": ":global.K5VQkydin44Vf-7G5349LK-iucU=:global.stg.ToayxDaRhzfJMVSWXtcIUFCwWNk=:template.bTk5jJ5DY2CHfcM2HCvtLrXCZkM="}, "gid_oauth2_userinfo_endpoint": {"template": "{{ string .value}}\n", "data": {"value": "https://partners.stg.g-bank.app/identity/v1/oauth2/userinfo"}, "hash": ":global.UOK4yQm2mUqLl3E_nSF9x7Xc33M=:global.stg.AsbE0-yIq-Q-6ibYFqLCau6KwvE=:template.bTk5jJ5DY2CHfcM2HCvtLrXCZkM="}, "gid_oauth2_weblogin_client_config_exp_keys": {"template": "login_type: '{{string .loginType}}'\nnew_login_api_enabled: '{{string .newLoginAPIEnabled }}'", "data": {"loginType": "naman_test_client_id_feature", "newLoginAPIEnabled": "grabid_weblogin_new_login_api"}, "hash": ":global.OB5oI4IN8hcth5XaaHmOJXoRlmk=:global.stg.C8JYftgGKlbrAXwTNCIf2prhux8=:template.lqZ6MXZrwTErar469UjaRBZXJE4="}, "gid_oauth2_weblogin_client_webconfig": {"template": "new_login_api_enabled: '{{ string .newLoginAPI }}'\napp_redirect: '{{ .app_redirect }}'\nauth_disabled: {{ bool .auth_disabled }}\notpCountdown: {{int .otpCountdown}}\nlogin_method: '{{ string .login_method }}'", "data": {"app_redirect": "", "auth_disabled": false, "login_method": "pax", "newLoginAPI": "true", "otpCountdown": 30}, "hash": ":global.CMxJzC96rsZFVGDYIR2kLVH8PAQ=:global.stg.5XzlvTXg4oizNUZruieYMMgrIPw=:template.Ic_0tX8vNcU1v0UVRf3cwJmmeEo="}, "gid_oauth2_whitelisted_browser_list": {"template": "{{ string .value}}", "data": {"value": "chrome,safari"}, "hash": ":global.Dh2IAuDB1lbK5oee6_GDHyG0uhs=:global.stg.Dh2IAuDB1lbK5oee6_GDHyG0uhs=:template.lsXd29zLUYpIDxMMByvD9Du4zzI="}, "gid_oauth2_whitelisted_browser_rollout": {"template": "{{ bool .var }}", "data": {"var": true}, "hash": ":global.__wqHsXn806frnDXOeJJNRh4bLk=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.NY5dmrhqLFwksQ3kTuuBLW_TKgE="}, "gid_otp_per_phone_number_config": {"data": {"maxOTPPerPhoneNumber": 10, "otpSendingWindowInMinutes": 1440, "coolDownPeriodInMinutes": 1440}, "template": "maxOTPPerPhoneNumber: {{ int .maxOTPPerPhoneNumber }}\notpSendingWindowInMinutes: {{ int .otpSendingWindowInMinutes }}\ncoolDownPeriodInMinutes: {{ int .coolDownPeriodInMinutes }}"}, "gid_otp_soft_locked_period_in_minutes": {"data": {"value": 30}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24=", "template": "{{ int .value }}"}, "gid_pax_activity_cache_capacity": {"template": "{{ int .value}}", "data": {"value": 1048576}, "hash": ":global.6vdgy0r-yOoang5X8x8oYozsFq0=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.uUjClgiahYQrWw6QuusxaIQ1mFM="}, "gid_pax_activity_cache_enabled": {"template": "{{ bool .enable }}", "data": {"enable": true}, "hash": ":global.-GAIbeoPwdheDfxt-QSGelCK9BY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.JiOcS0p7bNVseSIKgTM8R2Njth8="}, "gid_skip_counter_service_init": {"template": "{{ bool .enable }}", "data": {"enable": true}, "hash": ":global.-GAIbeoPwdheDfxt-QSGelCK9BY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.JiOcS0p7bNVseSIKgTM8R2Njth8="}, "gid_pax_dormant_after_inactive_days": {"template": "{{ int .value }}", "data": {"value": 30}, "hash": ":global.odkxQkrjtQSCmmzzwBlErN-rgis=:global.stg.Y3JcuRTMJdVQ1YF7vzf55vF9Ny0=:template.RZf0MUURZFAeaEIiT4lKEwPO7-w="}, "gid_pax_dormant_rollout_config": {"template": "{{ range $idx, $cfg := .cfgs }}\n  {{$cfg.country}} : {{$cfg.percent}}\n{{end}}", "data": {"cfgs": [{"country": "US", "percent": 100}, {"country": "MY", "percent": 100}, {"country": "Th", "percent": 100}, {"country": "SG", "percent": 100}, {"country": "ID", "percent": 50}]}, "hash": ":global.N2_AWIfSHK-WAxsZnqSX38E-mio=:global.stg.NklauGGKxCX1rg2NiSAOSjXRcAQ=:template.v46JYzN_Af6d92QFEtTEQjPE5sY="}, "gid_pax_down": {"template": "{{ bool .down }}\n", "data": {"down": false}, "hash": ":global.7hgS0zt4cw2EA4UvQ_g8QNm3mdw=:global.stg.7hgS0zt4cw2EA4UvQ_g8QNm3mdw=:template.i6PCBU2CJs6SMNBUgKuSSlQ2bXs="}, "gid_paysi_config": {"template": "get_merchant_info_url_format: '{{ string .get_merchant_info_url_format }}'\nget_check_wallet_url_format: '{{ string .get_check_wallet_url_format }}'\nhttp_client_timeout_in_seconds: {{ int .http_client_timeout_in_seconds }}\ncb_setting:\n  tag: '{{ string .cb_tag }}'\n  timeoutInMs: {{ int .cb_timeoutInMs }}\n  maxConcurrentReq: {{ int .cb_maxConcurrentReq }}\n  errorPercentThreshold: {{ int .cb_errorPercentThreshold }}\n  volumePercentThreshold: {{ int .cb_volumePercentThreshold }}\n  sleepWindowInMs: {{ int .cb_sleepWindowInMs }}\n  maxQueueSize: {{ int .cb_maxQueueSize }}", "data": {"cb_errorPercentThreshold": 50, "cb_maxConcurrentReq": 1000, "cb_maxQueueSize": 5000, "cb_sleepWindowInMs": 100, "cb_tag": "grabid:paysi:client", "cb_timeoutInMs": 3000, "cb_volumePercentThreshold": 20, "get_check_wallet_url_format": "https://paysi-int.stg-myteksi.com/v1/user/wallet/check?msgID=%s&currency=%s&serviceID=%s&userID=%d", "get_merchant_info_url_format": "https://paysi-hermes.stg-myteksi.com/paysihermes/v1/merchant-info", "http_client_timeout_in_seconds": 5}, "hash": ":global.REULFyfZkYNKV26XxPA8sucpdPs=:global.stg.xQvhLjMJnc4N3vu6V9IZbpSudKs=:template.rbPvDyZrF3xy9ha5hFAtOclF9eI="}, "gid_paysi_qa_config": {"template": "get_merchant_info_url_format: '{{ string .get_merchant_info_url_format }}'\nget_check_wallet_url_format: '{{ string .get_check_wallet_url_format }}'\nhttp_client_timeout_in_seconds: {{ int .http_client_timeout_in_seconds }}\ncb_setting:\n  tag: '{{ string .cb_tag }}'\n  timeoutInMs: {{ int .cb_timeoutInMs }}\n  maxConcurrentReq: {{ int .cb_maxConcurrentReq }}\n  errorPercentThreshold: {{ int .cb_errorPercentThreshold }}\n  volumePercentThreshold: {{ int .cb_volumePercentThreshold }}\n  sleepWindowInMs: {{ int .cb_sleepWindowInMs }}\n  maxQueueSize: {{ int .cb_maxQueueSize }}", "data": {"cb_errorPercentThreshold": 50, "cb_maxConcurrentReq": 1000, "cb_maxQueueSize": 5000, "cb_sleepWindowInMs": 100, "cb_tag": "grabid:paysi:client", "cb_timeoutInMs": 3000, "cb_volumePercentThreshold": 20, "get_check_wallet_url_format": "https://paysi-int.stg-myteksi.com/v1/user/wallet/check?msgID=%s&currency=%s&serviceID=%s&userID=%d", "get_merchant_info_url_format": "https://paysi-hermes.stg-myteksi.com/paysihermes/v1/merchant-info", "http_client_timeout_in_seconds": 5}, "hash": ":global.REULFyfZkYNKV26XxPA8sucpdPs=:global.stg.xQvhLjMJnc4N3vu6V9IZbpSudKs=:template.rbPvDyZrF3xy9ha5hFAtOclF9eI="}, "gid_paysi_uri": {"template": "{{ string .value }}", "data": {"value": "http://paysi-int.stg-myteksi.com"}, "hash": ":global.CanVTS11z7BUL2kG6nPj52KfHiE=:global.stg.vxuN8ASfInWJpKb5TOMGcVsVa-M=:template.KYaLQxFAf7eAFTln96KWIABn9Dg="}, "gid_percent_of_drivers_with_new_login_flow": {"template": "{{ .percent }}\n", "data": {"percent": 100}, "hash": ":global.XAJiavJU-cJrdo76yI-pIiB-1bw=:global.stg.mjBjY3xT3SuvA_wk4BSaRXGobRs=:template.TJ3mkoIw4s6wvnPBP46noJsCFQE="}, "gid_phone_blocked_cache_ttl_seconds": {"template": "{{ int .value }}", "data": {"value": 10800}, "hash": ":global.vOemELJrWzvNO5OHCBuDsuCNgR4=:global.stg.vOemELJrWzvNO5OHCBuDsuCNgR4=:template.RZf0MUURZFAeaEIiT4lKEwPO7-w="}, "gid_phone_create_pax_retry_config": {"template": "{\n\"enabled\": {{bool .enabled}},\n\"attempts\": {{ int .attempts}},\n\"sleepMs\": {{int .sleep}}\n}", "data": {"attempts": 5, "enabled": true, "sleep": 30}, "hash": ":global.krEeXBxZmPzvStA7RAiLGn-YaU0=:global.stg.:template.gdWwKHhjP-jKbWTB-4vsUTuk4bU="}, "gid_phone_disable_recycled_phone_user_cache": {"template": "{{ bool .value}}", "data": {"value": true}, "hash": ":global.O6LNNbEehXAfXlWEsMlaNW7XX_g=:global.stg.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_phone_email_during_registration_exclusion_list": {"template": "{{ string .value}}\n", "data": {"value": "KH,MM"}, "hash": ":global.7iIZVeA1Cx5tQn5CIwnuX8dPwYE=:global.stg.DnZB5zQfTRXsrxkTW-BbcK4GKSU=:template.bTk5jJ5DY2CHfcM2HCvtLrXCZkM="}, "gid_phone_enable_device_token_validate_check": {"template": "{{ bool .value}}", "data": {"value": true}, "hash": ":global.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:global.stg.:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_phone_enable_purge_low_value_account_email": {"template": "{{ bool .value}}", "data": {"value": false}, "hash": ":global.O6LNNbEehXAfXlWEsMlaNW7XX_g=:global.stg.O6LNNbEehXAfXlWEsMlaNW7XX_g=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_phone_login_history_gap_in_days": {"template": "{{ int .value}}", "data": {"value": 30}, "hash": ":global.Y3JcuRTMJdVQ1YF7vzf55vF9Ny0=:global.stg.Y3JcuRTMJdVQ1YF7vzf55vF9Ny0=:template.uUjClgiahYQrWw6QuusxaIQ1mFM="}, "gid_phone_lookup_cache_ttl_seconds": {"template": "{{ int .value}}", "data": {"value": 600}, "hash": ":global.WsMahfhRMAUfZm5QbMPS-DzaimE=:global.stg.:template.uUjClgiahYQrWw6QuusxaIQ1mFM="}, "gid_phone_max_recycled_accounts_per_phone": {"template": "{{ int .value }}", "data": {"value": 10000}, "hash": ":global.8Ni9E2izrizc_YHSocpGg4pVay4=:global.stg.ddzhKOpRYW5Rj-6f6SKDutIEfEM=:template.RZf0MUURZFAeaEIiT4lKEwPO7-w="}, "gid_phone_num_days_needed_before_create_another_account": {"template": "{{ int .value}}", "data": {"value": 14}, "hash": ":global.bKI6f4LEFLtdNwyqI2E1z2IRcrA=:global.stg.LbjHzE3fVyaL4uIq_9LzwJbNqAM=:template.uUjClgiahYQrWw6QuusxaIQ1mFM="}, "gid_phone_num_days_needed_before_create_another_account_with_banned_phone": {"template": "{{ int .value}}", "data": {"value": 30}, "hash": ":global.nDjZ5FgMxPSOhCleLdKkH4iLNy0=:global.stg.Y3JcuRTMJdVQ1YF7vzf55vF9Ny0=:template.uUjClgiahYQrWw6QuusxaIQ1mFM="}, "gid_phone_otp_expiry": {"template": "{{int .value}}", "data": {"value": 180}, "hash": ":global.SfWh6JC-Nfu49jQlaEjIpjcium8=:global.stg.:template.dBPGNuzAiX2SUdqyZn35eIqNGcw="}, "gid_phone_otp_ratelimit_waiting_time_in_minutes": {"template": "{{ int .value}}", "data": {"value": 1}, "hash": ":global.6pFM0Wqv4OyTOZYSvkqXixkIPXs=:global.stg.:template.uUjClgiahYQrWw6QuusxaIQ1mFM="}, "gid_phone_otp_sms_templates": {"template": "{{toYAML . 0}}", "data": {"pax_android_beta": "<#> %s\n\niJ57pq4MRfA", "pax_android_production": "<#> %s\n\nH9CMNjtZ1r6", "pax_android_staging": "<#> %s\n\nmeigbropiaC"}, "hash": ":global.c3F53wCBB2_-JOyD7t2MPabRtoI=:global.stg.YauexL2baVQSmbJgud48AOKvBRg=:template.3KpkcMNmnr_f3r45c9HN-YHI0u8="}, "gid_phone_recycle_check_rolling_out_percentage": {"template": "{{ int .value}}", "data": {"value": 100}, "hash": ":global.syjBrIyB5cdMmItaCCJ50jJcX9k=:global.stg.syjBrIyB5cdMmItaCCJ50jJcX9k=:template.uUjClgiahYQrWw6QuusxaIQ1mFM="}, "gid_phone_recycled_phone_number_check_list": {"template": "'{{ .stringVariable}}'", "data": {"stringVariable": "{\"shadowMode\":false,\"minWeight\":5,\"signals\":[{\"name\":\"SimIMSI\",\"weight\":5},{\"name\":\"DeviceID\",\"weight\":5},{\"name\":\"Email\",\"weight\":3},{\"name\":\"Location\",\"weight\":2.5},{\"name\":\"HistoryGap\",\"weight\":2.5,\"customConfig\":[1]}]}"}, "hash": ":global.SmTNedgB-gsCjSWtpWP-0qJiTgI=:global.stg.wPC-e2Zymvj8WfclhVF6rcIx_-c=:template.ozfSzGzPWmPNKrX9ZxUMBsW5TMM="}, "gid_phone_recycled_phone_user_cache_ttl_seconds": {"template": "{{ int .value}}", "data": {"value": 15552000}, "hash": ":global.UJkwAj2AUwI-oW72meB_hfbZETY=:global.stg.:template.uUjClgiahYQrWw6QuusxaIQ1mFM="}, "gid_phone_testing_phone_number_list": {"template": "{{ string .value}}", "data": {"value": "65000000000000-65000099999999"}, "hash": ":global.JtLpZlahbh2RQCt8T8Jy3XLvNVQ=:global.stg.R6Bzh9y6UqcSwtAlmm0NQt6Wdmg=:template.lsXd29zLUYpIDxMMByvD9Du4zzI="}, "gid_phone_whitelist_cache_ttl_seconds": {"template": "{{ int .value}}", "data": {"value": 30}, "hash": ":global.3GcaQn29CXX7nvabKZIFVpnbsq4=:global.stg.Y3JcuRTMJdVQ1YF7vzf55vF9Ny0=:template.uUjClgiahYQrWw6QuusxaIQ1mFM="}, "gid_ratelimit_configs": {"template": "{{ range $idx, $service := .services }}\n  '{{$service.name}}':\n    timeInerval : {{$service.timeInerval}}\n    maxReqAllowed : {{$service.maxReqAllowed}}\n{{end}}", "data": {"services": [{"maxReqAllowed": 2, "name": "otpPrompt", "timeInerval": 60}, {"maxReqAllowed": 1, "name": "otpProof", "timeInerval": 1}, {"maxReqAllowed": 5, "name": "verifyOtp", "timeInerval": 1}, {"maxReqAllowed": 1, "name": "patchPrompt", "timeInerval": 1}, {"maxReqAllowed": 1, "name": "resendOtp", "timeInerval": 60}, {"maxReqAllowed": 1, "name": "pinReset", "timeInerval": 1}, {"maxReqAllowed": 3, "name": "verifyPinReset", "timeInerval": 1}]}, "hash": ":global.sflCu35DGGXJuipLCM6NqSzblLs=:global.stg.e7SmUc40g1M7PB037D2NvFNgkBI=:template.xZ8_UCAWrRbjIg3wyE95NHK81XM="}, "gid_ratelimit_instance": {"template": "{\n    \"shadow\": {{bool .shadow}},\n    \"enabled\": {{bool .enabled}},\n    \"qps\": {{int .qps}}\n}", "data": {"enabled": true, "qps": 1000, "shadow": false}, "hash": ":global.7_ghJK7XKYfSAA7mzU7MoHcPacM=:global.stg.tIoZ5eHRzN3vWQfZklakMD01TAs=:template.FCyhvcehTxODOtT1KsBiB1jB5hA="}, "gid_read_puid_from_oauthDB": {"template": "{{ bool .value}}", "data": {"value": false}, "hash": ":global.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:global.stg.O6LNNbEehXAfXlWEsMlaNW7XX_g=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_redis_double-write": {"template": "{{bool .value}}", "data": {"value": true}, "hash": ":global.UXPNRiDwwr7k_e28BkBXW2657Fk=:global.stg.:template.1KkG44n-u7AJnzgLwkNYZ0wa3YY="}, "gid_redis_double-write-sync": {"template": "{{ bool .value}}", "data": {"value": true}, "hash": ":global.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:global.stg.:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_redis_fallback": {"template": "{{ int .value}}", "data": {"value": 0}, "hash": ":global.bKI6f4LEFLtdNwyqI2E1z2IRcrA=:global.stg.:template.uUjClgiahYQrWw6QuusxaIQ1mFM="}, "gid_redis_read-cluster": {"template": "{{bool .value}}", "data": {"value": false}, "hash": ":global.O6LNNbEehXAfXlWEsMlaNW7XX_g=:global.stg.:template.1KkG44n-u7AJnzgLwkNYZ0wa3YY="}, "gid_redis_read-cluster-sync": {"template": "{{bool .value}}", "data": {"value": true}, "hash": ":global.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:global.stg.:template.1KkG44n-u7AJnzgLwkNYZ0wa3YY="}, "gid_redis_read_from_master_percentage": {"template": "{{ int .percent }}\n", "data": {"percent": 100}, "hash": ":global.XAJiavJU-cJrdo76yI-pIiB-1bw=:global.stg.XAJiavJU-cJrdo76yI-pIiB-1bw=:template.E1tk_t5NGA0zQsozNzjLLWMsB5Q="}, "gid_replay-support-log": {"template": "{{int .value}}", "data": {"value": 0}, "hash": ":global.bKI6f4LEFLtdNwyqI2E1z2IRcrA=:global.stg.syjBrIyB5cdMmItaCCJ50jJcX9k=:template.dBPGNuzAiX2SUdqyZn35eIqNGcw="}, "gid_report_email_endpoint": {"template": "{{ string .value }}", "data": {"value": "https://weblogin.stg-myteksi.com/emails/report?token=%v"}, "hash": ":global.CanVTS11z7BUL2kG6nPj52KfHiE=:global.stg.BKkZK5ceQHvcqAbBhPRGBL-FISI=:template.KYaLQxFAf7eAFTln96KWIABn9Dg="}, "gid_request_flow_control_config": {"template": "{{ range $idx, $strategy := .strategies }}\n  '{{$strategy.name}}' :\n    DRIVER : {{$strategy.driver}}\n    TESTSERVICE : {{$strategy.testservice }}\n    MANAGE: {{$strategy.manage }}\n    ENTERPRISE: {{$strategy.enterprise }}\n{{end}}", "data": {"strategies": [{"driver": 0, "enterprise": 0, "manage": 0, "name": "Selective", "testservice": 0}, {"driver": 0, "enterprise": 0, "manage": 0, "name": "Random", "testservice": 0}]}, "hash": ":global.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:global.stg.R4XD0Yp9zBbo0Qio1i_mfJa4cPs=:template.In1d7RvtMt2oSzZnT9SKSJJGElI="}, "gid_resend_verification_email_subject": {"template": "{{ range $idx, $lang := .language }}\n  {{$lang.name}} : \"{{$lang.subject}}\"\n{{end}}", "data": {"language": [{"name": "en", "subject": "Verify your email to keep in touch with us"}, {"name": "zh", "subject": "请确认你的电邮地址，与我们保持联系"}, {"name": "th", "subject": "กรุณายืนยันอีเมลของคุณเพื่อรับข่าวสารจากเรา"}, {"name": "id", "subject": "Verifikasi emailmu untuk terhubung dengan kami"}, {"name": "ms", "subject": "[MS]Verify your email to keep in touch with us"}, {"name": "vi", "subject": "<PERSON><PERSON><PERSON> nhận e-mail để Grab giữ liên lạc với bạn/ <PERSON><PERSON><PERSON> nhận e-mail để Grab phục vụ bạn tốt hơn"}, {"name": "my", "subject": "ကၽြန္ေတာ္တို႔နဲ႔ အဆက္အသြယ္မျပတ္ရွိေနေစရန္ လူႀကီးမင္းရဲ႕ အီးေမးလ္လိပ္စာကို မွန္ကန္ေၾကာင္း အတည္ျပဳေပးရန္"}, {"name": "kh", "subject": "ផ្ទៀងផ្ទាត់អ៊ីម៉ែលរបស់អ្នកដើម្បីរក្សាទំនាក់ទំនងជាមួយពួកយើង"}, {"name": "km", "subject": "[KM]Verify your email to keep in touch with us"}, {"name": "bm", "subject": "<PERSON>la sahkan emel anda untuk terus berhubung dengan kami"}]}, "hash": ":global.RpsAcZWAefJaG75Eo5xGkD4kfJU=:global.stg.:template.jB5TQU5prVT7DGh9aSeFVQVULag="}, "gid_resend_verification_email_template": {"template": "{{ .value }}\n", "data": {"value": "grab_resend_user_verification_email"}, "hash": ":global.baj7f5ln1N8k7rRLTtyxLfoPmoc=:global.stg.:template.HU8X_Ik4ITDQJSX4e5cPL_VmR3k="}, "gid_respect_service_id_in_get_users": {"template": "{{ bool .trust }}\n", "data": {"trust": true}, "hash": ":global.50-O-dUrAb2qY5OC5lv0FAdKcjM=:global.stg.50-O-dUrAb2qY5OC5lv0FAdKcjM=:template.x3wH9Yz7GvkoKy9I61seTenvzWc="}, "gid_retry_remaining_attempts": {"template": "{{int .value}}", "data": {"value": 3}, "hash": ":global.Z9_sVezh8WtByjBqXJvB8Nlq9Hk=:global.stg.:template.dBPGNuzAiX2SUdqyZn35eIqNGcw="}, "gid_retry_remaining_time_in_sec": {"template": "{{int .value}}", "data": {"value": 180}, "hash": ":global.WsMahfhRMAUfZm5QbMPS-DzaimE=:global.stg.odkxQkrjtQSCmmzzwBlErN-rgis=:template.dBPGNuzAiX2SUdqyZn35eIqNGcw="}, "gid_return_false_on_check_match_error": {"template": "{{ bool .value}}\n", "data": {"value": true}, "hash": ":global.O6LNNbEehXAfXlWEsMlaNW7XX_g=:global.stg.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:template.39WX-LGyC4itcwCCsLpjTzZIKbY="}, "gid_return_mismatch_on_errors_list": {"template": "{{ string .list }}", "data": {"list": "statusRequestEntityTooLarge,data_validation_error,image_error_unsupported_format,invalid_image_size,multiple_faces,no_face_found,size_too_large"}, "hash": ":global.tSfeveSlQoIMPcf1z1VwFCygef4=:global.stg.ii5O4zBTyK_nVVNLTsdT5jPq-Nc=:template.pKIP_4SEHZS6f06EEHFGuK_l5gc="}, "gid_riskproxy_http_client_config": {"template": "host: '{{ string .host }}'\ntimeoutInMs: {{ int .timeoutInMs }}\nmaxConcurrent: {{ int .maxConcurrent }}\nerrorThresholdPercent: {{ int .errorThresholdPercent }}\n", "data": {"errorThresholdPercent": 50, "host": "https://stg-iam-riskgw01.stg-grabpay.com", "maxConcurrent": 100, "timeoutInMs": 2000}, "hash": ":global.O6TmncEOMQZ80oua6DOOI3aj3OY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.bc2QQ_YmiAHK5L8tUaGQBCQrN44="}, "gid_riskproxy_http_client_enabled": {"template": "{{ bool .value}}", "data": {"value": true}, "hash": ":global.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_security_alert_rollout_pct": {"template": "{{int .value}}", "data": {"value": 100}, "hash": ":global.bKI6f4LEFLtdNwyqI2E1z2IRcrA=:global.stg.syjBrIyB5cdMmItaCCJ50jJcX9k=:template.dBPGNuzAiX2SUdqyZn35eIqNGcw="}, "gid_security_alert_rollout_phone_list": {"template": "{{ string .value}}", "data": {"value": "19712350444,6583334450,12536938357,85555663334,6233367894550,6088774321,84355526834,66855540640,62878555580"}, "hash": ":global.pIP-2yJkUYO--V2KKBQVSYSgpHk=:global.stg.beMnRmlY6KZj1WALwdGHBHUOr8A=:template.lsXd29zLUYpIDxMMByvD9Du4zzI="}, "gid_send_deactivation_email_subject": {"template": "{{ range $idx, $l := .lang }}\n  {{$l.name}} : {{$l.value}}\n{{end}}", "data": {"lang": [{"name": "en", "value": "We miss you!"}, {"name": "zh", "value": "好久不见！"}]}, "hash": ":global.ihiMKKq0bIvqZpcnSfo477mHw0M=:global.stg.:template.UBguF9UxrylAAkLKHQCdFXd2hX0="}, "gid_send_deactivation_email_template": {"template": "{{ string .template }}", "data": {"template": "grab_deactivation_email"}, "hash": ":global.il6FGUUaPkByTBuqDZVdXHVefRo=:global.stg.339aQ64jtSyxzKTKG5zGLqOe5l8=:template.GfgLU_lHljWajKMbfNB-RTRNg_I="}, "gid_send_deactivation_email_template_1": {"template": "{{ string .value }}", "data": {"value": "grab_deactivation_email_1"}, "hash": ":global.Q8pXoYttwH1YrZJeTsI3YwHjPF8=:global.stg.:template.KYaLQxFAf7eAFTln96KWIABn9Dg="}, "gid_send_deactivation_email_template_2": {"template": "{{ string .value }}", "data": {"value": "grab_deactivation_email_2"}, "hash": ":global.RFzBPIr5q2VHYdBrp6f1CjEB2Xc=:global.stg.:template.KYaLQxFAf7eAFTln96KWIABn9Dg="}, "gid_send_deactivation_email_template_3": {"template": "{{ string .value }}", "data": {"value": "grab_deactivation_email_3"}, "hash": ":global.zzeA8b0f3F5X1wuJu1d9f6IOjMk=:global.stg.:template.KYaLQxFAf7eAFTln96KWIABn9Dg="}, "gid_send_otp_pushchainedinbox": {"template": "'{{ string .value }}'\n", "data": {"value": "bce9323f76d2402cab00fa038f471cd8,acf7f99b1a76430b805905ab6e6f482a"}, "hash": ":global.RgpWXE0x1CtLdkBVZbOZkb4joas=:global.stg.FHmUtODgepQGBJ1UMSAqp64c-Zk=:template.iuy6pefmpw8EOLvzfadIsgAA7M8="}, "gid_skip_otp_check_phone_number_list": {"template": "{{ string .value}}", "data": {"value": "***********#345678,***********#4567,639172408651#408651,6566576089488014#888616,6281555511116#123456,6281555511118#123456,6281555511119#123456"}, "hash": ":global.pIP-2yJkUYO--V2KKBQVSYSgpHk=:global.stg.j40StykDqT6IDhfJSfDVpxjY16Y=:template.lsXd29zLUYpIDxMMByvD9Du4zzI="}, "gid_skip_scope_gateway_config_scopeDef_malformed_json": {"template": "{{bool .value}}\n", "data": {"value": false}, "hash": ":global.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:global.stg.O6LNNbEehXAfXlWEsMlaNW7XX_g=:template.FukAbzh9tmxxzg8l5RdZzCkwi6Q="}, "gid_spring_config": {"template": "'{{ string .value }}'", "data": {"value": "{ \"get_user_info_url_format\": \"https://stg-spring-moca01.stg-grabpay.com/spring-wallet/v4/getUserInfo?isDenormalize=true&currency=VND&msgID=%s&userID=%d&userType=%s\",  \"http_client_timeout_in_seconds\": 5, \"cb_setting\": {  \"tag\": \"grabid:spring\",\"timeoutInMs\": 3000,\"maxConcurrentReq\": 1000,  \"errorPercentThreshold\": 50, \"volumePercentThreshold\": 20, \"sleepWindowInMs\": 100, \"maxQueueSize\": 5000  }}"}, "hash": ":global.CanVTS11z7BUL2kG6nPj52KfHiE=:global.stg.rDjBor12TC0_KBF83e_TLtpzmUo=:template.4LTVYHygXHcNstr1WqJHh7taIdQ="}, "gid_spring_enabled": {"template": "{{ bool .value }}", "data": {"value": true}, "hash": ":global.O6LNNbEehXAfXlWEsMlaNW7XX_g=:global.stg.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:template.agqciiThdyC8wUwL4ZJbPn3dsso="}, "gid_spring_fallback_to_grabid_user": {"template": "{{ bool .value }}", "data": {"value": false}, "hash": ":global.O6LNNbEehXAfXlWEsMlaNW7XX_g=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.agqciiThdyC8wUwL4ZJbPn3dsso="}, "gid_token_issued_at_time_correction_sec": {"template": "{{ int .sec }}", "data": {"sec": 3}, "hash": ":global.sMXLxywc2wPTjcPlF-d1TC6Jkdk=:global.stg.sMXLxywc2wPTjcPlF-d1TC6Jkdk=:template.SWMXxBPNiMMrg7VTXoewvtUrXuw="}, "gid_twilio_enabled": {"template": "{{ bool .value }}", "data": {"value": false}, "hash": ":global.O6LNNbEehXAfXlWEsMlaNW7XX_g=:global.stg.O6LNNbEehXAfXlWEsMlaNW7XX_g=:template.agqciiThdyC8wUwL4ZJbPn3dsso="}, "gid_use_gredis2_and_readreplica": {"template": "{{bool .value}}", "data": {"value": true}, "hash": ":global.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:global.stg.:template.1KkG44n-u7AJnzgLwkNYZ0wa3YY="}, "gid_use_sso_token_for_oauth_flow": {"template": "{{ bool .value }}", "data": {"value": true}, "hash": ":global.O6LNNbEehXAfXlWEsMlaNW7XX_g=:global.stg.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:template.agqciiThdyC8wUwL4ZJbPn3dsso="}, "gid_use_validate_profile_endpoint": {"template": "{{ bool .mode }}\n", "data": {"mode": true}, "hash": ":global.O5MtMZBviw4BsmgW4ypV2gzA_R8=:global.stg.:template.10XU8RMwsTQDdja8hSlFRp6h9hw="}, "gid_user_activity_recording_enabled": {"template": "{{ bool .value }}", "data": {"value": true}, "hash": ":global.O6LNNbEehXAfXlWEsMlaNW7XX_g=:global.stg.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:template.agqciiThdyC8wUwL4ZJbPn3dsso="}, "gid_user_activity_recording_expiry_in_minutes": {"template": "{{ int .value }}", "data": {"value": 259200}, "hash": ":global.-fYX6hEUmjo2ynnr-EJnkscPiEM=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.RZf0MUURZFAeaEIiT4lKEwPO7-w="}, "gid_user_addMetadata_retry_attempts": {"template": "attempts: {{ int .attempts }}\n", "data": {"attempts": 2}, "hash": ":global.oPvu4f5-ApJ8X7_2iaDU51pc5zc=:global.stg.:template.N0ywjixZ2BPmDtKtu8c1yvJvxRg="}, "gid_user_addMetadata_retry_sleep_ms": {"template": "mode: {{ int .sleep_ms }}", "data": {"sleep_ms": 20}, "hash": ":global._PgVaPq-NqWabSNOO_OkC15MBkM=:global.stg.5wRvUaxyUILzU74Pamd9JQFUL3k=:template.LvW0aYfuZf5A8yuKW5Z7ETf9l2Y="}, "gid_user_contact_cache_ttl_seconds": {"template": "{{ int .value }}", "data": {"value": 0}, "hash": ":global.pwWCv_jR4HUlOL-ZoEKAlGAod8Q=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.RZf0MUURZFAeaEIiT4lKEwPO7-w="}, "gid_user_safe_id_cache_ttl_seconds": {"template": "{{ int .value }}", "data": {"value": 72000}, "hash": ":global.cTLL5AHl1OnUgSqq5UTxXi_mUsQ=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.RZf0MUURZFAeaEIiT4lKEwPO7-w="}, "gid_validate_auth_allowed_claim": {"template": "{{ bool .mode }}", "data": {"mode": true}, "hash": ":global.XrmYOQvarWnx4hDehA0gcvJvwJs=:global.stg.y3z_0SsnMPGAlWUgZGWvseE9HdQ=:template.pFC3E9JAg-19JiBGOg7cmUwRE-A="}, "gid_validate_profile_endpoint_rollout": {"template": "{{ int .value }}\n", "data": {"value": 100}, "hash": ":global.3xiRTwCSbSvU-TwuVI8qlk6qToc=:global.stg.bGtjtdzUV_rxuSylhmmP_Y7Anps=:template.ogjREJWeuP2798osN9j7OrlL9Xk="}, "gid_verif_link_expiry": {"template": "{{ int .value}}", "data": {"value": 2}, "hash": ":global.tKyRJ_0BFnUpYcd3THzHWa-DkUw=:global.stg.:template.uUjClgiahYQrWw6QuusxaIQ1mFM="}, "gid_verify_google_token_locally_enabled": {"template": "{{bool .value}}", "data": {"value": true}, "hash": ":global.O6LNNbEehXAfXlWEsMlaNW7XX_g=:global.stg.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:template.1KkG44n-u7AJnzgLwkNYZ0wa3YY="}, "gid_write_puid_to_oauthDB": {"template": "{{ bool .value}}", "data": {"value": true}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gidsdk_grpc_rollout_config": {"template": "grpc_rollouts: {{ range $serviceID, $rollout := .grpc_rollouts }}\n    '{{ $serviceID }}': {{ range $method, $percentage := $rollout }}\n        '{{ $method }}' : {{ $percentage }}\n    {{ end }}\n{{ end }}\n", "data": {"grpc_rollouts": {"ADS": {"ServiceUsers_GetUserID": 100, "refreshIntervalSec": 30}, "DAPBOLT": {"ServiceUsers_RegisterUser": 50, "refreshIntervalSec": 300}, "DAPIGNITE": {"refreshIntervalSec": 300}, "DAPSTELLAR": {"ServiceUsers_GetProfile": 50, "ServiceUsers_UpdateProfile": 50, "Users_GetMe": 50, "Users_login": 50, "refreshIntervalSec": 300}, "DEFAULT": {"refreshIntervalSec": 900}, "DEVELOPER": {"Users_GetMe": 50, "Users_GetUser": 50, "Users_GetUserMfa": 50, "refreshIntervalSec": 300}, "DRIVER": {"ServiceUsers_CreateUser": 50, "ServiceUsers_GetProfile": 50, "ServiceUsers_UpdateProfile": 100, "Users_GetMe": 50, "Users_LoginWithMFA": 20, "Users_Logout": 50, "Users_MeTokensPut": 50, "Users_login": 0, "refreshIntervalSec": 300}, "DRIVER-PROFILES": {"ServiceUsers_GetProfile": 50, "refreshIntervalSec": 300}, "FOODCMSAPI": {"ServiceUsers_RegisterUser": 50, "Users_GetMe": 50, "Users_login": 50, "refreshIntervalSec": 300}, "FOODMAX": {"ServiceUsers_CreateUser": 50, "ServiceUsers_loginUser": 50, "Users_AddLoginMethod": 50, "Users_Logout": 50, "Users_login": 50, "refreshIntervalSec": 300}, "FRAUD": {"ServiceUsers_GetProfile": 50, "ServiceUsers_GetUserID": 50, "Users_GetUser": 50, "Users_GetUserMfa": 0, "refreshIntervalSec": 300}, "GEO-TOOLS": {"ServiceUsers_RegisterUser": 50, "Users_Logout": 50, "Users_RefreshToken": 50, "Users_login": 50, "refreshIntervalSec": 300}, "GRABMESSAGING": {"ServiceUsers_GetUserID": 50, "Users_GetUser": 50, "refreshIntervalSec": 300}, "GRABX": {"ServiceUsers_RegisterUser": 50, "Users_GetUser": 50, "Users_login": 50, "refreshIntervalSec": 300}, "GRAB_CONNECT": {"Users_GetUser": 50, "Users_SearchByPhoneNumber": 50, "Users_VerifyOTPV2": 3, "Users_VerifyOtpGuestV2": 50, "refreshIntervalSec": 300}, "GROWTH-REFERRAL": {"ServiceUsers_GetUserID": 50, "refreshIntervalSec": 300}, "PASSENGER": {"ServiceUsers_CreateUser": 50, "ServiceUsers_GetUserID": 50, "ServiceUsers_UpdateUser": 50, "ServiceUsers_loginUser": 50, "Users_GetMe": 50, "Users_GetUser": 0, "Users_GetUserMfa": 0, "refreshIntervalSec": 300}, "PAYSIARIES": {"Users_GetUser": 50, "Users_login": 0, "refreshIntervalSec": 300}, "TOLONG": {"Users_GetUser": 50, "refreshIntervalSec": 300}, "TRUST_WATCHTOWER": {"Users_GetMe": 50, "Users_Logout": 50, "Users_login": 50, "refreshIntervalSec": 300}}}, "hash": ":global.hxizPDG3FNFKo601am5l72R0Plk=:global.stg.kj5o-5VX00LbiT37gGweogJhbg8=:template.YxsNqmn53nVjvaJZtsBtRUqkY1E="}, "grabIDOauthMasterDB:config": {"template": "username: {{ .username }}\npassword: {{ .password }}\nhostname: {{ .hostname }}\nmaxidle: {{ .maxidle}}\nmaxopen: {{ .maxopen}}\ndbname: {{ .dbname}}  ", "data": {"dbname": "iam_service", "hostname": "grab-id-db.identity.stg.g-bank.app:3306", "key": "value", "maxidle": 2, "maxopen": 10, "path": "stg/rds/credentials/stg-grabid-oauth/grab_id", "role": "dev-grab-id", "username": "MYSQL_DB_USER", "password": "MYSQL_DB_PASSWORD"}, "hash": ":global.LC-FGwAjG6er7TdHwaPm9CezWyE=:global.stg.xyd-1PyxjyRcyiPs3PcG7ZYIGXc=:template.vjrdKrMOnxoTSmJ485im41F_UBM="}, "grabdevices_config": {"template": "serverAddress: '{{ string .serverAddress }}'\nserviceDiscoveryEnabled: {{ bool .serviceDiscoveryEnabled }}\npoolMaxOpen: {{ int .poolMaxOpen }}\ndialTimeoutInMs: {{ int .dialTimeoutInMs }}\ndefaultHealthCheckFrequencyInSec: {{ int .defaultHealthCheckFrequencyInSec }}\ndefaultHealthCheckTimeoutInSec: {{ int .defaultHealthCheckTimeoutInSec }}\ntimeout: {{ int .timeout }}\nmax_concurrent_requests: {{ int .max_concurrent_requests }}\nerror_percent_threshold: {{ int .error_percent_threshold }}\n", "data": {"defaultHealthCheckFrequencyInSec": 5, "defaultHealthCheckTimeoutInSec": 5, "dialTimeoutInMs": 5000, "error_percent_threshold": 25, "max_concurrent_requests": 1000, "poolMaxOpen": 10, "serverAddress": "", "serviceDiscoveryEnabled": false, "timeout": 4000}, "hash": ":global.5bt6-vZagKd7_VGFtZQJiQy2_Fc=:global.stg.5bt6-vZagKd7_VGFtZQJiQy2_Fc=:template.101eFivi824Uop7mdnaCQMMbcgY="}, "grabdevices_enabled": {"template": "{{ bool .enabled }}", "data": {"enabled": true}, "hash": ":global.KavstVz5q3T209i8fVHJE7hIjik=:global.stg.-xMPX7B2U52h6D75KByRJt_VyVg=:template.V9zi_N_gYu1eQdfUImZv__yWmFQ="}, "grabid_compare-db-config": {"template": "{{bool .value}}", "data": {"value": false}, "hash": ":global.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:global.stg.O6LNNbEehXAfXlWEsMlaNW7XX_g=:template.1KkG44n-u7AJnzgLwkNYZ0wa3YY="}, "grabid_facebook-login-skip-check-email": {"template": "{{ bool .value}}", "data": {"value": true}, "hash": ":global.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:global.stg.:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "grabid_google-login-skip-check-email": {"template": "{{bool .value}}", "data": {"value": true}, "hash": ":global.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:global.stg.:template.1KkG44n-u7AJnzgLwkNYZ0wa3YY="}, "grabid_partner_notification": {"template": "{{ bool .value}}", "data": {"value": true}, "hash": ":global.O6LNNbEehXAfXlWEsMlaNW7XX_g=:global.stg.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "grabid_phone_auth_notification_worker_pool_size": {"template": "{{ int .value}}", "data": {"value": 40}, "hash": ":global.aWtLjNcOiGOQ1ZDsL8AVfei9Hl4=:global.stg.:template.uUjClgiahYQrWw6QuusxaIQ1mFM="}, "grabid_service-load-db-conf": {"template": "{{ range $idx, $service := .services }}\n  {{$service.name}} : {{$service.value}}\n{{end}}", "data": {"services": [{"name": "MANAGE", "value": true}, {"name": "APP_DRIVE", "value": true}, {"name": "DRIVER", "value": false}, {"name": "PASSENGER", "value": true}, {"name": "FLAGSVIEW", "value": true}]}, "hash": ":global.QPc5doQxmVwURSt9p9Zan_CWn24=:global.stg.NAZ-ty3rU3Lq7O-tnKSB8zVPnpQ=:template.Jen5wd4_-XDtuoURz-GF1t7DTTc="}, "grabid_service_role": {"template": "{{ range $idx, $service := .services }}\n  '{{$service.name}}': {{ .strList}}\n{{end}}", "data": {"services": [{"name": "USER_MANAGER", "strList": "[\"DIG<PERSON><PERSON><PERSON>\",\"CUSTOMER_MASTER\",\"ID_EXP\"]"}, {"name": "GRABSECURE_CHALLENGE_ACK", "strList": "[\"<PERSON><PERSON><PERSON><PERSON><PERSON>\",\"TX_SIGN\",\"ID_EXP\"]"}, {"name": "OAUTH2_ADMIN", "strList": "[\"OAUTH2_ADMIN\"]"}, {"name": "OAUTH2_MANAGE_READER", "strList": "[\"OAUTH2_ADMIN\"]"}, {"name": "OAUTH2_MANAGE_WRITER", "strList": "[\"OAUTH2_ADMIN\"]"}, {"name": "OAUTH2_TOKEN_VERIFIERS", "strList": "[\"PARTNER_GATEWAY\",\"API_GATEWAY\"]"}, {"name": "PROFILE_UPDATE", "strList": "[\"DIG<PERSON><PERSON><PERSON>\",\"ID_EXP\",\"CUSTOMER_EXPERIENCE\"]"}]}, "hash": ":global.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:global.stg.XW9CWNsVFM71DeWrkdMvDCtjn6k=:template.MSBEGqfa6n0IHrf5LIH8QHAm6Ck="}, "grabidgk_limit_rps_per_endpoint": {"template": "defaultRPS: {{ .defaultRPS }}\nlimits: \n    {{ range $client, $limit := .limits }}\n    '{{ $client }}': {{ $limit }}\n  {{ end }}", "data": {"defaultRPS": 5000}, "hash": ":global.gl-jsu6PrvEoScTyD-nq42vhiNg=:global.stg.:template.eKzYpCgcBlI7jHnq2Q6NJVgaoXk="}, "gredis2HystrixEnabled": {"template": "{{bool .value}}", "data": {"value": true}, "hash": ":global.O6LNNbEehXAfXlWEsMlaNW7XX_g=:global.stg.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:template.1KkG44n-u7AJnzgLwkNYZ0wa3YY="}, "gsstream": {"template": "producerEnabled: {{ bool .producerEnabled }}\nconsumerEnabled: {{ bool .consumerEnabled }}\n", "data": {"consumerEnabled": true, "producerEnabled": false}, "hash": ":global.PRi76YAafCdGdi5IP70WsbQidNo=:global.stg.oSPu93Uj60wYNIhMiUqTGZmOxVY=:template.stUG5dE5meDCrfCkruVQTbJpUHI="}, "loginstreamv2": {"template": "producerEnabled: {{ bool .producerEnabled }}\ndtoName: '{{ string .dtoName }}'\nconsumerEnabled: {{ bool .consumerEnabled }}\nenableTLL: {{ bool .enableTLL }}\ntlsConfig: {{ .tlsConfig }}\nbrokers: \n{{ range $val := .brokers }}\n  - {{ $val }}\n{{ end }}\n", "data": {"consumerEnabled": false, "producerEnabled": true, "enableTLL": true, "tlsConfig": {}, "dtoName": "GrabIDLogin", "brokers": ["b-1.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094"]}, "hash": ":global.PRi76YAafCdGdi5IP70WsbQidNo=:global.stg.oSPu93Uj60wYNIhMiUqTGZmOxVY=:template.stUG5dE5meDCrfCkruVQTbJpUHI="}, "hedwig_client_config": {"template": "serverAddress: '{{ string .serverAddress }}'\nserviceDiscoveryEnabled: {{ bool .serviceDiscoveryEnabled }}\npoolMaxOpen: {{ int .poolMaxOpen }}\ndialTimeoutInMs: {{ int .dialTimeoutInMs }}\ndefaultHealthCheckFrequencyInSec: {{ int .defaultHealthCheckFrequencyInSec }}\ndefaultHealthCheckTimeoutInSec: {{ int .defaultHealthCheckTimeoutInSec }}\ntimeout: {{ int .timeout }}\nmax_concurrent_requests: {{ int .max_concurrent_requests }}\nerror_percent_threshold: {{ int .error_percent_threshold }}\n", "data": {"defaultHealthCheckFrequencyInSec": 5, "defaultHealthCheckTimeoutInSec": 5, "dialTimeoutInMs": 5000, "error_percent_threshold": 25, "max_concurrent_requests": 100, "poolMaxOpen": 10, "serverAddress": "", "serviceDiscoveryEnabled": false, "timeout": 3000}, "hash": ":global.2fICZtQj0DijDvDvikTR8dpkoeg=:global.stg.4XxXV1CJV2szfpuXkN5TUavu05g=:template.101eFivi824Uop7mdnaCQMMbcgY="}, "gid_digibank_pigeon_config": {"template": "serverAddress: '{{ string .serverAddress }}'\nsendEmailServerPath: '{{ string .sendEmailServerPath }}'\nsendSMSServerPath: '{{ string .sendSMSServerPath }}'\nreleasePushTokenServerPath: '{{ string .releasePushTokenServerPath }}'\npushNotificationServerPath: '{{ string .pushNotificationServerPath }}'\nserviceDiscoveryEnabled: {{ bool .serviceDiscoveryEnabled }}\npoolMaxOpen: {{ int .poolMaxOpen }}\ndialTimeoutInMs: {{ int .dialTimeoutInMs }}\ndefaultHealthCheckFrequencyInSec: {{ int .defaultHealthCheckFrequencyInSec }}\ndefaultHealthCheckTimeoutInSec: {{ int .defaultHealthCheckTimeoutInSec }}\ntimeout: {{ int .timeout }}\nmax_concurrent_requests: {{ int .max_concurrent_requests }}\nerror_percent_threshold: {{ int .error_percent_threshold }}\n", "data": {"defaultHealthCheckFrequencyInSec": 5, "defaultHealthCheckTimeoutInSec": 5, "dialTimeoutInMs": 5000, "error_percent_threshold": 25, "max_concurrent_requests": 100, "poolMaxOpen": 10, "serverAddress": "http://id-exp.identity.svc.cluster.local", "sendEmailServerPath": "/v1/email", "sendSMSServerPath": "/v1/sms", "releasePushTokenServerPath": "/v1/internal/push/token", "pushNotificationServerPath": "/v1/push", "serviceDiscoveryEnabled": false, "timeout": 6000}, "hash": ":global.2fICZtQj0DijDvDvikTR8dpkoeg=:global.stg.4XxXV1CJV2szfpuXkN5TUavu05g=:template.101eFivi824Uop7mdnaCQMMbcgY="}, "gid_digibank_hedwig_config": {"template": "serverAddress: '{{ string .serverAddress }}'\nsendEmailServerPath: '{{ string .sendEmailServerPath }}'\nsendSMSServerPath: '{{ string .sendSMSServerPath }}'\nreleasePushTokenServerPath: '{{ string .releasePushTokenServerPath }}'\npushNotificationServerPath: '{{ string .pushNotificationServerPath }}'\npushInboxServerPath: '{{ string .pushInboxServerPath }}'\nserviceDiscoveryEnabled: {{ bool .serviceDiscoveryEnabled }}\npoolMaxOpen: {{ int .poolMaxOpen }}\ndialTimeoutInMs: {{ int .dialTimeoutInMs }}\ndefaultHealthCheckFrequencyInSec: {{ int .defaultHealthCheckFrequencyInSec }}\ndefaultHealthCheckTimeoutInSec: {{ int .defaultHealthCheckTimeoutInSec }}\ntimeout: {{ int .timeout }}\nmax_concurrent_requests: {{ int .max_concurrent_requests }}\nerror_percent_threshold: {{ int .error_percent_threshold }}\n", "data": {"defaultHealthCheckFrequencyInSec": 5, "defaultHealthCheckTimeoutInSec": 5, "dialTimeoutInMs": 5000, "error_percent_threshold": 25, "max_concurrent_requests": 100, "poolMaxOpen": 10, "serverAddress": "http://hedwig.backend-rtc-app-01.svc.cluster.local", "sendEmailServerPath": "/hedwig/v1/email", "sendSMSServerPath": "/hedwig/v1/sms", "releasePushTokenServerPath": "/hedwig/v1/push/token", "pushNotificationServerPath": "/hedwig/v1/push", "pushInboxServerPath": "/hedwig/v1/pushInbox", "serviceDiscoveryEnabled": false, "timeout": 6000}, "hash": ":global.2fICZtQj0DijDvDvikTR8dpkoeg=:global.stg.4XxXV1CJV2szfpuXkN5TUavu05g=:template.101eFivi824Uop7mdnaCQMMbcgY="}, "gid_mfa_push_get_challenge_id_from_auth_cache": {"template": "{{ bool .value }}", "data": {"value": true}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.prod.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_push_hedwig_template_id": {"template": "{{ string .value }}", "data": {"value": "c8f65709-71da-47df-9c8c-ee60b81c811f"}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.prod.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "push_allowed_action_seconds": {"template": "{{ int .value }}", "data": {"value": 45}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_digibank_grabdevices_config": {"template": "serverAddress: '{{ string .serverAddress }}'\nupdateDeviceInfoServerPath: '{{ string .updateDeviceInfoServerPath }}'\nserviceDiscoveryEnabled: {{ bool .serviceDiscoveryEnabled }}\npoolMaxOpen: {{ int .poolMaxOpen }}\ndialTimeoutInMs: {{ int .dialTimeoutInMs }}\ndefaultHealthCheckFrequencyInSec: {{ int .defaultHealthCheckFrequencyInSec }}\ndefaultHealthCheckTimeoutInSec: {{ int .defaultHealthCheckTimeoutInSec }}\ntimeout: {{ int .timeout }}\nmax_concurrent_requests: {{ int .max_concurrent_requests }}\nerror_percent_threshold: {{ int .error_percent_threshold }}\n", "data": {"defaultHealthCheckFrequencyInSec": 5, "defaultHealthCheckTimeoutInSec": 5, "dialTimeoutInMs": 5000, "error_percent_threshold": 25, "max_concurrent_requests": 100, "poolMaxOpen": 10, "serverAddress": "http://grabdevices.backend-rtc-app-01.svc.cluster.local", "updateDeviceInfoServerPath": "/grabdevices/updateDeviceInfo", "serviceDiscoveryEnabled": false, "timeout": 6000}, "hash": ":global.2fICZtQj0DijDvDvikTR8dpkoeg=:global.stg.4XxXV1CJV2szfpuXkN5TUavu05g=:template.101eFivi824Uop7mdnaCQMMbcgY="}, "gid_digibank_login_sources": {"template": "{{ .sourcesMap }}\n", "data": {"sourcesMap": "{\"0cf3f9da-97ae-4525-9882-db3be487297f\":true, \"66b04bfe-fa03-4a14-94bd-c165a51adc3d\":true, \"e287a41b-d145-4bcc-8fba-a3de3d899d25\":true, \"2d428317-e3a6-4173-8c66-9e844f4610fd\":true, \"4d0668ac-1531-40ad-a9c8-1e40b7d3dbb1\":true, \"0c85636d-f36f-4bab-ad2a-d84a1fd9dc6e\":true, \"5e709d74-9b05-4070-ba84-5dd22c63560a\":true }"}, "hash": ":global.2fICZtQj0DijDvDvikTR8dpkoeg=:global.stg.4XxXV1CJV2szfpuXkN5TUavu05g=:template.101eFivi824Uop7mdnaCQMMbcgY="}, "gid_digibank_risk_config": {"template": "serverAddress: '{{ string .serverAddress }}'\nriskCheckPartnerTxPath: '{{ string .riskCheckPartnerTxPath }}'\nserviceDiscoveryEnabled: {{ bool .serviceDiscoveryEnabled }}\npoolMaxOpen: {{ int .poolMaxOpen }}\ndialTimeoutInMs: {{ int .dialTimeoutInMs }}\ndefaultHealthCheckFrequencyInSec: {{ int .defaultHealthCheckFrequencyInSec }}\ndefaultHealthCheckTimeoutInSec: {{ int .defaultHealthCheckTimeoutInSec }}\ntimeout: {{ int .timeout }}\nmax_concurrent_requests: {{ int .max_concurrent_requests }}\nerror_percent_threshold: {{ int .error_percent_threshold }}\n", "data": {"defaultHealthCheckFrequencyInSec": 5, "defaultHealthCheckTimeoutInSec": 5, "dialTimeoutInMs": 5000, "error_percent_threshold": 25, "max_concurrent_requests": 100, "poolMaxOpen": 10, "serverAddress": "http://id-exp.identity.svc.cluster.local", "riskCheckPartnerTxPath": "/v1/pre-login/risk-check", "serviceDiscoveryEnabled": false, "timeout": 3000}, "hash": ":global.2fICZtQj0DijDvDvikTR8dpkoeg=:global.stg.4XxXV1CJV2szfpuXkN5TUavu05g=:template.101eFivi824Uop7mdnaCQMMbcgY="}, "gid_digibank_customer_experience_config": {"template": "serverAddress: '{{ string .serverAddress }}'\nserviceDiscoveryEnabled: {{ bool .serviceDiscoveryEnabled }}\npoolMaxOpen: {{ int .poolMaxOpen }}\ndialTimeoutInMs: {{ int .dialTimeoutInMs }}\ndefaultHealthCheckFrequencyInSec: {{ int .defaultHealthCheckFrequencyInSec }}\ndefaultHealthCheckTimeoutInSec: {{ int .defaultHealthCheckTimeoutInSec }}\ntimeout: {{ int .timeout }}\nmax_concurrent_requests: {{ int .max_concurrent_requests }}\nerror_percent_threshold: {{ int .error_percent_threshold }}\ngetActiveProfileIDServerPath: '{{ string .getActiveProfileIDServerPath }}'\nisValidProfileServerPath: '{{ string .isValidProfileServerPath }}'\n", "data": {"defaultHealthCheckFrequencyInSec": 5, "defaultHealthCheckTimeoutInSec": 5, "dialTimeoutInMs": 5000, "error_percent_threshold": 25, "max_concurrent_requests": 100, "poolMaxOpen": 10, "serverAddress": "http://customer-experience.onboarding.svc.cluster.local", "getActiveProfileIDServerPath": "/api/v1/getActiveProfileID", "isValidProfileServerPath": "/api/v1/isValidProfile", "serviceDiscoveryEnabled": false, "timeout": 3000}}, "gid_digibank_customer_master_config": {"template": "serverAddress: '{{ string .serverAddress }}'\nserviceDiscoveryEnabled: {{ bool .serviceDiscoveryEnabled }}\npoolMaxOpen: {{ int .poolMaxOpen }}\ndialTimeoutInMs: {{ int .dialTimeoutInMs }}\ndefaultHealthCheckFrequencyInSec: {{ int .defaultHealthCheckFrequencyInSec }}\ndefaultHealthCheckTimeoutInSec: {{ int .defaultHealthCheckTimeoutInSec }}\ntimeout: {{ int .timeout }}\nmax_concurrent_requests: {{ int .max_concurrent_requests }}\nerror_percent_threshold: {{ int .error_percent_threshold }}\n", "data": {"defaultHealthCheckFrequencyInSec": 5, "defaultHealthCheckTimeoutInSec": 5, "dialTimeoutInMs": 5000, "error_percent_threshold": 25, "max_concurrent_requests": 100, "poolMaxOpen": 10, "serverAddress": "http://customer-master.onboarding.svc.cluster.local", "serviceDiscoveryEnabled": false, "timeout": 3000}, "hash": ":global.2fICZtQj0DijDvDvikTR8dpkoeg=:global.stg.4XxXV1CJV2szfpuXkN5TUavu05g=:template.101eFivi824Uop7mdnaCQMMbcgY="}, "gid_digibank_tx_sign_config": {"template": "serverAddress: '{{ string .serverAddress }}'\nregisterServerPath: '{{ string .registerServerPath }}'\nderegisterServerPath: '{{ string .deregisterServerPath }}'\nserviceDiscoveryEnabled: {{ bool .serviceDiscoveryEnabled }}\npoolMaxOpen: {{ int .poolMaxOpen }}\ndialTimeoutInMs: {{ int .dialTimeoutInMs }}\ndefaultHealthCheckFrequencyInSec: {{ int .defaultHealthCheckFrequencyInSec }}\ndefaultHealthCheckTimeoutInSec: {{ int .defaultHealthCheckTimeoutInSec }}\ntimeout: {{ int .timeout }}\nmax_concurrent_requests: {{ int .max_concurrent_requests }}\nerror_percent_threshold: {{ int .error_percent_threshold }}\n", "data": {"defaultHealthCheckFrequencyInSec": 5, "defaultHealthCheckTimeoutInSec": 5, "dialTimeoutInMs": 5000, "error_percent_threshold": 25, "max_concurrent_requests": 100, "poolMaxOpen": 10, "serverAddress": "http://transaction-signing.identity.svc.cluster.local", "registerServerPath": "/v1/tx-sign/register", "deregisterServerPath": "/v1/tx-sign/deregister", "serviceDiscoveryEnabled": false, "timeout": 3000}, "hash": ":global.2fICZtQj0DijDvDvikTR8dpkoeg=:global.stg.4XxXV1CJV2szfpuXkN5TUavu05g=:template.101eFivi824Uop7mdnaCQMMbcgY="}, "gid_digibank_device_id_verifier_config": {"template": "serverAddress: '{{ string .serverAddress }}'\nverifyDeviceIDServerPath: '{{ string .verifyDeviceIDServerPath }}'\nserviceDiscoveryEnabled: {{ bool .serviceDiscoveryEnabled }}\npoolMaxOpen: {{ int .poolMaxOpen }}\ndialTimeoutInMs: {{ int .dialTimeoutInMs }}\ndefaultHealthCheckFrequencyInSec: {{ int .defaultHealthCheckFrequencyInSec }}\ndefaultHealthCheckTimeoutInSec: {{ int .defaultHealthCheckTimeoutInSec }}\ntimeout: {{ int .timeout }}\nmax_concurrent_requests: {{ int .max_concurrent_requests }}\nerror_percent_threshold: {{ int .error_percent_threshold }}\n", "data": {"defaultHealthCheckFrequencyInSec": 5, "defaultHealthCheckTimeoutInSec": 5, "dialTimeoutInMs": 5000, "error_percent_threshold": 25, "max_concurrent_requests": 100, "poolMaxOpen": 10, "serverAddress": "http://transaction-signing.identity.svc.cluster.local", "verifyDeviceIDServerPath": "/v1/tx-sign/verify-device-id", "serviceDiscoveryEnabled": false, "timeout": 3000}, "hash": ":global.2fICZtQj0DijDvDvikTR8dpkoeg=:global.stg.4XxXV1CJV2szfpuXkN5TUavu05g=:template.101eFivi824Uop7mdnaCQMMbcgY="}, "gid_digibank_verification_provider_config": {"template": "serverAddress: '{{ string .serverAddress }}'\nauthLinkServerPath: '{{ string .authLinkServerPath }}'\nverifyProfileServerPath: '{{ string .verifyProfileServerPath }}'\nverifyLoginServerPath: '{{ string .verifyLoginServerPath }}'\nserviceDiscoveryEnabled: {{ bool .serviceDiscoveryEnabled }}\npoolMaxOpen: {{ int .poolMaxOpen }}\ndialTimeoutInMs: {{ int .dialTimeoutInMs }}\ndefaultHealthCheckFrequencyInSec: {{ int .defaultHealthCheckFrequencyInSec }}\ndefaultHealthCheckTimeoutInSec: {{ int .defaultHealthCheckTimeoutInSec }}\ntimeout: {{ int .timeout }}\nmax_concurrent_requests: {{ int .max_concurrent_requests }}\nerror_percent_threshold: {{ int .error_percent_threshold }}\n", "data": {"defaultHealthCheckFrequencyInSec": 5, "defaultHealthCheckTimeoutInSec": 5, "dialTimeoutInMs": 5000, "error_percent_threshold": 25, "max_concurrent_requests": 100, "poolMaxOpen": 10, "serverAddress": "http://verification-provider.backend-dakota-app-01.svc.cluster.local", "authLinkServerPath": "/authorise-link", "verifyProfileServerPath": "/v2/verify-profile", "verifyLoginServerPath": "/login", "serviceDiscoveryEnabled": false, "timeout": 6000}, "hash": ":global.2fICZtQj0DijDvDvikTR8dpkoeg=:global.stg.4XxXV1CJV2szfpuXkN5TUavu05g=:template.101eFivi824Uop7mdnaCQMMbcgY="}, "hedwig_oauth2_otp_pushchainedinbox_pct": {"template": "{{ .percentage }}\n", "data": {"percentage": 100}, "hash": ":global.Bz55Y15PmyD0Wg3tG-bNJZ_nEt8=:global.stg.XapTzpTGo-QmnCUIkgWF5HSOtGQ=:template.14sYM-3rWnHLYlhU1yYQrK4Zl0I="}, "hedwig_rollout_pct": {"template": "{{ .percent }}\n", "data": {"percent": 100}, "hash": ":global.irJsP0SNfOM_ENnF96HYTyxD_sM=:global.stg.f6wsPEZeFwsNiumEFEYiVl1y_PE=:template.TJ3mkoIw4s6wvnPBP46noJsCFQE="}, "hedwig_rollout_sms_pct": {"template": "{{ int .value }}", "data": {"value": 100}, "hash": ":global.bKI6f4LEFLtdNwyqI2E1z2IRcrA=:global.stg.syjBrIyB5cdMmItaCCJ50jJcX9k=:template.RZf0MUURZFAeaEIiT4lKEwPO7-w="}, "hedwig_sms_access_token": {"template": "{{ decryptWithVault .role .path .key }}", "data": {"key": "access_token", "path": "stg/service/grab-id/hedwig_sms", "role": "dev-grab-id"}, "hash": ":global.mpYegIgLkQL3qrWDsfoJYBgfO0k=:global.stg.eH4gQqVirQwTXq_0yM9z8T65nRA=:template.1RY6zFGQl7IVCy7I8_fOndpvEdg="}, "hedwig_sms_fallback_grabmessaging": {"template": "{{ bool .value }}", "data": {"value": false}, "hash": ":global.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:global.stg.O6LNNbEehXAfXlWEsMlaNW7XX_g=:template.agqciiThdyC8wUwL4ZJbPn3dsso="}, "hedwig_sms_template_id": {"template": "{{ string .value }}", "data": {"value": "2bb2b73d-8656-470f-8654-e426bb00537c"}, "hash": ":global.CanVTS11z7BUL2kG6nPj52KfHiE=:global.stg.expBFNWkv6faWaxmu1o4ZXtHp-g=:template.KYaLQxFAf7eAFTln96KWIABn9Dg="}, "invalidateWhenUnlink": {"template": "{{bool .value }}", "data": {"value": true}, "hash": ":global.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:global.stg.:template.JiWK9emWNKKDIOXrITVkumwxLt8="}, "low_quality_supported_errors": {"template": "{{ string .mode }}", "data": {"mode": "MULTIPLE_FACES:image_ref1,INVALID_IMAGE_SIZE:image_ref1,NO_FACE_FOUND:image_ref1,IMAGE_ERROR_UNSUPPORTED_FORMAT:image_ref1,statusRequestEntityTooLarge"}, "hash": ":global.0WpCEpTylgpZrs_WOVAk96fD-4A=:global.stg.na0lE3v9XcWFheuyk3zBrn6alWM=:template.69v9h3efh3vNaYvpNEZbXETcfCQ="}, "mail_and_sms_via_grab_messaging": {"template": "{{bool .value}}", "data": {"value": true}, "hash": ":global.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:global.stg.:template.1KkG44n-u7AJnzgLwkNYZ0wa3YY="}, "mark_profile_as_low_quality_enabled": {"template": "{{ bool .mode }}\n", "data": {"mode": false}, "hash": ":global.PjthdWZ_qd_nklav7GGLTLQVCJA=:global.stg.bropxfxD4ovyMGupxePq01wfiQw=:template.10XU8RMwsTQDdja8hSlFRp6h9hw="}, "oauth2:stepup:enable_condition_check": {"template": "{{ bool .enable }}\n", "data": {"enable": false}, "hash": ":global.NCtXh84dcSGz4zwsvLF1cRWma9Y=:global.stg.NCtXh84dcSGz4zwsvLF1cRWma9Y=:template.16YkFby1TdM0GuPkmbyuDuOpTxY="}, "oauth2:stepup:moca_orig_method:key": {"template": "{{ string .var }}", "data": {"var": "POST"}, "hash": ":global.UJWvAHjznmt0BDNvn27GdYGLlWo=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.TRk86KGp1gqxSfYSVA2hX98U3B0="}, "oauth2:stepup:moca_orig_path:key": {"template": "{{ string .var }}", "data": {"var": "/mocapay/v2/web/pax/charge/confirm"}, "hash": ":global.TdPpaonOIEMTxGCVhFWDTkcBWeE=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.TRk86KGp1gqxSfYSVA2hX98U3B0="}, "oauth2:stepup:moca_uri:key": {"template": "{{ string .var }}", "data": {"var": "https://weblogin.stg-paysi.moca.vn/stepup"}, "hash": ":global.pp_5q9HKGBcVlUcfY6oLtcGUAjQ=:global.stg.uPnEA95ZeAFxoixcvowlkIqq4tE=:template.TRk86KGp1gqxSfYSVA2hX98U3B0="}, "oauth2:stepup:partner_gateway:fallback": {"template": "{{ bool .var }}", "data": {"var": false}, "hash": ":global.__wqHsXn806frnDXOeJJNRh4bLk=:global.stg.aDcbakFZcdvjTB9FoFbVP-_zxz4=:template.NY5dmrhqLFwksQ3kTuuBLW_TKgE="}, "oauth2:stepup:policy:rollout": {"template": "{{ bool .var }}", "data": {"var": true}, "hash": ":global.aDcbakFZcdvjTB9FoFbVP-_zxz4=:global.stg.__wqHsXn806frnDXOeJJNRh4bLk=:template.NY5dmrhqLFwksQ3kTuuBLW_TKgE="}, "oauth2:stepup:riskproxy:rollout": {"template": "{{ bool .var }}", "data": {"var": true}, "hash": ":global.__wqHsXn806frnDXOeJJNRh4bLk=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.NY5dmrhqLFwksQ3kTuuBLW_TKgE="}, "oauth2:stepup:shadow_condition_check": {"template": "{{ bool .enable }}", "data": {"enable": true}, "hash": ":global.NCtXh84dcSGz4zwsvLF1cRWma9Y=:global.stg.-GAIbeoPwdheDfxt-QSGelCK9BY=:template.JiOcS0p7bNVseSIKgTM8R2Njth8="}, "oauth2:stepup:weblogin_uri:key": {"template": "{{ string .var }}", "data": {"var": "https://weblogin.stg-myteksi.com/stepup"}, "hash": ":global.M0jSb6iy05xRiU3myS_eFzaVUtY=:global.stg._ZWkFapcVdHvMkH9gLK-VGOSGN8=:template.TRk86KGp1gqxSfYSVA2hX98U3B0="}, "oauth_step_up_auth_policies": {"template": "{{ range $idx, $policy := .policies }}\n  '{{ $policy.name }}':\n    condition: {{$policy.condition}}\n    challengePolicy: {{$policy.challengePolicy}}\n{{end}}", "data": {"policies": [{"challengePolicy": "phoneOTP", "condition": "RiskProxy", "name": "MocaOTP"}, {"challengePolicy": "PinEmail", "condition": "RiskProxy", "name": "RiskProxyPinEmail"}, {"challengePolicy": "PinEmail", "condition": "PartnerUserMFASession", "name": "PartnerUserPinEmail"}, {"challengePolicy": "PinEmail", "condition": "AlwaysRequired", "name": "AlwaysPinEmail"}]}, "hash": ":global.vrY5Pbuibi2XUFRD8ucWIjN0olc=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.HiGByczajW9fPrmyMt-NahuIiqY="}, "partner_enabled": {"template": "{{bool .value}}", "data": {"value": true}, "hash": ":global.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:global.stg.:template.1KkG44n-u7AJnzgLwkNYZ0wa3YY="}, "paysi_get_merchant_info_rollout": {"template": "{{ .percentage }}\n", "data": {"percentage": 100}, "hash": ":global.r4EtpVJWuoVb_GE2Qymnld_kCWw=:global.stg.-LbwIkTMXS-D0RlD-iAXFbqoKmQ=:template.14sYM-3rWnHLYlhU1yYQrK4Zl0I="}, "phone_login_risk_check_rollout_pct": {"template": "{{ .percentage }}\n", "data": {"percentage": 100}, "hash": ":global.Tn1YaUwbWfdJsFIaLeDqLFoVHn8=:global.stg.-LbwIkTMXS-D0RlD-iAXFbqoKmQ=:template.14sYM-3rWnHLYlhU1yYQrK4Zl0I="}, "phone_login_risk_check_test_phone_numbers": {"template": "{{ string .testPhoneNumbers }}\n", "data": {"testPhoneNumbers": "12535677888,14253466917,60322223333,17652767315,12532374819,6581235689,13604744946,13604308688,15033954718_1598908638945_R,13132130793"}, "hash": ":global.8XT_v88AvZ2mJ_9v7DB4HrwZces=:global.stg.86szoU96CyQLNC5RCt1i9-sDV_E=:template.g5lhedxxiQBbQwhe8jmtyAyT0bU="}, "phone_number_recycled_email_subject": {"template": "{{ range $idx, $lang := .language }}\n  {{$lang.name}} : \"{{$lang.subject}}\"\n{{end}}\n\n", "data": {"language": [{"name": "en", "subject": "Grab: Changed Your Mobile Number?"}, {"name": "zh", "subject": "Grab: 换了您的手机号码？"}, {"name": "th", "subject": "Grab: เปลี่ยนแปลงหมายเลขโทรศัพท์มือถือของคุณหรือไม่"}, {"name": "id", "subject": "Grab: Ganti Nomor HP?"}, {"name": "ms", "subject": "Grab: Nombor Telefon Bimbit Sudah Bertukar?"}, {"name": "vi", "subject": "Grab: Thay đổi số điện thoại?"}, {"name": "my", "subject": "Grab: သင့်မိုဘိုင်းနံပါတ်ကို ပြောင်းခဲ့သလား။"}]}, "hash": ":global.Lr22re5dzssj2tnVw_zsfJ_lcTQ=:global.stg.:template.gnLzwkdWZJppZiG7j4_chWcxfw0="}, "pin_reset_email_subject": {"template": "{{ range $idx, $lang := .language }}\n  {{$lang.name}} : \"{{$lang.subject}}\"\n{{end}}", "data": {"language": [{"name": "en", "subject": "Please reset your PIN"}, {"name": "zh", "subject": "Please reset your PIN"}, {"name": "th", "subject": "Please reset your PIN"}, {"name": "id", "subject": "Please reset your PIN"}, {"name": "ms", "subject": "Please reset your PIN"}, {"name": "vi", "subject": "Please reset your PIN"}, {"name": "my", "subject": "Please reset your PIN"}]}, "hash": ":global.R_tJioc3U0UwrHS9ji9qYeeY6Vc=:global.stg.:template.jB5TQU5prVT7DGh9aSeFVQVULag="}, "profile_picture_resize_rollout": {"template": "{{ int .percentage }}\n", "data": {"percentage": 100}, "hash": ":global.Tn1YaUwbWfdJsFIaLeDqLFoVHn8=:global.stg.-LbwIkTMXS-D0RlD-iAXFbqoKmQ=:template.0Mzq0xIFlhzIe28hcBDeaiy1eYI="}, "profile_picture_resize_threshold": {"template": "{{ int .value }}\n", "data": {"value": 2097152}, "hash": ":global.jlVxMvNKuYLRfKvUOnjID0q54mM=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.ogjREJWeuP2798osN9j7OrlL9Xk="}, "ratelimit_tb_maxreq_persecond": {"template": "{{int .value}}", "data": {"value": 5000}, "hash": ":global.Id2t6QTA7Itgpj6d9Dc35M4Ion8=:global.stg.Tbh1E8U9SpQ_U3c8qICHwPUzg3s=:template.dBPGNuzAiX2SUdqyZn35eIqNGcw="}, "read_from_master_table_mfa_challenges_pct": {"template": "{{int .percentage }}\n", "data": {"percentage": 100}, "hash": ":global.Tn1YaUwbWfdJsFIaLeDqLFoVHn8=:global.stg.-LbwIkTMXS-D0RlD-iAXFbqoKmQ=:template.fcNRYPYhVy0f8wxe7Sdwry3KkDM="}, "read_from_master_table_phone_number_verifications_pct": {"template": "{{ int .percentage }}\n", "data": {"percentage": 100}, "hash": ":global.Tn1YaUwbWfdJsFIaLeDqLFoVHn8=:global.stg.-LbwIkTMXS-D0RlD-iAXFbqoKmQ=:template.0Mzq0xIFlhzIe28hcBDeaiy1eYI="}, "recycle_low_value_account_percentage": {"template": "{{ int .value}}", "data": {"value": 0}, "hash": ":global.bKI6f4LEFLtdNwyqI2E1z2IRcrA=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.uUjClgiahYQrWw6QuusxaIQ1mFM="}, "redis_cluster_config": {"template": "addr: {{ string .addr }}\npoolSize: {{ int .poolSize }}\nreadTimeoutInSec: {{ int .readTimeoutInSec }}\nwriteTimeoutInSec: {{ int .writeTimeoutInSec }}\nidleTimeoutInSec: {{ int .idleTimeoutInSec }}\nreadOnlyFromSlaves: {{ bool .readOnlyFromSlaves }}", "data": {"addr": "grab-id-redis.identity.stg.g-bank.app:6379", "idleTimeoutInSec": 2, "poolSize": 300, "readOnlyFromSlaves": false, "readTimeoutInSec": 2, "writeTimeoutInSec": 2}, "hash": ":global.IDHpeaFPnzHpEvUBXhqVxEa2uu8=:global.stg._vvKrJDKOIys9XqYTm6eBnAuOaM=:template.kraZnkdyYEjiijMxY33vwdaSD0o="}, "redis_cluster_secure_config": {"template": "tlsEnabled: {{ bool .tlsEnabled }}\npasswordEnabled: {{ bool .passwordEnabled }}\npassword: {{ string .password }}", "data": {"tlsEnabled": true, "passwordEnabled": true, "password": "EC_REDIS_PASSWORD"}, "hash": ":global.IDHpeaFPnzHpEvUBXhqVxEa2uu8=:global.stg._vvKrJDKOIys9XqYTm6eBnAuOaM=:template.kraZnkdyYEjiijMxY33vwdaSD0o="}, "redis_migration_using_redis_cluster": {"template": "{{ bool .enable }}", "data": {"enable": true}, "hash": ":global.NCtXh84dcSGz4zwsvLF1cRWma9Y=:global.stg.-GAIbeoPwdheDfxt-QSGelCK9BY=:template.JiOcS0p7bNVseSIKgTM8R2Njth8="}, "report_email_subject": {"template": "{{ range $idx, $lang := .language }}\n  {{$lang.name}} : \"{{$lang.subject}}\"\n{{end}}", "data": {"language": [{"name": "en", "subject": "Grab: You've Changed Your Email Address"}, {"name": "zh", "subject": "Grab: You've Changed Your Email Address"}, {"name": "th", "subject": "Grab: You've Changed Your Email Address"}, {"name": "id", "subject": "Grab: You've Changed Your Email Address"}, {"name": "ms", "subject": "Grab: You've Changed Your Email Address"}, {"name": "vi", "subject": "Grab: You've Changed Your Email Address"}, {"name": "my", "subject": "Grab: You've Changed Your Email Address"}]}, "hash": ":global.EsgU4wWFnEJlW2HCUlSE5a_PfEY=:global.stg.:template.jB5TQU5prVT7DGh9aSeFVQVULag="}, "returnMismatchOnBaselineQualityErrorsRollOut": {"template": "{{ string .value }}", "data": {"value": "US|100,MY|100,SG|0,ID|100"}, "hash": ":global.bGhNuczLdbgXLSYhhjAHR737UwU=:global.stg.4GgcaLFFjDr9T7_PPot2oLmqC20=:template.KYaLQxFAf7eAFTln96KWIABn9Dg="}, "risk_check_device_changed_roll_out": {"template": "{{ .percentage }}\n", "data": {"percentage": 100}, "hash": ":global.PXG_xshwVgCDJOJJSyFd98YgHOg=:global.stg.-LbwIkTMXS-D0RlD-iAXFbqoKmQ=:template.14sYM-3rWnHLYlhU1yYQrK4Zl0I="}, "risk_check_mock_mfa_desc": {"template": "{{ string .value }}", "data": {"value": "mfa required"}, "hash": ":global.mpHDcnyy2rqoDZJMdCddcAvmxDE=:global.stg.UhDoq6W7dtNPmhuwDGKc0vnX_jQ=:template.KYaLQxFAf7eAFTln96KWIABn9Dg="}, "risk_check_mock_mfa_mech": {"template": "{{ string .value }}", "data": {"value": "PIN_PLUS"}, "hash": ":global.CanVTS11z7BUL2kG6nPj52KfHiE=:global.stg.IaADaGBFSrVIv9F6eb8wpiDDqdk=:template.KYaLQxFAf7eAFTln96KWIABn9Dg="}, "risk_check_mock_mfa_required_service_user_ids": {"template": "{{ string .value }}", "data": {"value": "91632062,93585461,93858758,91615448,93342417,91627092,91598822,100750281,100933133,94036879"}, "hash": ":global.CanVTS11z7BUL2kG6nPj52KfHiE=:global.stg.KepmPh1eq_GGwAkCj7EukzUHWfA=:template.KYaLQxFAf7eAFTln96KWIABn9Dg="}, "risk_check_mock_mfa_status_code": {"template": "{{ int .value }}", "data": {"value": 200100}, "hash": ":global.EjJ9lAGMwirdyUf4eZxGGTi5mYs=:global.stg.Feku25OBf1bH-pGjbHBIVS0Y1Xc=:template.RZf0MUURZFAeaEIiT4lKEwPO7-w="}, "risk_check_new_social_login_roll_out": {"template": "{{ .percentage }}\n", "data": {"percentage": 100}, "hash": ":global.Tn1YaUwbWfdJsFIaLeDqLFoVHn8=:global.stg.-LbwIkTMXS-D0RlD-iAXFbqoKmQ=:template.14sYM-3rWnHLYlhU1yYQrK4Zl0I="}, "rollout_enforce_recycle_phone_web_login": {"template": "'{{ .jsonString}}'", "data": {"jsonString": "{\"US\":[\"08044981144746ec80a870c605fe705b\",\"21f57cc3249144bb92377f3a4d9a6d63\",\"acf7f99b1a76430b805905ab6e6f482a\",\"bce9323f76d2402cab00fa038f471cd8\"],\"ID\":[\"21f57cc3249144bb92377f3a4d9a6d63\",\"acf7f99b1a76430b805905ab6e6f482a\",\"bce9323f76d2402cab00fa038f471cd8\"],\"VN\":[\"21f57cc3249144bb92377f3a4d9a6d63\",\"acf7f99b1a76430b805905ab6e6f482a\",\"bce9323f76d2402cab00fa038f471cd8\"],\"TH\":[\"21f57cc3249144bb92377f3a4d9a6d63\",\"acf7f99b1a76430b805905ab6e6f482a\",\"bce9323f76d2402cab00fa038f471cd8\"],\"PH\":[\"21f57cc3249144bb92377f3a4d9a6d63\",\"acf7f99b1a76430b805905ab6e6f482a\",\"bce9323f76d2402cab00fa038f471cd8\"],\"SG\":[\"21f57cc3249144bb92377f3a4d9a6d63\",\"acf7f99b1a76430b805905ab6e6f482a\",\"bce9323f76d2402cab00fa038f471cd8\"],\"MY\":[\"acf7f99b1a76430b805905ab6e6f482a\",\"bce9323f76d2402cab00fa038f471cd8\"],\"MM\":[\"acf7f99b1a76430b805905ab6e6f482a\",\"bce9323f76d2402cab00fa038f471cd8\"]}"}, "hash": ":global.W61fYETDNBuh51_kB_BzLd1VLCM=:global.stg.frAIs-XPQwxDXO4bQVy0HBeR2MY=:template.vJBFJoKujl5OJc8aCGCAqcKhb70="}, "rollout_pct_check_banned_status_before_otp_sms": {"template": "{{ int .percentage }}\n", "data": {"percentage": 0}, "hash": ":global.r4EtpVJWuoVb_GE2Qymnld_kCWw=:global.stg.eUfIaaQpQYMxF2-ar83r18P_204=:template.0Mzq0xIFlhzIe28hcBDeaiy1eYI="}, "safetySelfieVerifyShadowBucket": {"template": "{{ string .value }}\n", "data": {"value": "stg-tis-safety"}, "hash": ":global.bdadrHA3cY8TYmtfR6JVDpnl3fU=:global.stg.IXMInDgJ_P8YP22m_5a5v0hUFro=:template.VECmorbvPLm14upR5CjXv0J9KHA="}, "safetySelfieVerifyShadowRollout": {"template": "{{ bool .enable }}\n", "data": {"enable": false}, "hash": ":global.LGJ_-XZVD1JkkJswDF7b4LstPYs=:global.stg.6oP8xrHfo_fyXhklzCFmDdkJMA8=:template.16YkFby1TdM0GuPkmbyuDuOpTxY="}, "selfiePayloadSizeLimit": {"template": "{{ int .limit }}\n", "data": {"limit": 2097152}, "hash": ":global.nH1Nx-Qg2ESY1uax9tXN-bKZ9pY=:global.stg.lpvDhOqzB9vYq_MHuKAZVUl_AB4=:template.qjFyQpUguII0SFmLjfZe7DcnlGo="}, "serviceFailureSimulatorConfig": {"template": "{\n  \"abacus\" : {\n    \"canSimulateError\": {{bool .abacusCanSimulateError}},\n    \"simulatedLatencyMs\": {{int .abacusSimulatedLatencyMs}}\n  },\n  \"booking\" : {\n    \"canSimulateError\": {{bool .bookingCanSimulateError}},\n    \"simulatedLatencyMs\": {{int .bookingSimulatedLatencyMs}}\n  },\n  \"borders\" : {\n    \"canSimulateError\": {{bool .bordersCanSimulateError}},\n    \"simulatedLatencyMs\": {{int .bordersSimulatedLatencyMs}}\n  },\n  \"cds\" : {\n    \"canSimulateError\": {{bool .cdsCanSimulateError}},\n    \"simulatedLatencyMs\": {{int .cdsSimulatedLatencyMs}}\n  },\n  \"rewards\" : {\n    \"canSimulateError\": {{bool .rewardsCanSimulateError}},\n    \"simulatedLatencyMs\": {{int .rewardsSimulatedLatencyMs}}\n  },\n  \"paysi\" : {\n    \"canSimulateError\": {{bool .paysiCanSimulateError}},\n    \"simulatedLatencyMs\": {{int .paysiSimulatedLatencyMs}}\n  },\n  \"ted\" : {\n    \"canSimulateError\": {{bool .tedCanSimulateError}},\n    \"simulatedLatencyMs\": {{int .tedSimulatedLatencyMs}}\n  },\n  \"facebook\" : {\n    \"canSimulateError\": {{bool .facebookCanSimulateError}},\n    \"simulatedLatencyMs\": {{int .facebookSimulatedLatencyMs}}\n  },\n  \"google\" : {\n    \"canSimulateError\": {{bool .googleCanSimulateError}},\n    \"simulatedLatencyMs\": {{int .googleSimulatedLatencyMs}}\n  },\n  \"grabMessaging\" : {\n    \"canSimulateError\": {{bool .grabMessagingCanSimulateError}},\n    \"simulatedLatencyMs\": {{int .grabMessagingSimulatedLatencyMs}}\n  },\n  \"twilio\" : {\n    \"canSimulateError\": {{bool .twilioCanSimulateError}},\n    \"simulatedLatencyMs\": {{int .twilioSimulatedLatencyMs}}\n  },\n  \"hedwig\" : {\n    \"canSimulateError\": {{bool .hedwigCanSimulateError}},\n    \"simulatedLatencyMs\": {{int .hedwigSimulatedLatencyMs}}\n  }\n}", "data": {"abacusCanSimulateError": false, "abacusSimulatedLatencyMs": 0, "bookingCanSimulateError": false, "bookingSimulatedLatencyMs": 0, "bordersCanSimulateError": false, "bordersSimulatedLatencyMs": 0, "cdsCanSimulateError": false, "cdsSimulatedLatencyMs": 0, "facebookCanSimulateError": false, "facebookSimulatedLatencyMs": 0, "googleCanSimulateError": false, "googleSimulatedLatencyMs": 0, "grabMessagingCanSimulateError": false, "grabMessagingSimulatedLatencyMs": 0, "hedwigCanSimulateError": false, "hedwigSimulatedLatencyMs": 0, "paysiCanSimulateError": false, "paysiSimulatedLatencyMs": 0, "rewardsCanSimulateError": false, "rewardsSimulatedLatencyMs": 0, "tedCanSimulateError": false, "tedSimulatedLatencyMs": 0, "twilioCanSimulateError": false, "twilioSimulatedLatencyMs": 0}, "hash": ":global.fabmJduoAv_LE6J-XNHLpRzS2Kg=:global.stg.fabmJduoAv_LE6J-XNHLpRzS2Kg=:template.V2qTwRsWugqpsntrLo23l9HqalM="}, "settings:bali:concedo:key": {"template": "{{ decryptWith .kms .concedo_key }}", "data": {"concedo_key": "dev-grab-id|NP+BAwEBB3BheWxvYWQB/4IAAQMBA0tleQEKAAEFTm9uY2UB/4QAAQdNZXNzYWdlAQoAAAAZ/4MBAQEJWzI0XXVpbnQ4Af+EAAEGATAAAP/w/4IB/6cBAQEAeOOi32NpypHP5BwICOG8LirPXyTpkF3LOo4ciKmbuZyEAAAAfjB8BgkqhkiG9w0BBwagbzBtAgEAMGgGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMPIu9d7IZ7Tfd8QSXAgEQgDtH0Qki1hDvkj+fSwJwHqTj9L3o6cjtMA6xEAjSZjGgxhHHPuAuVYD+4uEBjCrEPhOCbS2WowOeDwO1wwEYNjp3Sf+SZf/zTDz/nC7/hV9bUf/0/6Ii/845aP/WTCwBHyhSAjNZDKxO1rRBIdxDLtLbPgDFFC+y2bdtzQk7vDMA", "kms": "dev-grab-id"}, "hash": ":global.SH1XoId_hDXMbneUmU_vadSU2Xs=:global.stg.rzfSwsCtkJjITDwhLdQPydBSe38=:template.ZEkufaneUWoYh-Rep25uKTs5tdg="}, "settings:bali:concedo:privateKey": {"template": "{{ decryptWith .kms .privateKey }}", "data": {"kms": "dev-grab-id", "privateKey": "dev-grab-id|NP+BAwEBB3BheWxvYWQB/4IAAQMBA0tleQEKAAEFTm9uY2UB/4QAAQdNZXNzYWdlAQoAAAAZ/4MBAQEJWzI0XXVpbnQ4Af+EAAEGATAAAP4Bzv+CAf+nAQEBAHjjot9jacqRz+QcCAjhvC4qz18k6ZBdyzqOHIipm7mchAAAAH4wfAYJKoZIhvcNAQcGoG8wbQIBADBoBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDEqz/YLiVJmYKCitcwIBEIA7D7hEg2fqTtgsQ3nu6MuWiYpBAo0g5TdtG9YjP8hdiEZU5m0y/RAsc2gEBqSayFTIjUxJVnuBIZ+OV1sBGP+//9ceZQr/7QT/xf/7/6v/z//U/8f/tjVCSyw2eP+CcG7/8AH/+OMN8nho96u7InJdByUYdLusX74XYmTdu8hHT1KGflSFT3VwhxoP6N+LpJNK66AcdZV67rDLPSuil1Immkz0bNL4tWfjkce93EgeI7flMhXYMXHwCb9TMKCrPvEWe+Z7+qpHxG8P/bGH969I8RAshuWwfblBSX6XMZiJ57q3j9nKTgX0cPAwd6QnNgQFsukzCqBhtj/KuUgfaCUcCthAlDrODrOnois27lMe7t1SYLrQlpWQ41ojQ28UTM3TG6dqDcv5tebkVBEiraLu8KnnGrS0d43lzAnNWKNeNvh+XrbvoX/3PsnlBpW4FJqecgY8nWPPzEA5GF9jAA=="}, "hash": ":global.V4aSDOq86OBs0ySJi4D0Balpp98=:global.stg.WoJrft4bqJ8UYSJc5p2yn294BNE=:template.DT_CANeS3-YwvSAMzYQDF1qA72w="}, "settings:bali:concedo:secret": {"template": "{{ decryptWith .kms .concedo_secret }}", "data": {"concedo_secret": "dev-grab-id|NP+BAwEBB3BheWxvYWQB/4IAAQMBA0tleQEKAAEFTm9uY2UB/4QAAQdNZXNzYWdlAQoAAAAZ/4MBAQEJWzI0XXVpbnQ4Af+EAAEGATAAAP/0/4IB/6cBAQEAeOOi32NpypHP5BwICOG8LirPXyTpkF3LOo4ciKmbuZyEAAAAfjB8BgkqhkiG9w0BBwagbzBtAgEAMGgGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQM6v0EacBfGgpKE6q8AgEQgDsdv+gySXBromqF6BW+PWtBqUm8xcMOhVV85YHVdwkjhZDyjfRII8C8iLGOoBrGATCxnFu+tSmVfhYBiAEY//7/rv/OEHp2/5f/0P+pAl//iGz/xkVPNGFG/7gO/8QZ/+4BIFqjqxTgBcGVBXGQIBSDnAGEl6FE0nC4MZEfL6qQKE+/AA==", "kms": "dev-grab-id"}, "hash": ":global.yzO5pgnrCg2LC3EguRGeyuns8pw=:global.stg.A9nnj9vt-uDo9_8Acgm3jP5oP8M=:template.lwDKzAimHrLkoWagsY2hMpW4FcM="}, "settings:bali:concedo:version": {"template": "{{ string .version}}", "data": {"version": "v3"}, "hash": ":global.Pt8J6yBPBA1eS7bHomJSzblLP2Y=:global.stg.LoszESsJFqt1B4JTzp3hSzs09Ho=:template.gho4-412Uj07lXQrwR7xrErOStg="}, "sitevar2_only": {"template": "{{bool .value}}", "data": {"value": false}, "hash": ":global.O6LNNbEehXAfXlWEsMlaNW7XX_g=:global.stg.:template.1KkG44n-u7AJnzgLwkNYZ0wa3YY="}, "stepUpAuthChallengeConfigTemplates": {"template": "{\r  \"shadowMode\": false,\r  \"emailMask\" : \"*\",\r  \"userStateTtlMinutes\": {{ int .userStateTtlMinutes }},\r  \"userChallengeTtlMinutes\": {{ int .userChallengeTtlMinutes }},\r  \"changingChallengeAvailableAfterMinutes\": {{ int .changingChallengeAvailableAfterMinutes }},\r  \"fixedChallengeAvailableAfterMinutes\": {{ int .fixedChallengeAvailableAfterMinutes }},\r  \"retryIncorrectlyRespondedChallenges\": {{ bool .retryIncorrectlyRespondedChallenges }},\r  \"accountNameSeparators\": \"[ ,.]\",\r  \"challengeTemplates\": [{\r    \"templateID\": \"pin6digit\",\r    \"templateType\": \"pin6digit\",\r    \"description\": \"6-digit PIN challenge\",\r    \"classification\": \"fixed\",\r    \"enabled\": true,\r    \"maxAttempts\": 5,\r    \"applicableServices\": [\"PASSENGER\"],\r    \"applicableCountries\": [\"*\"]\r  },\r    {\r      \"templateID\": \"email\",\r      \"templateType\": \"text\",\r      \"description\": \"GrabID verified email challenge\",\r      \"classification\": \"fixed\",\r      \"enabled\": true,\r      \"applicableServices\": [\"PASSENGER\"],\r      \"applicableCountries\": [\"*\"]\r    },\r    {\r      \"templateID\": \"accountName\",\r      \"templateType\": \"text\",\r      \"description\": \"GrabID/Passenger account name challenge\",\r      \"classification\": \"fixed\",\r      \"enabled\": true,\r      \"applicableServices\": [\"PASSENGER\"],\r      \"applicableCountries\": [\"*\"]\r    },\r    {\r      \"templateID\": \"paxRides\",\r      \"templateType\": \"multiChoice\",\r      \"description\": \"# of PAX rides challenge\",\r      \"classification\": \"changing\",\r      \"enabled\": true,\r      \"maxChoices\": 4,\r      \"applicableServices\": [\"PASSENGER\"],\r      \"applicableCountries\": [\"*\"]\r    },\r    {\r      \"templateID\": \"gpcBalance\",\r      \"templateType\": \"multiChoice\",\r      \"description\": \"Grab Pay balance challenge\",\r      \"classification\": \"changing\",\r      \"enabled\": true,\r      \"maxChoices\": 4,\r      \"applicableServices\": [\"PASSENGER\"],\r      \"applicableCountries\": [\"*\"]\r    },\r    {\r      \"templateID\": \"gpcTopUp\",\r      \"templateType\": \"multiChoice\",\r      \"description\": \"Grab Pay top-up challenge\",\r      \"classification\": \"changing\",\r      \"enabled\": true,\r      \"maxChoices\": 4,\r      \"applicableServices\": [\"PASSENGER\"],\r      \"applicableCountries\": [\"*\"]\r    },\r    {\r      \"templateID\": \"rewardsTier\",\r      \"templateType\": \"multiChoice\",\r      \"description\": \"Rewards Tier challenge\",\r      \"classification\": \"fixed\",\r      \"enabled\": true,\r      \"maxChoices\": 4,\r      \"applicableServices\": [\"PASSENGER\"],\r      \"applicableCountries\": [\"*\"]\r    },\r    {\r      \"templateID\": \"rewardsBalance\",\r      \"templateType\": \"multiChoice\",\r      \"description\": \"Rewards Balance challenge\",\r      \"classification\": \"changing\",\r      \"enabled\": true,\r      \"maxChoices\": 4,\r      \"applicableServices\": [\"PASSENGER\"],\r      \"applicableCountries\": [\"*\"]\r    },\r    {\r      \"templateID\": \"phoneOTP\",\r      \"templateType\": \"phoneOTP\",\r      \"description\": \"Phone OTP challenge\",\r      \"classification\": \"retryable\",\r      \"enabled\": true,\r      \"maxAttempts\": 3,\r      \"applicableServices\": [\"PASSENGER\"],\r      \"applicableCountries\": [\"*\"]\r    }]\r}", "data": {"changingChallengeAvailableAfterMinutes": 10, "fixedChallengeAvailableAfterMinutes": 5, "retryIncorrectlyRespondedChallenges": true, "userChallengeTtlMinutes": 15, "userStateTtlMinutes": 10}, "hash": ":global.g4iPhCdI9UPuhogQi4FMGqJov0Q=:global.stg.evrp-aOaqB6wowno5NSNNBh77Oc=:template.yWpDwTeuX5RcbXabjmL8mM5Ok3U="}, "stepUpAuthChallengePolicies": {"template": "{\n   \"policies\":[\n      {\n         \"name\":\"defaultPolicyAll\",\n         \"passingGrade\":70,\n         \"overrides\":[\n            {\n               \"templateID\":\"pin6digit\",\n               \"required\":true,\n               \"weight\":100\n            },\n            {\n               \"templateID\":\"email\",\n               \"weight\":50\n            },\n            {\n               \"templateID\":\"accountName\",\n               \"weight\":50\n            },\n            {\n               \"templateID\":\"paxRides\",\n               \"weight\":20\n            },\n            {\n               \"templateID\":\"gpcBalance\",\n               \"weight\":20\n            },\n            {\n               \"templateID\":\"gpcTopUp\",\n               \"weight\":20\n            },\n            {\n               \"templateID\":\"rewardsBalance\",\n               \"weight\":20\n            },\n            {\n               \"templateID\":\"rewardsTier\",\n               \"weight\":20\n            }\n         ]\n      },\n      {\n         \"name\":\"PinWithEmailNameFallback\",\n         \"passingGrade\":100,\n         \"overrides\":[\n            {\n               \"templateID\":\"pin6digit\",\n               \"required\":true,\n               \"weight\":100\n            },\n            {\n               \"templateID\":\"email\",\n               \"maxAttempts\": 3,\n               \"required\":true,\n               \"weight\":100\n            },\n            {\n               \"templateID\":\"accountName\",\n               \"maxAttempts\": 3,\n               \"weight\":100\n            }\n         ]\n      },\n      {\n         \"name\":\"PinEmail\",\n         \"passingGrade\":100,\n         \"overrides\":[\n            {\n               \"templateID\":\"pin6digit\",\n               \"required\":true,\n               \"weight\":100\n            },\n            {\n               \"templateID\":\"email\",\n               \"maxAttempts\": 3,\n               \"weight\":100\n            }\n         ]\n      },\n      {\n         \"name\":\"daxWallet\",\n         \"passingGrade\":100,\n         \"overrides\":[\n            {\n               \"templateID\":\"pin6digit\",\n               \"required\":true,\n               \"weight\":100\n            }\n         ]\n      },\n      {\n         \"name\":\"defaultPolicy\",\n         \"passingGrade\":100,\n         \"overrides\":[\n            {\n               \"templateID\":\"email\",\n               \"required\":true,\n               \"maxAttempts\": 3,\n               \"weight\":100\n            }\n         ]\n      },\n      {\n         \"name\":\"phoneOTP\",\n         \"passingGrade\":100,\n         \"overrides\":[\n            {\n               \"templateID\":\"phoneOTP\",\n               \"required\":true,\n               \"maxAttempts\": 3,\n               \"weight\":100\n            }\n         ]\n      }\n   ]\n}", "data": {}, "hash": ":global.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:global.stg.:template.zx3BlcoxAw6yYNGu2FQ49lgOFgc="}, "stepUpAuthLocalizedPrompts": {"template": "{\n   \"pin6digit\":{\n      \"en\":{\n         \"prompt\":\"What's your Grab PIN?\",\n         \"description\":\"Enter the PIN that you've set up in the Grab app.\"\n      },\n      \"id\":{\n         \"prompt\":\"Apa PIN Grab-mu?\",\n         \"description\":\"Masukkan PIN yang kamu tetapkan di aplikasi Grab.\"\n      },\n      \"km\":{\n         \"prompt\":\"តើអ្វីជាលេខកូដសម្ងាត់អ្នកជាអ្វី?\",\n         \"description\":\"បញ្ចូលលេខកូដសម្ងាត់ដែលអ្នកបានដំឡើងនៅក្នុងកម្មវិធី Grab ។\"\n      },\n      \"ms\":{\n         \"prompt\":\"Apakah Grab PIN anda?\",\n         \"description\":\"Masukkan PIN yang telah anda tetapkan dalam aplikasi Grab.\"\n      },\n      \"my\":{\n         \"prompt\":\"သင့် Grab PIN မှာ အဘယ်နည်း။\",\n         \"description\":\"Grab အက်ပ်တွင် ပြုလုပ်ထားသော PIN ကို ထည့်သွင်းပါ။\"\n      },\n      \"th\":{\n         \"prompt\":\"Grab PIN ของคุณคืออะไร?\",\n         \"description\":\"โปรดกรอกหมายเลข PIN ที่คุณตั้งค่าในแอปฯ Grab\"\n      },\n      \"vi\":{\n         \"prompt\":\"Mã Grab PIN của bạn là gì?\",\n         \"description\":\"Nhập mã PIN mà bạn đã thiết lập trên <PERSON>.\"\n      },\n      \"zh\":{\n         \"prompt\":\"您的 Grab 密码是多少？\",\n         \"description\":\"请输入您在 Grab 应用程序中设置的密码。\"\n      }\n   },\n   \"email\":{\n      \"en\":{\n         \"prompt\":\"What's your email address?\",\n         \"description\":\"Enter the email address you registered with Grab.\",\n         \"hint\":\"You registered with {emailHint}\"\n      },\n      \"id\":{\n         \"prompt\":\"Apa alamat email-mu?\",\n         \"description\":\"Masukkan alamat email yang kamu daftarkan di Grab.\",\n         \"hint\":\"Kamu terdaftar dengan {emailHint}\"\n      },\n      \"ms\":{\n         \"prompt\":\"Apakah alamat e-mel anda?\",\n         \"description\":\"Masukkan alamat e-mnel yang anda daftar dengan Grab.\",\n         \"hint\":\"Anda telah berdaftar dengan {emailHint}\"\n      },\n      \"zh\":{\n         \"prompt\":\"您的电邮地址是多少？\",\n         \"description\":\"请输入您在 Grab 注册的电邮地址。\",\n         \"hint\":\"您使用 {emailHint} 注册过\"\n      },\n      \"th\":{\n         \"prompt\":\"อีเมลของคุณคืออะไร?\",\n         \"description\":\"โปรดกรอกที่อยู่อีเมลที่คุณใช้ลงทะเบียนกับ Grab\",\n         \"hint\":\"คุณลงทะเบียนด้วยอีเมล {emailHint}\"\n      },\n      \"vi\":{\n         \"prompt\":\"Địa chỉ email của bạn là?\",\n         \"description\":\"Nhập địa chỉ email mà bạn đã đăng ký với Grab.\",\n         \"hint\":\"Bạn đã sử dụng {emailHint} để đăng ký\"\n      },\n      \"my\":{\n         \"prompt\":\"သင့်အီးမေးလ်လိပ်စာမှာ အဘယ်နည်း။\",\n         \"description\":\"Grab တွင်စာရင်းသွင်းထားသည့်အီးမေးလ်လိပ်စာကို ထည့်သွင်းပါ။\",\n         \"hint\":\"သင်မှတ်ပုံတင်ထားသည့်အီးမေးလ်မှာ {emailHint}\"\n      },\n      \"km\":{\n         \"prompt\":\"តើអាសយដ្ឋានអ៊ីមែលរបស់អ្នកជាអ្វី?\",\n         \"description\":\"បញ្ចូលអាសយដ្ឋានអ៊ីមែលដែលអ្នកបានចុះឈ្មោះជាមួយ Grab ។\",\n         \"hint\":\"អ្នកបានចុះឈ្មោះជាមួយ {emailHint}\"\n      }\n   },\n   \"accountName\":{\n      \"en\":{\n         \"prompt\":\"What's your name in your Grab account?\",\n         \"description\":\"Enter the name you used in your Grab app.\"\n      },\n      \"id\":{\n         \"prompt\":\"Apa namamu di akun Grab?\",\n         \"description\":\"Masukkan nama yang kamu pakai di aplikasi Grab.\"\n      },\n      \"ms\":{\n         \"prompt\":\"Apakah nama anda di dalam aplikasi Grab?\",\n         \"description\":\"Masukkan nama yang anda gunakan di dalam aplikasi Grab.\"\n      },\n      \"zh\":{\n         \"prompt\":\"您在 Grab 账户中的名字是什么？\",\n         \"description\":\"请输入您在 Grab 应用程序中使用的名字。\"\n      },\n      \"th\":{\n         \"prompt\":\"ชื่อบัญชี Grab ของคุณคืออะไร?\",\n         \"description\":\"โปรดกรอกชื่อที่คุณใช้ในแอปฯ Grab \"\n      },\n      \"vi\":{\n         \"prompt\":\"Tên của bạn trong tài khoản Grab là gì?\",\n         \"description\":\"Nhập tên mà bạn sử dụng trong Grab.\"\n      },\n      \"my\":{\n         \"prompt\":\"Grab အကောင့်ရှိ သင့်အမည်မှာ အဘယ်သို့နည်း။\",\n         \"description\":\"Grab တွင် အသုံးပြုထားသော အမည်ကို ထည့်သွင်းပါ။\"\n      },\n      \"km\":{\n         \"prompt\":\"តើអ្នកមានឈ្មោះអ្វីនៅក្នុងគណនី Grab របស់អ្នក?\",\n         \"description\":\"បញ្ចូលឈ្មោះដែលអ្នកបានប្រើប្រាស់នៅក្នុងកម្មវិធី Grab របស់អ្នក។\"\n      }\n   },\n   \"paxRides\":{\n      \"en\":{\n         \"prompt\":\"Select the number of rides you made with Grab in the last 14 days:\"\n      }\n   },\n   \"gpcBalance\":{\n      \"en\":{\n         \"prompt\":\"What is the range of remaining GrabPay balance in your account?\"\n      },\n      \"id\":{\n         \"prompt\":\"Berapa sisa saldo OVO di akunmu?\"\n      },\n      \"ms\":{\n         \"prompt\":\"Berapakah jumlah baki GrabPay anda di dalam akaun?\"\n      },\n      \"zh\":{\n         \"prompt\":\"您账户中的 GrabPay 余额范围是多少？\"\n      },\n      \"th\":{\n         \"prompt\":\"ตอนนี้คุณมียอดเงิน GrabPay เท่าไหร่?\"\n      },\n      \"vi\":{\n         \"prompt\":\"Số dư trong Ví điện tử Moca của bạn còn khoảng bao nhiêu?\"\n      },\n      \"my\":{\n         \"prompt\":\"သင့်အကောင့်ရှိ GrabPay စာရင်းရှိငွေ လက်ကျန်မှာမည်မျှခန့်ရှိသနည်း။\"\n      },\n      \"km\":{\n         \"prompt\":\"តើសមតុល្យគណនី GrabPay នៅសល់ក្នុងគណនីរបស់អ្នកមានប៉ុន្មាន? \"\n      }\n   },\n   \"gpcTopUp\":{\n      \"en\":{\n         \"prompt\":\"Select the top-up amount you made with Grab in the last 14 days:\"\n      }\n   },\n   \"rewardsTier\":{\n      \"en\":{\n         \"prompt\":\"What is your current GrabRewards tier?\",\n         \"choices\":[\n            \"Member\",\n            \"Silver\",\n            \"Gold\",\n            \"Platinum\"\n         ]\n      },\n      \"id\":{\n         \"prompt\":\"GrabRewards-mu tingkat berapa sekarang?\",\n         \"choices\":[\n            \"Anggota\",\n            \"Silver\",\n            \"Gold\",\n            \"Platinum\"\n         ]\n      },\n      \"ms\":{\n         \"prompt\":\"Apakah peringkat GrabRewards anda sekarang?\",\n         \"choices\":[\n            \"Ahli\",\n            \"Perak\",\n            \"Emas\",\n            \"Platinum\"\n         ]\n      },\n      \"zh\":{\n         \"prompt\":\"您现在的 GrabRewards 级别是什么？\",\n         \"choices\":[\n            \"会员\",\n            \"白银\",\n            \"黄金\",\n            \"铂金\"\n         ]\n      },\n      \"th\":{\n         \"prompt\":\"ระดับสมาชิก GrabRewards ของคุณคือระดับไหน?\",\n         \"choices\":[\n            \"สมาชิก\",\n            \"ซิลเวอร์\",\n            \"โกลด์\",\n            \"แพลตินัม\"\n         ]\n      },\n      \"vi\":{\n         \"prompt\":\"Hạng thành viên GrabRewards của bạn là gì?\",\n         \"choices\":[\n            \"Thành viên\",\n            \"Bạc\",\n            \"Vàng\",\n            \"Bạch Kim\"\n         ]\n      },\n      \"my\":{\n         \"prompt\":\"သင့် လက်ရှိ GrabRewards အဆင့်မှာ အဘယ်နည်း။\",\n         \"choices\":[\n            \"အဖွဲ့ဝင်\",\n            \"Silver\",\n            \"Gold\",\n            \"Platinum\"\n         ]\n      },\n      \"km\":{\n         \"prompt\":\"តើ GrabRewards បច្ចុប្បន្នរបស់អ្នកនៅក្នុងចំណាត់ថ្នាក់ណា?\",\n         \"choices\":[\n            \"សមាជិក\",\n            \"ប្រាក់\",\n            \"មាស\",\n            \"ផ្លាទីន\"\n         ]\n      }\n   },\n   \"rewardsBalance\":{\n      \"en\":{\n         \"prompt\":\"How much GrabRewards points do you have left in your account?\"\n      },\n      \"id\":{\n         \"prompt\":\"Berapa sisa poin GrabRewards di akunmu?\"\n      },\n      \"ms\":{\n         \"prompt\":\"Berapakah baki mata ganjaran GrabRewards di dalam akaun anda sekarang?\"\n      },\n      \"zh\":{\n         \"prompt\":\"您账户中剩余的 GrabRewards 积分为多少？\"\n      },\n      \"th\":{\n         \"prompt\":\"ตอนนี้คุณมีคะแนน GrabRewards เท่าไหร่?\"\n      },\n      \"vi\":{\n         \"prompt\":\"Bạn còn bao nhiêu điểm GrabRewards trong tài khoản?\"\n      },\n      \"my\":{\n         \"prompt\":\"သင့်အကောင့်တွင် ကျန်ရှိနေသော GrabRewards ပွိုင့်မည်မျှရှိသနည်း။\"\n      },\n      \"km\":{\n         \"prompt\":\"តើពិន្ទុ GrabReward ដែលអ្នកបានទុកនៅក្នុងគណនីរបស់អ្នកមានចំនួនប៉ុន្មាន?\"\n      }\n   },\n   \"phoneOTP\":{\n      \"en\":{\n         \"prompt\":\"Enter the 6-digit code sent to your Moca-registered number.\"\n      },\n      \"vi\":{\n         \"prompt\":\"Nhập mã 6 số được gửi về số điện thoại đăng ký với Moca.\"\n      }\n   }\n}", "data": {}, "hash": ":global.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:global.stg.:template.RRyWxh7Z9YN4b24jNpuxeWfPHJM="}, "use-token-partitions": {"template": "{{bool .value}}", "data": {"value": true}, "hash": ":global.O6LNNbEehXAfXlWEsMlaNW7XX_g=:global.stg.lpkIbzhpoaoSXT7dGM4wCU1zwuw=:template.1KkG44n-u7AJnzgLwkNYZ0wa3YY="}, "verifyProfileRolloutByCountry": {"template": " {{ string .list }}", "data": {"list": "US,MY,SG,ID"}, "hash": ":global.tSfeveSlQoIMPcf1z1VwFCygef4=:global.stg.tAsj0UcmkGs-x0cB1RBe703oj14=:template.UIovkcp67PbLxKG-2HEr3GuIkv0="}, "verify_email_subject": {"template": "{{ range $idx, $lang := .language }}\n  {{$lang.name}} : \"{{$lang.subject}}\"\n{{end}}", "data": {"language": [{"name": "en", "subject": "Grab: Verify Your Grab Account"}, {"name": "zh", "subject": "Grab: 验证您的 Grab 账户"}, {"name": "th", "subject": "Grab: ยืนยันบัญชี Grab ของคุณ"}, {"name": "id", "subject": "Grab: <PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "ms", "subject": "Grab: <PERSON><PERSON><PERSON>"}, {"name": "vi", "subject": "Grab: <PERSON><PERSON><PERSON>h tài k<PERSON>n Grab của bạn"}, {"name": "my", "subject": "Grab: သင့်ရဲ့ Grab အကောင့်ကို အတည်ပြုပါ"}]}, "hash": ":global.1d6qVYpc2EM0MV49MSS-j4qWOVM=:global.stg.:template.jB5TQU5prVT7DGh9aSeFVQVULag="}, "config_s3_config_grabid_secret_key": {"template": "{{ string .value }}", "data": {"value": "MXpmOFyiRgc6P5Nn5avCO0nKcdixScAv74gYIttI"}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "config_face_auth_config_api_secret": {"template": "{{ string .value }}", "data": {"value": "xXTfzNwTSyGFkSfAW6Q5dOhzjw_ly5rR"}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "config_kmsMasterKey": {"template": "{{ string .value }}", "data": {"value": "arn:aws:kms:ap-southeast-1:109025824511:key/5f9bb86f-2c46-4c0a-aeaf-28e0b42c1698"}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "config_passwordRecoverySecretKey": {"template": "{{ string .value }}", "data": {"value": "aPkQzWLtVxcLIGqInyvL6Uj86QQNE7N4Cdd1X7GWto3Qdrw12lWainSWHWc5v6XZ"}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "fake-twilio-access-token": {"template": "{{ string .value }}", "data": {"value": "fake-twilio-access-token"}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_testing_sms_otp": {"template": "{{ string .value }}", "data": {"value": "'030521'"}, "hash": ":global.2fICZtQj0DijDvDvikTR8dpkoeg=:global.stg.4XxXV1CJV2szfpuXkN5TUavu05g=:template.101eFivi824Uop7mdnaCQMMbcgY="}, "gid_allow_pin_update_via_reset_link": {"template": "{{ bool .value}}", "data": {"value": true}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_login_idtoken_expiry_in_minutes": {"template": "{{ int .value }}", "data": {"value": 52560000}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "refresh_token_rotation_enabled": {"template": "{{ bool .value }}", "data": {"value": true}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "biometric_enrollment_skip_mfa_check": {"template": "{{ bool .value }}", "data": {"value": false}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_return_otp_expired_error": {"template": "{{ bool .value }}", "data": {"value": true}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_universal_link_email_otp_url": {"template": "{{ string .value }}", "data": {"value": "https://gxsdebug.page.link/authentication/email-verification?proof=<proof>"}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_universal_link_reset_pin_url": {"template": "{{ string .value }}", "data": {"value": "https://gxsdebug.page.link/authentication/pin-reset?safeid=<safeid>&token=<token>"}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_soft_lock_max_pin_attempts": {"template": "{{ int .value }}", "data": {"value": 3}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_device_authentication_enabled": {"template": "{{ bool .value }}", "data": {"value": true}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_device_lock_on_otp_enabled": {"template": "{{ bool .value }}", "data": {"value": true}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_device_soft_lock_enabled": {"template": "{{ bool .value }}", "data": {"value": true}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_pin_soft_locked_period_in_minutes": {"template": "{{ int .value }}", "data": {"value": 2}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_nonce_expiry_in_minute": {"template": "{{ int .value }}", "data": {"value": 30}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "pin_reset_token_duration_in_minutes": {"template": "{{ int .value }}", "data": {"value": 30}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_max_failed_otp_attempts": {"data": {"value": 5}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24=", "template": "{{ int .value }}"}, "gid_max_failed_pin_attempts": {"template": "{{ int .value }}", "data": {"value": 10000}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_tx_sign_enabled": {"template": "{{ bool .value }}", "data": {"value": true}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_verify_device_auth_per_request_enabled": {"template": "{{ bool .value }}", "data": {"value": true}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_securepin_base64_ecdh_priv_key": {"template": "{{ string .value }}", "data": {"value": "SECUREPIN_PRIV_KEY_VALUE"}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_pubkey_base64_ecdh_priv_key": {"template": "{{ string .value }}", "data": {"value": "PUBKEY_PRIV_KEY_VALUE"}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_pin_hasher_pbkdf2_config": {"template": "iterations: '{{ string .iterations }}'\npepperValue: '{{ string .pepperValue }}'\npepperKeyId: {{ string .pepperKeyId }}\n", "data": {"iterations": "500000", "pepperValue": "PIN_HASHER_PEPPER_VALUE", "pepperKeyId": "stg_v1"}, "hash": ":global.2fICZtQj0DijDvDvikTR8dpkoeg=:global.stg.4XxXV1CJV2szfpuXkN5TUavu05g=:template.101eFivi824Uop7mdnaCQMMbcgY="}, "gid_pin_hasher_type": {"template": "{{ string .value }}", "data": {"value": "PBKDF2"}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_securepin_in_transit_enabled": {"template": "{{ bool .value }}", "data": {"value": true}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_pub_key_encryption_enabled": {"template": "{{ bool .value }}", "data": {"value": true}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_app_signature_validation_enabled": {"template": "{{ bool .value }}", "data": {"value": false}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_app_signature_public_key": {"template": "{{ string .value }}", "data": {"value": "0433b0a03cd517a0057de0c663974d711b2906d709da716bf5d6fa7a19dce1fe6ee7fec4b9b95badf58670773ef81f1959b75c85cf83e178fd05933c57bf491390"}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_app_signature_min_version:android": {"template": "{{ string .value }}", "data": {"value": "1.1.0"}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_app_signature_min_version:ios": {"template": "{{ string .value }}", "data": {"value": "1.0.0"}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_app_signature_min_version:sdk": {"template": "{{ string .value }}", "data": {"value": "1.0.0"}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_securepin_at_frontend_required": {"template": "{{ bool .value }}", "data": {"value": true}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_dump_redis_tokens_before_count": {"template": "{{ bool .value }}", "data": {"value": true}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_read_token_once_before_delete": {"template": "{{ bool .value }}", "data": {"value": true}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_login_prompt_expiry_in_minutes": {"template": "{{ int .value }}", "data": {"value": 30}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_singpass_prompt_expiry_in_minutes": {"template": "{{ int .value }}", "data": {"value": 15}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_update_pin_token_period_in_sec": {"template": "{{ int .value }}", "data": {"value": 300}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_oauth2_access_token_threshold_minutes_for_ref_token": {"template": "{{ int .value }}", "data": {"value": 525961}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_login_notifier": {"template": "{{ string .value }}", "data": {"value": "<PERSON>eon"}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_email_otp_expiry_in_minutes": {"template": "{{ int .value }}", "data": {"value": 5}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_phone_otp_expiry_in_minutes": {"template": "{{ int .value }}", "data": {"value": 5}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_biometric_auth_unenroll_upon_pin_change": {"template": "{{ bool .value }}", "data": {"value": false}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_max_refresh_token_per_user": {"template": "{{ int .value }}", "data": {"value": 1}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_finid_client_config": {"template": "host: '{{ string .host }}'\nhostVN: '{{ string .hostVN }}'\napiKey: '{{ string .apiKey}}'\nclientID: '{{ string .clientID}}'\n", "data": {"host": "https://stg-ignite01.stg-grabpay.com", "hostVN": "https://stg-ignite01.stg-grabpay.com", "apiKey": "test-api-key", "clientID": "test-client"}}, "gid_digibank_whitelist_service_config": {"template": "serverAddress: '{{ string .serverAddress }}'\nwhitelistSearchServerPath: '{{ string .whitelistSearchServerPath }}'\nserviceDiscoveryEnabled: {{ bool .serviceDiscoveryEnabled }}\npoolMaxOpen: {{ int .poolMaxOpen }}\ndialTimeoutInMs: {{ int .dialTimeoutInMs }}\ndefaultHealthCheckFrequencyInSec: {{ int .defaultHealthCheckFrequencyInSec }}\ndefaultHealthCheckTimeoutInSec: {{ int .defaultHealthCheckTimeoutInSec }}\ntimeout: {{ int .timeout }}\nmax_concurrent_requests: {{ int .max_concurrent_requests }}\nerror_percent_threshold: {{ int .error_percent_threshold }}\n", "data": {"defaultHealthCheckFrequencyInSec": 5, "defaultHealthCheckTimeoutInSec": 5, "dialTimeoutInMs": 5000, "error_percent_threshold": 25, "max_concurrent_requests": 100, "poolMaxOpen": 10, "serverAddress": "http://whitelist-service.backend-dakota-app-01.svc.cluster.local", "whitelistSearchServerPath": "/api/v1/customer/whitelist/search", "serviceDiscoveryEnabled": false, "timeout": 6000}, "hash": ":global.2fICZtQj0DijDvDvikTR8dpkoeg=:global.stg.4XxXV1CJV2szfpuXkN5TUavu05g=:template.101eFivi824Uop7mdnaCQMMbcgY="}, "gid_oauth2_refresh_token_for_requires_consent_always": {"template": "{{ bool .value }}", "data": {"value": false}, "hash": ":global.2fICZtQj0DijDvDvikTR8dpkoeg=:global.stg.4XxXV1CJV2szfpuXkN5TUavu05g=:template.101eFivi824Uop7mdnaCQMMbcgY="}, "gid_allowed_login_methods": {"template": "{{ .methodsMap }}\n", "data": {"methodsMap": "{\"MYINFO\":false, \"SINGPA<PERSON><PERSON><PERSON>GI<PERSON>\":false, \"SUBSEQUENTPINRESET\":true, \"EMAILOTP\":false, \"PHONENUMBER\":true, \"PHONENUMBERPINLOGIN\":true, \"PHON<PERSON>UMBERPINSIGNU<PERSON>\":true}"}, "hash": ":global.2fICZtQj0DijDvDvikTR8dpkoeg=:global.stg.4XxXV1CJV2szfpuXkN5TUavu05g=:template.101eFivi824Uop7mdnaCQMMbcgY="}, "gid_msg_email_email_normalization_v2_rollout_pct": {"template": "{{ int .value }}", "data": {"value": 100}, "hash": ":global.2fICZtQj0DijDvDvikTR8dpkoeg=:global.stg.4XxXV1CJV2szfpuXkN5TUavu05g=:template.101eFivi824Uop7mdnaCQMMbcgY="}, "gid_digibank_location": {"template": "{{ string .value }}", "data": {"value": "Asia/Kuala_Lumpur"}, "hash": ":global.2fICZtQj0DijDvDvikTR8dpkoeg=:global.stg.4XxXV1CJV2szfpuXkN5TUavu05g=:template.101eFivi824Uop7mdnaCQMMbcgY="}, "gid_allowed_phone_number_regex": {"template": "{{ .strList }}", "data": {"strList": "[\"^(\\\\+?6?01)[02-46-9]-*[0-9]{7}$|^(\\\\+?6?01)[1]-*[0-9]{8}$\"]"}, "hash": ":global.2fICZtQj0DijDvDvikTR8dpkoeg=:global.stg.4XxXV1CJV2szfpuXkN5TUavu05g=:template.101eFivi824Uop7mdnaCQMMbcgY="}, "hedwig_http_client_config": {"template": "host: '{{ string .host }}'\ntimeoutInMs: {{ int .timeoutInMs }}\nmaxConcurrent: {{ int .maxConcurrent }}\nerrorThresholdPercent: {{ int .errorThresholdPercent }}", "data": {"host": "https://hedwig.myteksi.net", "timeoutInMs": 2000, "maxConcurrent": 100, "errorThresholdPercent": 50}}, "gid_user_deactivation_on_max_soft_locks": {"template": "{{ bool .value }}", "data": {"value": true}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_max_soft_locks_before_user_deactivation": {"template": "{{ int .value }}", "data": {"value": 2}, "hash": ":global.2fICZtQj0DijDvDvikTR8dpkoeg=:global.stg.4XxXV1CJV2szfpuXkN5TUavu05g=:template.101eFivi824Uop7mdnaCQMMbcgY="}, "gid_services_history_enabled": {"template": "{{ bool .value }}", "data": {"value": true}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_digibank_max_last_login_seen_count": {"template": "{{ int .value }}", "data": {"value": 1}, "hash": ":global.2fICZtQj0DijDvDvikTR8dpkoeg=:global.stg.4XxXV1CJV2szfpuXkN5TUavu05g=:template.101eFivi824Uop7mdnaCQMMbcgY="}, "gid_previous_pins_record_count": {"template": "{{ int .value }}", "data": {"value": 5}, "hash": ":global.2fICZtQj0DijDvDvikTR8dpkoeg=:global.stg.4XxXV1CJV2szfpuXkN5TUavu05g=:template.101eFivi824Uop7mdnaCQMMbcgY="}, "gid_previous_pins_read_write_enabled": {"template": "{{ bool .value }}", "data": {"value": true}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_previous_pins_policy_enforcement": {"template": "{{ bool .value }}", "data": {"value": true}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_digibank_enable_timestamp_device_signature": {"template": "{{ bool .value }}", "data": {"value": true}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_enable_manual_logout_for_unbounded_device_login": {"template": "{{ bool .value }}", "data": {"value": true}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_enable_pin_reset_push_notification": {"template": "{{ bool .value }}", "data": {"value": true}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "enable_gs_challenge_children_jti": {"template": "{{ bool .value }}", "data": {"value": true}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gs_challenge_ttl_in_seconds": {"template": "{{ int .value }}", "data": {"value": 900}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_max_pin_attempts_notification_enabled": {"template": "{{ bool .value }}", "data": {"value": true}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_oauth2_access_token_pop_time_skew_sec": {"template": "{{ int .value }}", "data": {"value": 30}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "weblogin:enable_aes_cipher": {"template": "{{ bool .value }}", "data": {"value": true}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_oauth2_exp_ref_token_after_last_access_ttl_seconds": {"template": "{{ int .value }}", "data": {"value": 51840000}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "gid_jwt_issuer": {"template": "{{ string .value }}", "data": {"value": ""}}, "gid_is_profile_id_enabled": {"template": "{{ bool .value }}", "data": {"value": true}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "send_otp_for_onboarded_phonenumbersignup_users": {"template": "{{ bool .value }}", "data": {"value": false}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}, "static_pre_otp_checks:DIGIBANK:checklist": {"template": "{{ .value }}", "data": {"value": "cooldownStatus"}, "hash": ":global.kMURblYdZQQfHg2IxyjfeWNgOAY=:global.stg.2jmj7l5rSw0yVb_vlWAYkK_YBwk=:template.Dq-loT_ne2zgmFvzG94oAzgmR24="}}, "versionNumber": "v20210204.1330.57869.grab-id"}