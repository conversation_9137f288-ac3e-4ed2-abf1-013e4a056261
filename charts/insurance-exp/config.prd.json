{"name": "insurance-exp Service", "serviceName": "insurance-exp", "host": "0.0.0.0", "port": 8080, "env": "prd", "ownerInfo": {"name": "digibank", "email": "<EMAIL>", "url": "digibank.myteksi.com"}, "systemClock": {"timeTravelEnabled": false, "offset": "0d"}, "data": {"mysql": {"master": {"dsn": "{{ MYSQL_USERNAME }}:{{ MYSQL_PASSWORD }}@tcp($MYSQL_HOST$:3306)/$DB_NAME$?parseTime=true&loc=UTC", "maxIdle": 10, "maxOpen": 20, "connMaxLifetime": "1800s"}, "slave": {"dsn": "{{ MYSQL_USERNAME }}:{{ MYSQL_PASSWORD }}@tcp($MYSQL_HOST_REPLICA$:3306)/$DB_NAME$?parseTime=true&loc=UTC", "maxIdle": 10, "maxOpen": 20, "connMaxLifetime": "1800s"}, "masterCircuitBreaker": {"timeoutInMs": 1000}, "slaveCircuitBreaker": {"timeoutInMs": 1000}}}, "statsd": {"host": "$DOGSTATSD_HOST$", "port": 8125}, "trace": {"host": "$DOGSTATSD_HOST$", "port": 8126}, "logger": {"workerCount": 10, "bufferSize": 10000, "logLevel": 2, "stacktraceLevel": 4, "logFormat": "json"}, "redisConfig": {"addr": "clustercfg.dbmy-prd-insurance-ec-insurance-exp.wsxujn.apse1.cache.amazonaws.com:6379", "idleTimeoutInSec": 1, "poolSize": 300, "readOnlyFromSlaves": false, "readTimeoutInSec": 1, "writeTimeoutInSec": 1, "password": "{{ REDIS_PASSWORD }}", "tlsEnabled": true}, "kafkaConfig": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "insurance-exp", "clusterType": "critical", "enableTLS": true, "initOffset": "newest"}, "customerJournalStreamConfig": {"streamID": "customer-journal-audit_log", "topic": "prd-audit-log", "dtoName": "AuditLog"}, "contactUpdateStreamConfig": {"streamID": "gilstream", "topic": "gid-login-stream", "dtoName": "GrabIDLogin"}, "profileUpdateStreamConfig": {"streamID": "prd_profile_update_event", "topic": "prd-profile-update-event", "dtoName": "ProfileUpdateDetail"}, "paymentEngineTxStreamConfig": {"streamID": "prd_payment_engine_tx", "topic": "prd-payment-engine-tx", "dtoName": "PaymentEngineTx"}, "customerMasterConfig": {"baseURL": "http://customer-master.onboarding.svc.cluster.local"}, "customerExperienceConfig": {"baseURL": "http://customer-experience.onboarding.svc.cluster.local"}, "accountServiceConfig": {"baseURL": "http://account-service.core-banking.svc.cluster.local"}, "partnerpayEngineConfig": {"baseURL": "http://partnerpay-engine.payments.svc.cluster.local", "partnerID": "614c7879-5a90-4878-a08e-eb8423815224", "carInsurancePartnerID": "d7f50297-37b3-11f0-81d4-0a51196330d3"}, "riskBrokerServiceConfig": {"baseURL": "http://risk-broker.fintrust.svc.cluster.local"}, "pigeonConfig": {"baseURL": "http://pigeon.pigeon.svc.cluster.local"}, "sqsV2": {"queueURL": "https://sqs.ap-southeast-1.amazonaws.com/************/prd-insurance-insurance-exp-sqs-insurance20240807073931282800000002", "awsRegion": "ap-southeast-1", "exponentialBackoffBaseIntervalSeconds": 10, "waitTimeSeconds": 5, "defaultVisibilityTimeoutSeconds": 60, "workerCount": 1, "maxNumberOfMessages": 1}, "retryStream": {"queueURL": "https://sqs.ap-southeast-1.amazonaws.com/************/prd-insurance-insurance-exp-sqs-retryable-stream", "awsRegion": "ap-southeast-1", "exponentialBackoffBaseIntervalSeconds": 10, "topicExponentialBackoffConfig": {"prd-payment-engine-tx": {"baseIntervalSeconds": 0, "growthRateSeconds": 3600}}, "waitTimeSeconds": 5}, "productWhitelists": [{"name": "cyberFraudProtect", "rollOutConfig": {"salt": "8523b98a-51e9-4697-b55b-2498dcc3e6ee", "whitelistedUserIDs": [], "blacklistedUserIDs": [], "percentage": 100, "cacheDurationInMinutes": 5, "supportedAppVersion": [{"appOperationSystem": "iOS", "enableAppVersionFilter": true, "supportedVersion": "1.13.7"}, {"appOperationSystem": "adr", "enableAppVersionFilter": true, "supportedVersion": "1.13.7"}]}}, {"name": "carInsurance", "rollOutConfig": {"salt": "f14cc733-308f-11f0-81d4-0a51196330d3", "whitelistedUserIDs": [], "blacklistedUserIDs": [], "percentage": 100, "cacheDurationInMinutes": 5, "supportedAppVersion": [{"appOperationSystem": "iOS", "enableAppVersionFilter": true, "supportedVersion": "2.7.20"}, {"appOperationSystem": "adr", "enableAppVersionFilter": true, "supportedVersion": "2.7.20"}]}}, {"name": "travel", "rollOutConfig": {"salt": "a939d632-765d-11f0-81d4-0a51196330d3", "whitelistedUserIDs": ["781eb6c8-a656-47cd-8538-3fe30ba0a216", "526c2fcd-93d3-4061-9ab6-ce16051f0d80", "21506c19-7a92-40cd-98ac-1fadc78f6ac0", "5b2867b5-d91b-4365-b61d-07028d06a786", "ffc7bc1c-9b31-4c41-8dbe-b23e7fc504c0", "3f5b276e-c099-49a4-b646-0e39ef7f6a32", "Beta-Whitelist-Users-Below", "d8969362-f245-425c-9a5e-1bbfe874e53e", "595c213b-6e2d-44eb-b7ef-006abbc15df2", "45a77dc4-f24e-4b60-bf7b-152bab4ea2f6", "6305a9d8-f8b6-4dbd-bf7e-04161c5ee638", "42b18637-5d66-4cbf-9217-2bb7450d13dc", "c929509b-d4bc-42b7-b922-112ce9aadffa", "33d54858-6ff3-448d-8f3a-bfb9459e34e0", "1d507d45-fa2b-432b-9cdf-61f06d21ff6e", "9dd32a1c-9930-4250-acfd-832908716245", "323386df-566f-4b90-9a67-2d7f394e3af8", "0a580516-70d0-49e4-a333-1a0706770c20", "9ad6cb53-1a40-403a-ac39-1d5083bb121b", "f97e4a8c-63b4-4dca-bd25-1633322ee758", "89251ce5-ca3e-465c-a5cd-ac25b028df7d", "52f2384c-8808-49ba-93bb-40b06c1fe2b3", "d0633ed4-2caf-44c7-8a24-6e9c70a659d2", "ba86dd78-91e2-4539-9910-e0a032326ac0", "6dcaea64-51a8-49f7-ae44-deea2cee7e6f", "59c7d1f7-9f61-4741-8dce-036019e39954", "f94b4897-04dd-466e-a0a2-e6a29b0948f3", "34a794d9-c428-4258-89b5-8763<PERSON>ead274", "6f4f164b-3f76-4594-8710-94e89c352404", "a5ed8dd3-6d6b-4b5d-91df-9520ce00a3ac", "cff55079-1549-4473-8fcc-f2271bee56d6", "bdabec3e-4d7c-4f18-b988-e4f66dcff74c", "c81c4bb8-ee35-4dc5-af59-f0c8b635a612", "e0cfa881-8147-474e-8b09-9930e4580585", "2a8de141-a7f7-45da-b8d4-83e1dbe3e111", "c61a9593-c3db-429d-a18c-10891e1df596", "53be44f7-4e85-4830-92fe-3bfa8a9119dc", "409ff7b5-ff98-4b14-83a6-c4bf94f46178", "2c329703-2dde-40d9-809a-f69aa46382de", "1920e453-1e9f-4768-8bf1-eb007f10de9f", "c719cfd4-cefd-4672-8849-796c3b7f22d6", "e96ef633-7df9-4994-910a-244537bbdb52", "5dc28278-afde-4851-afa1-5b80376c0422", "1731e5d7-ce8c-40b2-a204-e5653d8365bc", "5ad3672f-bbd0-4681-b0ea-1443f28c8a06", "ab0d5240-1099-4b6f-ac2b-ac09ebc729a8", "c292fd7d-42d9-4cc8-a56f-1942e1a010ff", "e5b92627-1752-478d-a5bb-cc35a759734d", "2a1812db-852b-4670-aa61-c80f681b1f0e", "15f8aace-93ea-4e3f-94d8-c8519c9b712b", "3ac498f4-2bd9-409d-8b4c-d4d465c50f58", "454350ac-3657-44a9-994f-981f33fcb8e3", "9fb7a20c-dd4d-4c72-8b03-60b82341ffd7", "d66dee32-219e-4003-8135-69f60c614ec7", "2ef1c93e-9431-4786-b172-a4874cbe532c", "bef06a6a-e762-479c-b29a-2f957a9afbe5", "1f11510d-a957-49cf-9c00-82159c27cf46", "57fbacfd-1fcc-4a9f-9293-ffa4d69760a3", "ffc7bc1c-9b31-4c41-8dbe-b23e7fc504c0", "f8a10f4f-236d-464f-8935-a0aed92e9515", "bed1ec6a-dfd0-403f-8ac9-1aff8063d0fb", "efec7b8f-237e-42f8-bf31-7489b2c5f2fc", "5a04f952-ce4e-493d-ac4b-c8c7ebaa85ae", "0323b4f6-f58e-4930-bfe3-5746ddd68ee1", "a2235a27-cc66-48f7-935c-04c0d104486a", "e2224f75-3ef1-4aa6-ac05-947761deaea7", "e448e545-0393-4e82-ae93-953c83e13c8f", "d83eca71-f3fe-4f70-946d-a2830e9ad347", "09d7988e-b27c-428c-9d7e-93750546344e", "924e0a90-5b07-4687-aca3-d88487292de8", "386f2f94-13a1-4cee-83a6-006783735ccb", "155ef493-77e2-4c45-b049-488c0191f3f1", "0887980a-dc56-4361-9070-51d913ca6c8e", "33a88dd2-4f4b-4789-b743-f87313f7fbf6", "ba6b5fc6-76f8-4bc4-a558-5d8a4a431362", "7ceb7558-2d8a-45f8-b044-d3c5591ee09d", "29a2239b-1b65-48a1-b9db-61bef91905c2", "c96ec6d9-b5a8-420c-bc1c-998587619024", "438e62b0-8086-4953-a140-1e95c6b4ac75", "4953fc3a-5cfb-43e8-a152-560f6b54aa10", "4b04f6d3-d170-4657-ad18-874d5ecefee6", "ba346454-3fad-4068-9908-7060d89fd4e2", "a4fa1631-16f7-4aff-b779-dbc8bb1adeba", "525865a9-cfb9-4d67-b827-171a947a7525", "8c678b31-0901-4fb3-853f-53ace21424db", "08b74e9d-cf1d-422b-bf46-893d9ca87e0c", "7b19aed7-2da2-4ca0-bd7d-b4cea85ac542", "1c712309-31cc-4cc1-a346-8fab45632e99", "ed28e820-ef9d-42a1-a54c-359a3bd33279", "49cdf5e6-f8a3-46fc-8747-f53d3ff47d87", "00b4ade9-ad44-47f5-9ee9-55b987a70963", "0d11cf11-7566-48c1-8ec3-aad667496c87", "5017e85f-9916-408b-bfd8-2afe19d0abad", "40c77ca2-41df-426d-bbc2-2ee56309fcdf", "73dfdfe8-793c-4000-b2ff-4e3c56a09900", "d8a943c6-65c7-4864-bfa5-22cc3dc62b5c", "ab1999ff-3a8c-430e-ad29-5927db884364", "ce57d423-9d62-41ab-8df0-f09221043221", "82780335-d685-439b-ae46-b8f4cd965e2d", "7d6fabef-b517-4515-93e3-f4f22f323cde", "049705a2-4188-457d-abc1-6e777ece4592", "c3e00209-8ff9-4c15-8c33-2faf781ca9fb", "ef831288-6def-474d-bab5-88b21ce5d11a", "ab2700c0-8fa2-478e-a059-499d8efaf9ed", "327b124f-4b2c-47eb-aa38-1bf24ab63711", "cf241a12-be66-4af6-8b5d-08e3f6dc1106", "fe9b8c75-124a-400f-a043-c2b150a127fe", "f091726b-801c-4e4f-a3c1-3e3a3de3b268", "c3a9501a-b740-4383-9449-ff5507a9c7f6", "a710d2b1-54a1-4933-b60e-0a0000a4a60a", "7dd3ff80-4f64-478d-9bbb-7b3a43ce1794", "792b20f7-61c1-48a5-adc1-36443fc666a2", "8f18d740-bd85-48b7-ae08-31b03c2806c7", "5de356b6-d6a5-4e79-891c-57befb5c9c56", "6d99d18a-79fc-4a9f-8f7e-ad6c62e869eb", "cfca719f-548f-47be-b2f9-60a6fa583de5", "4adcd5a8-3398-4923-a571-66d84a590892", "28cad8f8-2f21-492b-9486-bc21fc4cf30a", "c6603204-f9e4-4bc1-956a-8d4b9134d68f", "e16bbb55-91d0-4657-9820-eec9b69371d2", "5a57cfcf-1e6e-4f03-95b9-51f1dde7c02e", "4faa3f87-cefc-4f2d-b6f4-7ec9e964f077", "3083e3c7-1d23-49ae-9e63-c6ad3df19163", "fc05d1b4-0368-4103-963c-2671e73cd5ae", "57b3b7c9-9f07-4732-8c9c-6c4e506be3c5", "c31c044b-7b07-48f5-8974-debe75126aba", "0052e650-a561-46f9-b178-bcd409c7b053", "44cd1cf2-d8a8-4d80-89f3-af0b0ff2dde8", "ef48dc65-79b6-4f3f-aadb-c209982a7f04", "a27326f1-b81d-4bda-8b3b-dc65a5b0ee16", "eb67fc25-1cc6-4b8d-ae46-4568861ea371", "c7f5c4b7-d2bc-4bc4-a5c1-6a3a86cc3444", "adeba737-fc2c-44f2-9fcc-e92ae7547cff", "05360ea6-0a60-4228-893b-bd7bc6fb3cd0", "eb4a9c81-2983-4053-8260-34bd27e49879", "fa34af9d-4230-46bc-a44c-fb84fe51e653", "cdf4b8ac-ddac-48f5-880d-359bece8b0fd", "df7374db-5ada-4c08-8652-d13c25c3a24f", "27f8aed2-b570-470b-816a-87c92b4c65b8", "c65529d2-5089-4eb2-a4ca-198767c7c8a7", "fc9f4c0f-dd44-4c12-bfd8-56e4a593d27e", "854679b9-59af-4614-bedc-4ceca94e47b1", "fc4c262b-b524-4c9b-8cb7-f60b26950cbd", "2d4e09b4-2a6b-4da2-8346-d36fde6e8cde", "8cd9b1d3-337b-4d57-8094-665eee87de74", "136aca31-43c1-4de4-aed6-c2f37a409ea3", "710fe274-7bf4-4327-a68d-c1a41ff316f0", "4797884a-a71b-4682-8736-33839236d9ee", "eb5d921a-e2a9-40c5-b74c-bb985cac0976", "c5e9d75a-c939-4871-aab4-46c0fbb7bae5", "dc00bf55-af77-4a88-a70d-da91fc39231c", "456498db-4315-4e46-8e9e-8ad3918e1f01", "f833e1de-701c-4d9c-8fea-39ef3edcf440", "0086dbb9-d746-499c-884a-1746a1231b7f", "67f6466f-3d83-4d15-808b-eaad4405a1fe", "8d9f27ed-6e89-436a-a4d6-3f106148bdfa", "9571fd5f-99b3-4675-81e9-832d4c78590f", "f8a48d8a-6225-4ef2-9595-1ba4badba700", "d348cb9f-760a-42ce-82d4-95935d502739", "d93ce62c-6e82-4886-97e3-32f1d6b29b16", "38d9808d-b3d7-4295-98b2-4586f5b0ce5a", "722a4d62-740b-4dd5-b6fb-1ac2ede5796b", "41b86f0f-9789-416e-abda-d1be2834dfdd", "c2134ca5-5cc3-4144-ba2c-148faa2e46b8", "83151c69-c9ce-428a-bc9c-dbdeb0324e0d", "1e4ed37b-abcf-4f83-ab90-45c2b112e879", "2e60d914-9853-48fe-8b67-92ca5911044a", "c970cbcd-5368-4c12-9d03-50e6ab88c743", "c73f61f4-f14f-44c3-bdbc-d13b8feb106c", "26c707e4-a665-4279-8b6c-79afd1e1f6f6", "576ffd65-2526-46b3-bb94-b08956eeaf18", "7a466c15-469d-441a-8705-d5f2ad472a6d", "8b851fdf-4487-48f1-8a76-47940d7daced", "08f308b2-2524-4465-8c6c-7293f61cae33", "013cb6ea-e92c-4af2-91b9-ce142408db83", "61c33912-a39a-48d8-a9a6-216b54c78aea", "84e9eb30-2657-4a0d-8b3a-507b61975a40", "c924f10a-f372-4a82-8472-16f496cd97a6", "2a0ddbe7-139d-45d4-8a2f-9a2b092a9c48", "0b0a5d05-8931-406f-94e1-83a4dd877d56", "758f2ea4-dbc8-4d65-92a0-4440e57bc37a", "a55514c0-13dc-4837-979c-ddfa9b5c8612", "35536dc2-62d5-4a3c-ac81-c0faf5bd7212", "71b18f1e-34bb-4d88-b6e3-464e59716bef", "6a12d3b5-8348-4a11-b9c4-9f135dfedeb9", "7b74be66-ebcd-4434-800f-881edd35a97e", "dcdca411-302f-4877-8219-c3a94497fcf4", "280b961c-6519-4e13-a323-b011e92aaf31", "8c4d991c-d29a-4663-ac8f-2b531471cf63", "b6f724ba-c1ac-40ca-b6b3-b7f6275c6be8", "10151cf6-29fb-4cc2-9b52-c253adebc834", "a268eb44-7530-4b63-8ca1-7a75bced911b", "8ed744a9-92ad-43a4-92e0-b1024acc87cf", "e059dea6-68e6-4099-81ad-cd3e0ea575bc", "d0a4260e-2903-4e0d-bc8a-5caf33f9a365", "c546e562-b7d6-4f6c-924e-d02cfddb7349", "b9a607dc-ff61-478f-abe8-762133d9c23c", "46f1cb41-21c4-4f30-a69c-8fc3a4f53038", "97f91a90-6bfa-4cd5-83c7-d70ad0fe8214", "11fbe5ad-b2a3-4bfb-ae97-0bfbce54d630", "574e0622-6509-4b2d-ba9d-7c8514772779", "9768a73a-6f91-48b5-a7e9-551ed766fc5d", "489cae61-bab6-49d5-95d6-ad98c9b0383e", "4698145a-3d89-4a90-892a-c9ecd8b3f1f7", "8309f183-2dde-4b26-a847-b1d129f846fe", "c1ce1c7a-2473-4374-a4b8-38bfc6f06ead", "9d6b7b93-4c6d-4474-b35b-13c172baaf32", "210a81fc-1e29-4bd6-bae9-14bc0b152c0a", "45bbad24-f8a0-411b-8bec-a3ae95edda1a", "e474f10f-6360-4754-b80b-b7e1dc4dc184", "d86a8e9e-07ef-42a2-a431-c48a388dcec2", "c737b9d9-80ce-4baa-92c5-5b9a55c5328c", "245029c7-846b-404b-8b59-e32f3ecc0b34", "a9d7e8dc-cddf-4a7f-a17b-d2d3d6048af8", "f14985c0-e2f8-4c10-bdc9-9939cc43b3de", "4e63aee5-232a-4e0a-9dde-e1ac6d33cb77", "04419651-b23e-437a-9873-d40c97749397", "b359bdc4-8a06-430f-aa9c-f1a7ae895a04", "e95b8e3b-9b13-4b1b-bb07-00db39bcf3b0", "77a15d24-545a-475f-979e-416f469948b0", "cbc45568-4e63-483d-ad6a-dfb3b8e50692", "33ed9f2e-5260-4417-a87a-c2f7f0f150a3", "22d51315-3d9a-459c-9c23-d2d878e21abf", "d416963e-ce76-4a83-86d5-7a2e522ac734", "431aad83-91e6-43d0-8cc1-6c8da0656d6b", "3d35474b-c747-4b4c-860f-590d19912d95", "9662020b-b51c-4c61-a9de-d2c5fc6d942b", "8093341a-f72c-4580-866b-6066ee52f467", "565619d4-2f3d-4610-b6f8-89ab5a9626a0", "1b82ba2b-dcdf-4c9f-8176-fb0977745c0b", "040eaff0-8c99-48d2-abc9-e6e37bd15272", "7adaee9d-48b4-43f5-b051-c07a842d1d1e", "94c4ac2a-4636-44ca-a6f4-b3f5ff4d633d", "74191242-97c7-40f1-934b-3318e06cf1f8", "0d389054-2390-4ac0-b0da-e62f9380a9a7", "802cdf1e-4bfe-4ca3-a506-c5399ad99f92", "532ee196-34a7-4b5d-b75f-e6d1bee30007", "3b2d719b-79f5-4fe5-8e41-8a0ae021e0cd", "2ea6dfb0-6c9f-445d-805f-6f423aaf713c", "223f3aaf-1896-4b04-9000-51938c1b296b", "c5a4dfdd-b5bf-4031-acbd-fd170338f5f8", "b5b98bdd-2abc-4a09-94c5-a8ffb0a058c4", "5b22dafa-a4f2-477d-85f9-e4f949a0c58c", "0d872e8f-575f-4875-8a5c-4878627093a3", "b98c3710-5636-48ae-8a7f-38960f3b512c", "3d8edd35-ce9f-4ed0-9335-048c4c852741", "2320517d-d98d-447e-a793-165baed27644", "57de0b0c-d665-4ce8-8ef0-0a7bdce862b0", "0ddd847c-e77d-4cd3-968a-3b65b37ffb28", "e1c345e7-ddec-4ae2-a099-37b77f72d022", "dff9d6b5-8c94-4f26-9693-839d1b529611", "898d54ff-f4ff-41a8-88f6-c9286090b4de", "80041559-68e2-4e09-8eb3-35a0995f1337", "f6bcc92c-0c91-4beb-9a9f-9d4ce1eb0c55", "a4613342-319b-4b7b-b4ae-aba13236a49d", "e7e79589-7b89-4c07-b920-d71b26ba02ab", "906ad2c6-75e5-4d59-b56e-67e49e2daccc", "4bf1b660-c9b3-401b-9154-81257f91bfcf", "a0480228-8d87-4897-928a-eed8ffad7bc1", "b5c44ee0-4e3f-427c-b79b-e52ed67486ab", "3e714b8e-2266-45a4-8965-f780808bd47e", "ec858205-6037-49dd-84a4-18b4f9483fac", "218bfb49-1f38-4fae-a7de-cdd0fa3eddc7", "67543a04-7dad-46c0-b3db-28fe00e1d59c", "4e72fd8f-8c13-4e0f-8c68-8f280262324f", "5b6592fb-245f-480c-be95-52c230379f24", "92cc7cd4-293b-40c9-b7f8-c8bce20796f1", "4abea85d-0a93-4105-9313-832432d319a5", "4b8407b6-9d37-498c-af00-a93b4e3cbb2f", "21718b1d-f69e-4d74-964f-c38781826b9b", "9db25435-b1e4-469d-a6b6-6544dd66efd4", "35f6f9f0-6cc7-4618-a34c-6be6b6459d7f", "3f8af4ef-f514-4e29-95d8-1ef96f6ff310", "596aa4f2-a94b-4950-b854-69e6b2301d8d", "9fc24acc-e25f-4e7c-b86e-3bc482763deb", "3e441717-1cb4-4d7f-b65c-bc0e557dde34", "de426457-5ee6-4c17-8d07-c7b19d1da65b", "d0414ee1-4c71-4a09-8f17-76f77dab2b59", "3e081e2d-0ec1-4beb-b775-bd8cf1eeff0d", "71628563-276e-4c5a-9ff5-f62ddab765b8", "e6276ddb-2b20-4ea7-88c9-1dddf22f98fb", "f5bbf59d-2b89-42ae-9330-dbdd33c9c5b0", "6ee753aa-df3c-4761-acc2-203547b960da", "9f17e254-54aa-4bf6-a6f3-e1404cac3d2b", "566b19af-52d2-40ac-94b1-ba29157a27af", "526c2fcd-93d3-4061-9ab6-ce16051f0d80", "8650f906-8d03-4e6e-8002-0bdc3cbc424a", "3918e738-ee85-4d00-8a4a-5ec68eebea6d", "f627da62-e857-4c72-933c-b223bb81a8c9", "b5a5a6a1-d388-40ca-90fb-df04d0a14397", "719fb59a-8280-4ca0-84c4-3cff893bd23d", "a0b0dc0f-0518-424d-90b3-0951959fa826", "6fe40ea6-92f6-4cf1-87aa-7c5b520b8224", "52be7ad4-d871-4c01-bdc4-b278ef1d372f", "9162751f-d5d2-4567-b46b-9b366959dd77", "161aa909-d280-457e-8628-d411fde518fa", "c54f5344-2155-404c-bff5-e36cc0549b6b", "e5703ee7-4b4e-4fc1-b8a9-0436099a524e", "4b091baf-6131-40af-8c53-3ea4c60dfca4", "df8c78c9-e737-417c-9de0-6e05d40893fb", "3e3ea034-4e71-4b2b-9a49-fb37532611cd", "b1b282a9-033d-46df-b94f-fb9fd8f1c963", "a82841ad-7b97-4215-a3de-1e58190d431a", "a155d46b-6d56-45b7-9e61-3aec58a80f55", "4905a47b-77d2-4138-b36d-39b39020a6e6", "c5197e1d-ca5e-4c27-9f7e-a6d6acd06ef2", "6a2f91be-7890-49bd-a839-d85d86d578a6", "7524fc7f-9c4e-43af-8727-a671ab17515a", "7d14357c-931e-4ac9-8ffb-0d8e6cf4e950", "5b2867b5-d91b-4365-b61d-07028d06a786", "ae43cfd0-c913-45d9-8dd5-2af2a5b1fe04", "b7f35e78-575f-4f04-a483-77305e4d145d", "98a15e85-6fc5-48d5-a6b9-d3d72b587034", "19c0acd9-fbdb-465e-9c6f-4557045a12fe", "04caca97-c8f6-4cfd-8e0c-9599fa210aa0", "8c59719f-3e30-4d50-a95e-13df4ac2c49b", "083a3782-e2d8-4063-9614-52c1bd78f189", "5145c2ef-f68f-4616-81f1-464b040b0646", "3642161b-26bf-4cf0-a2fd-cbaa396f6084", "62089a08-42a5-4abe-9384-4f0ab7b03d6d", "0d3a9973-acb9-4e80-983f-129c4e24b106", "4b04bca3-e318-4aca-aa2e-687d85dd3da3", "7ff39140-c587-498a-9cf8-ab1eedc86b15", "90fbaaaa-837e-47e4-9825-9e62a100a47c", "9180efae-d262-42a3-9b24-782daf2c9812", "87e25884-4910-4de7-9898-8d8c8137a300", "d03f1b96-87fb-453e-9a6d-8c806743767f", "1b30ac7c-bbe4-4b42-a5be-bf7b920b8889", "ceb69218-b546-42d7-ab0e-20311753cbac", "3953be6f-9873-439d-bf59-60da8fb0ea01", "24cdf8d8-93a0-4712-a44f-0dfd0351e592", "33682ef6-fb87-4ce7-b351-033bb45116e8", "bac44123-b2ac-481f-a76d-099dbd69de00", "e0fb25b6-15c0-4775-be3e-b3f13069b47e", "480d9b9d-3ca4-486c-98d2-768b714827d1", "f582f0b4-414d-487b-9ba9-4e9f73c826fb", "3befd595-a4cb-4e57-99b3-6ffb0ada0b1d", "91190034-bece-4ebb-821f-32d1b826820f", "e3bf83cc-d5d0-4fe0-b99b-a39f268ff6ae", "0fb349f8-8a78-41ae-a510-d0c2f97287eb", "3d219d3a-cb35-413c-bfdf-caeac48b6df9", "8fd1fc50-c0b7-4b73-a932-069c15adf096", "1c17deaa-9b55-4040-a566-fd431abf8449", "3a828f5d-695b-464a-b3fe-1ea13d8fb472", "795610d0-3ae1-44ae-839e-9e06e371e924", "c3d2d109-5ca3-43d4-a188-f7ef1e39a684", "ddf466c5-7751-4d8f-9b01-de39af3e7b37", "f542c0ca-517f-48e7-9e34-7d461047d8bf", "e3e507e4-9360-4808-ba3c-5dfe04bbbf2b", "ad5bd641-f103-4b78-b781-0ae681cd026a", "25fc8874-3e68-41bf-bdd3-873282e13a2c", "0205944a-8679-478a-a7da-0f3de726c666", "3233e94f-ca2e-4bae-b804-c12756fa9371", "1d4cc319-6949-40b1-baa5-bf75b579e231", "f292d56c-9332-454a-a78f-1605a6d79c57", "ec429493-63b4-4df8-9feb-36f22c88108f", "e8dbc82a-d329-49b5-aca0-1d1f76dce6a5", "5d1388d0-b530-4c73-9b85-fa591d96a2e5", "4ff23d32-fa9e-448a-8b01-a0aaa4667a78", "d8d56893-a261-458c-94aa-305eee15fbfe", "5107110f-7b1d-455a-a341-8561c4802a15", "a84a143a-05dd-476c-b415-ffca48b5d8ac", "1b98c464-f880-4e5f-a696-f4230848cd90", "cc5d970a-8457-483d-890a-b61d62e6eb2f", "194a3735-5417-43c7-937e-c144c21dbf3c", "587f2dfa-1895-4218-b137-05f0197a3ddb", "8fb755eb-b47c-4418-80a2-de4751d3893f", "887718b6-6aa7-4a93-9e37-d1f4ce8a1556", "e6b8f8b6-3252-44ac-9652-8ba7ed8ffddf", "a673659a-794a-4587-8c61-0cd3acc4122f", "e59d25ac-02b1-43fd-927f-14270ffa8f75", "18c154d3-9d2d-4aa5-bc92-3016d01cc0ab", "c1494b4d-a4c7-4421-b28f-d0ceb17faeae", "894ab333-833f-4c29-9be0-7be7977a191a", "d67cbaa4-7705-4397-953f-a4bbb4e3d6b3", "7783bccd-a68d-474b-94db-9c3f8777a5fb", "df2cd864-215d-432d-b0c5-f7ce3eed2a2a", "856f7819-bfd2-4793-a9c1-ebfe38d3390f", "9019f923-f2b3-4770-9019-9829a2165731", "ee56a337-715b-4ee0-8b4d-3059f5fa342b", "c6354eeb-5a26-4b8a-af43-f34c42384416", "10bdf446-43af-4f9b-aa9b-d0ad2a1fadc0", "01e104e1-44b6-41cc-b75d-e10d53ce2561", "898275e8-45e7-4e1a-9be2-4f89cc27b8f9", "830c7d02-7076-4337-89d0-1a1c4e366eb0", "5c727b84-8095-428d-a0a7-9205405e4b10", "31e65505-d8c8-4e93-ac98-62d7637d641f", "44481ba7-0b3e-4e78-8004-409b75204a07", "a26ccf2c-c9d5-49f0-888f-baa1533145b8", "da9750e0-2e3a-40a6-9eb2-8ec1547d302a", "9d71becd-481a-42e6-a27d-5fac48e5dd63", "85dc3cea-eeeb-4c2d-936c-b208431ba112", "c11eb19c-5e16-40bc-88c7-5eb86d13b6c6", "24d78c3a-bff7-47ac-bd64-afdf865608d4", "f3512b69-03f4-46ba-81dd-23efe2a46874", "e4108663-0a74-472b-a34d-ec75118d9553", "871445b8-c872-40ba-b9cb-8cc7c0193311", "86e01001-1f3a-4e18-9e2e-1f366e8e650c"], "blacklistedUserIDs": [], "percentage": 0, "cacheDurationInMinutes": 5, "supportedAppVersion": [{"appOperationSystem": "iOS", "enableAppVersionFilter": true, "supportedVersion": "2.12.9"}, {"appOperationSystem": "adr", "enableAppVersionFilter": true, "supportedVersion": "2.12.9"}]}}], "cfpConfig": {"schedulerConfig": {"renewPolicyCron": "0 12 * * *", "updatePolicyCron": "0 0 * * *", "cancelPolicyCron": "0 0 * * *", "reminderPolicyCron": "0 8 * * *", "retryRenewPolicyCron": "0 5 * * *"}, "renewPolicyRedisConfig": {"key": "RENEW_POLICY_KEY", "lock": "RENEW_POLICY_KEY", "lockDurationInSeconds": 10}, "updatePolicyRedisConfig": {"key": "UPDATE_POLICY_KEY", "lock": "UPDATE_POLICY_KEY", "lockDurationInSeconds": 10}, "reminderPolicyRedisConfig": {"key": "REMINDER_POLICY_KEY", "lock": "REMINDER_POLICY_KEY", "lockDurationInSeconds": 10}, "retryRenewPolicyRedisConfig": {"key": "RETRYRENEW_POLICY_KEY", "lock": "RETRYRENEW_POLICY_KEY", "lockDurationInSeconds": 10}, "cancelPolicyRedisConfig": {"key": "CANCEL_POLICY_KEY", "lock": "CANCEL_POLICY_KEY", "lockDurationInSeconds": 10}, "deeplinks": {"quotationDeeplink": "gxbank://open?screenType=insurance&targetScreen=cyberfraud_quotation", "policyDetailsDeeplink": "gxbank://open?screenType=insurance&targetScreen=cyberfraud_policy_details"}, "featureFlags": {"enableEmailVerificationChecking": true, "enableAppVersionChecking": true, "zurichMaintenance": false, "gxbMaintenance": false}, "maintenanceDetails": {"startTime": "2024-10-17T00:30:00+08:00", "endTime": "2024-10-17T01:00:00+08:00"}, "rateLimitConfig": {"renewalRateLimitIntervalInMs": 500}, "policyConfig": {"periodRange": "1m", "promoPeriod": "1m"}, "zurichConfig": {"baseURL": "https://ap.capi.zurich.com/zmy/apdn/e/partner/v1/api", "username": "{{ ZURICH_USERNAME }}", "password": "{{ ZURICH_PASSWORD }}", "circuitConfig": {"zurich": {"timeoutInMs": 11000}}}, "timeTravelConfig": {"enable": false, "duration": "0d"}}, "carInsConfig": {"promo": {"isActive": true, "startDate": "2025-06-25T00:00:00+08:00", "endDate": "2025-11-01T00:00:00+08:00", "deadLine": "2025-11-01T00:00:00+08:00", "contentPhraseKey": "phrase-key", "isCommissionBasedDiscountActive": false}, "links": {"policyWording": "https://assets.prd.g-bank.app/insurance/ZDriver/Z-Driver_Policy_Wording.pdf", "productDisclosureSheet": "https://assets.prd.g-bank.app/insurance/ZDriver/Z-Driver_PDS.pdf", "helpCenter": "https://prd.helpctr.gxb.com.my/dir-list/1746", "pidmTips": "https://www.pidm.gov.my/pidm2022/media/downloads/Takaful-And-Insurance-Benefits-Protection-System-Brochure.pdf", "pidm": "https://www.pidm.gov.my/en", "tnc": "https://gxbank.my/campaign-tnc", "dutyOfDisclosure": "https://assets.prd.g-bank.app/insurance/ZDriver/ZGIMB_Duty_of_Disclosure.pdf", "pdpa": "https://www.zurich.com.my/en/customer-hub/show-me-more-info/personal-data-protection-notice", "insurancePartnerWebsite": "https://www.zurich.com.my/", "insurancePartnerPortal": "https://myzurichlife.com.my/Landing/login", "landingDeeplink": "gxbank://open?screenType=insurance&targetScreen=car_landing"}, "zurichConfig": {"serviceName": "CarInsZurichClient", "baseURL": "https://api.zurich.com.my/v1/general/insurance/services", "username": "{{ CARINS_ZURICH_USERNAME }}", "password": "{{ CARINS_ZURICH_PASSWORD }}", "enableNewIssuanceEndpoint": false, "circuitBreaker": {"CarInsZurichClient": {"timeout": 13000, "max_concurrent_requests": 100}, "CarInsZurichClient.CalculatePremium": {"timeout": 25000, "max_concurrent_requests": 100}, "CarInsZurichClient.IssueCoverNote": {"timeout": 60000, "max_concurrent_requests": 100}}, "issueCoverNoteMockConfig": {"enable": false, "status": "400 Bad Request", "statusCode": 400, "body": {"meta": {"respCode": "017", "respMessage": "Quotation is declined"}, "data": {"coverNoteInfo": {"coverNoteNo": "D18249-********", "ncdMsg": null, "coverNoteError": null}}}}}, "featureFlags": {"enableFullErrorHandling": true, "enableProfileContactUpdate": true, "enableEVTesting": false, "enableCarInsuranceFeature": true}}, "travelConfig": {"promo": {"isPromoChipActive": true}, "links": {"helpCenter": "https://prd.helpctr.gxb.com.my/dir-list/3960", "pidmTips": "https://www.pidm.gov.my/pidm2022/media/downloads/Takaful-And-Insurance-Benefits-Protection-System-Brochure.pdf", "pidm": "https://www.pidm.gov.my/en", "insurancePartnerPortal": "https://myzurichlife.com.my/Landing/login", "landingDeeplink": "gxbank://open?screenType=insurance&targetScreen=travel_landing", "domesticPolicyWording": "https://assets.prd.g-bank.app/insurance/travel/doc/Travel_Domestic_Lite_Policy_Wording.pdf", "domesticPDS": "https://assets.prd.g-bank.app/insurance/travel/doc/Travel_Domestic_Lite_PDS.pdf", "internationalPolicyWording": "https://assets.prd.g-bank.app/insurance/travel/doc/Travel_International_Lite_Policy_Wording.pdf", "internationalPDS": "https://assets.prd.g-bank.app/insurance/travel/doc/Travel_International_Lite_PDS.pdf", "microSite": "https://web.edge.zurich.com.my/partner/gxbank/travel/quote"}, "assetLinks": {"headerImageUrl": "https://assets.prd.g-bank.app/insurance/travel/img/header.webp", "perks1ImageUrl": "https://assets.prd.g-bank.app/insurance/travel/img/perks-1.webp", "perks2ImageUrl": "https://assets.prd.g-bank.app/insurance/travel/img/perks-2.webp", "perks3ImageUrl": "https://assets.prd.g-bank.app/insurance/travel/img/perks-3.webp", "claimStep1ImageUrl": "https://assets.prd.g-bank.app/insurance/travel/img/claim-step-1.webp", "claimStep2ImageUrl": "https://assets.prd.g-bank.app/insurance/travel/img/claim-step-2.webp", "claimStep3ImageUrl": "https://assets.prd.g-bank.app/insurance/travel/img/claim-step-3.webp"}, "featureFlags": {"enableTravelFeature": true}}}