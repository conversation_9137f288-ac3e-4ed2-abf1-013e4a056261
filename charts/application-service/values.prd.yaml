env: prd
configName: config.prd.json
localeMSConfig: ms.prd.json

gateway:
  enabled: true
  annotations: { }
  hosts:
    - "internal.onboarding.prd.g-bank.app"
  tls: true

replicaCount: 2

envVars:
  #TODO please wait after terraform MR approved
  MYSQL_HOST: application-service-db.onboarding.prd.g-bank.app
  MYSQL_HOST_REPLICA: application-service-db.onboarding.prd.g-bank.app
  SECRET_CONF: /vault/secrets/
  LOCALE_MS: /config_files/ms.json
  
resources:
  limits:
    memory: 512Mi
  requests:
    cpu: 500m
    memory: 512Mi
  
autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 8
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80

deployment:
  enabled: true

podAnnotations:
  #Only db specific vault annotation are specified. rest goes in go-app-lib values
  vault.hashicorp.com/agent-inject-secret-db_creds.json: "database/creds/prd-onboarding-application-service-secret-rds-mysql-dba" 
  vault.hashicorp.com/role: "application-service"
  vault.hashicorp.com/agent-inject-template-db_creds.json: |
    {{ with secret "database/creds/prd-onboarding-application-service-secret-rds-mysql-dba" -}}
      {
        "MYSQL_USERNAME": "{{ .Data.username }}",
        "MYSQL_PASSWORD": "{{ .Data.password }}"
      }
    {{- end }}
  vault.hashicorp.com/agent-inject-secret-redis_creds.json: "kv/data/security/tf/prd/app-service/ec/auth"
  vault.hashicorp.com/agent-inject-template-redis_creds.json: |
    {{ with secret "kv/data/security/tf/prd/app-service/ec/auth" -}}
      {
        "REDIS_PASSWORD": "{{ .Data.auth_token }}"
      }
    {{- end }}

authorizationpolicy:
  enabled: true
  rules:
    - sources: [ "cluster.local/ns/onboarding/sa/customer-experience" ]
      endpoints:
        - methods: [ "POST" ]
          paths: [ "/applications" ]
        - methods: [ "GET", "PUT" ]
          paths: [ "/applications/*", "/product/*" ]
        - methods: [ "PATCH" ]
          paths: [ "/applications/address" ]
    - sources: [ "cluster.local/ns/lending-platform/sa/loan-app" ]
      endpoints:
        - methods: [ "GET" ]
          paths: [ "/applications/*", "/health_check"]
    - sources: [ "cluster.local/ns/onboarding/sa/customer-experience", "cluster.local/ns/operations/sa/customer-portal", "cluster.local/ns/partner-gateway/sa/sentry-partner-t6" ]
      endpoints:
        - methods: [ "GET" ]
          paths: [ "/applications" ]
    - sources: [ "cluster.local/ns/onboarding/sa/customer-experience", "cluster.local/ns/operations/sa/customer-portal" ]
      endpoints:
        - methods: [ "GET" ]
          paths: [ "/health_check" ]
    - sources: [ "cluster.local/ns/onboarding/sa/customer-master" ]
      endpoints:
        - methods: [ "GET" ]
          paths: [ "/applications", "/health_check" ]
    - sources: [ "cluster.local/ns/operations/sa/customer-portal" ]
      endpoints:
        - methods: [ "GET" ]
          paths: [ "/health_check", "/applications/*" ]
    - sources: [ "cluster.local/ns/onboarding/whitelist-service" ]
      endpoints:
        - methods: [ "GET" ]
          paths: [ "/health_check" ]
    - sources: [ "cluster.local/ns/onboarding/sa/whitelist-service" ]
      endpoints:
        - methods: [ "GET" ]
          paths: [ "/health_check" ]
        - methods: [ "POST" ]
          paths: [ "/count/applications" ]
    - sources: [ "cluster.local/ns/identity/sa/id-exp" ]
      endpoints:
        - methods: [ "GET" ]
          paths: [ "/health_check", "/applications", "/applications/*" ]
        - methods: [ "PUT" ]
          paths: [ "/applications/*" ]
    - sources: [ "cluster.local/ns/fintrust/sa/gd-proxy" ]
      endpoints:
        - methods: [ "GET" ]
          paths: [ "/health_check", "/applications" ]
    - sources: [ "cluster.local/ns/onboarding/sa/ekyb-service" ]
      endpoints:
        - methods: [ "GET" ]
          paths: [ "/health_check", "/applications" ]
    - sources: [ "cluster.local/ns/fintrust/sa/risk-broker" ]
      endpoints:
        - methods: [ "GET" ]
          paths: [ "/health_check", "/applications" ]
    - sources: [ "cluster.local/ns/partner/sa/partner-integration-api" ]
      endpoints:
        - methods: [ "GET" ]
          paths: [ "/health_check", "/applications" ]
        - methods: [ "POST" ]
          paths: [ "/applications" ]
        - methods: [ "GET", "PUT" ]
          paths: [ "/applications/*", "/product/*" ]

appgateway:
  enabled: false
  annotations: {}
  hosts:
    - "internal.onboarding.prd.g-bank.app"
  tls: true
