{"name": "user-segmentation", "serviceName": "user-segmentation", "host": "0.0.0.0", "port": 8080, "env": "stg", "ownerInfo": {"name": "digibank", "email": "<EMAIL>", "url": "digibank.myteksi.com"}, "data": {"mysql": {"master": {"dsn": "{{ MYSQL_USERNAME }}:{{ MYSQL_PASSWORD }}@tcp($MYSQL_HOST$:3306)/$DB_NAME$?parseTime=true&loc=UTC", "maxIdle": 2, "maxOpen": 10, "connMaxLifetime": "1800s"}, "slave": {"dsn": "{{ MYSQL_USERNAME }}:{{ MYSQL_PASSWORD }}@tcp($MYSQL_HOST$:3306)/$DB_NAME$?parseTime=true&loc=UTC", "maxIdle": 2, "maxOpen": 10, "connMaxLifetime": "1800s"}, "masterCircuitBreaker": {"timeoutInMs": 1000}, "slaveCircuitBreaker": {"timeoutInMs": 1000}}}, "statsd": {"host": "$DOGSTATSD_HOST$", "port": 8125}, "logger": {"workerCount": 10, "bufferSize": 10000, "logLevel": 1, "stacktraceLevel": 4, "logFormat": "json"}, "trace": {"host": "$DOGSTATSD_HOST$", "port": 8126}, "userSegmentationUpdatesStreamConfig": {"brokers": ["b-1.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "stg-user-segmentation", "clusterType": "critical", "topicName": "stg-user-segmentation-updates", "enableTLS": true, "initOffset": "oldest", "consumerGroupID": "user-segmentation-consumer-group-stg", "dtoName": "UserSegmentationUpdate"}, "odysseySegmentsUpdatesStreamConfig": {"brokers": ["b-1.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "stg-user-segmentation", "clusterType": "critical", "topicName": "stg-odyssey-segment-update", "enableTLS": true}, "onUserSegmentUpdatedStreamConfig": {"brokers": ["b-1.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "stg-user-segmentation", "clusterType": "critical", "topicName": "stg-on-user-segment-updated", "enableTLS": true}, "retrySQSConsumerConfig": {"queueURL": "https://sqs.ap-southeast-1.amazonaws.com/081342228458/stg-loyalty-user-segmentation-retry-queue-sqs", "awsRegion": "ap-southeast-1", "delaySeconds": 0, "waitTimeSeconds": 10, "minimumRetryIntervalSeconds": 5}, "s3EventSQSConsumerConfig": {"queueURL": "https://sqs.ap-southeast-1.amazonaws.com/081342228458/stg-loyalty-user-segmentation-segment-updates-sqs", "awsRegion": "ap-southeast-1", "delaySeconds": 0, "waitTimeSeconds": 10, "minimumRetryIntervalSeconds": 5}, "redisConf": {"addr": "$REDIS_HOST$:6379", "idleTimeoutInSec": 60, "poolSize": 300, "readOnlyFromSlaves": false, "readTimeoutInSec": 1, "writeTimeoutInSec": 1, "password": "{{ REDIS_PASSWORD }}", "tlsEnabled": true, "timeout": 200, "command_name": "redis_UserSegmentation", "command_group": "UserSegmentation", "max_concurrent_requests": 1000, "error_percent_threshold": 20, "sleep_window": 5000}, "apis": {"enumerateUserIDsInGroup": {"pageSizeLimit": 10000}}, "featureFlags": {"enableUserGroupMapUpdate": {"fund_a_pocket_eval_20240320_stg_qa_001": true, "fund_a_pocket_eval_20240320_stg_qa_002": true, "fund_a_pocket_eval_20240320_stg_qa_003": true, "fund_a_pocket_eval_20240320_stg_qa_004": true, "fund_a_pocket_eval_20240320_stg_qa_005": true, "fund_a_pocket_eval_20240320_stg_de_001": true, "fund_a_pocket_eval_20240320_stg_de_002": true, "fund_a_pocket_eval_20240320_stg_de_003": true, "fund_a_pocket_eval_20240320_stg_de_004": true, "fund_a_pocket_eval_20240320_stg_de_005": true, "load_test_metric_20240918_eval": true, "evergreen_segment_all_customer": true}}, "region": {"country": "MY"}, "tridentHelperConfig": {"createMission": {"baseURL": "http://reward-targeting.loyalty.svc.cluster.local", "missionID": "BETA_TEST_1129_6", "requestBody": "{ \"adminEmail\": \"<EMAIL>\", \"name\": \"BETA_TEST_1129_6\", \"startTime\": \"2025-08-18T01:00:00.000Z\", \"endTime\": \"2025-09-30T16:00:00.000Z\", \"owner\": \"TARGETING\", \"timezone\": \"SGT\", \"targetUserType\": \"generic_user\", \"countryID\": {{country_code}}, \"metadata\": { \"missionGoal\": \"BE_FINANCIALLY_SAVVY\", \"missionCategory\": \"Spend\", \"segment_value_not_for_testing\": \"campaign_RWD1738_20240809\", \"segment\": \"QA_GXB_TEST_1\", \"rewardTypes\": \"[\\\"Cashback\\\"]\", \"content\": \"{ \\\"title\\\": \\\"[Beta 2] GX Card Reactivation\\\", \\\"subtitle\\\": \\\"Make two debit card transactions\\\", \\\"iconURL\\\": \\\"https://assets.prd.g-bank.app/loyalty/odyssey/icons/spend_icon.png\\\", \\\"bannerURL\\\": \\\"https://assets.prd.g-bank.app/loyalty/odyssey/banners/spend.png\\\", \\\"actionButton\\\": { \\\"content\\\": \\\"Spend Now\\\", \\\"deeplink\\\": \\\"gxbankstg://open?screenType=cards\\\" }, \\\"extraContent\\\": { \\\"title\\\": \\\"Reward\\\", \\\"description\\\": \\\"RM 5 cashback\\\" }, \\\"detailsListContent\\\": [ { \\\"title\\\": \\\"Reward\\\", \\\"iconURL\\\": \\\"https://assets.prd.g-bank.app/loyalty/odyssey/icons/reward_icon.png\\\", \\\"description\\\": [ \\\"RM 5 cashback\\\" ] }, { \\\"title\\\": \\\"Mission goal\\\", \\\"iconURL\\\": \\\"https://assets.prd.g-bank.app/loyalty/odyssey/icons/mission_goal_icon.png\\\", \\\"description\\\": [ \\\"Spend using GX Card\\\" ] }, { \\\"title\\\": \\\"Mission criteria\\\", \\\"iconURL\\\": \\\"https://assets.prd.g-bank.app/loyalty/odyssey/icons/mission_criteria_icon.png\\\", \\\"description\\\": [ \\\"Debit card transaction worth RM 1 and above\\\" ] } ], \\\"tnc\\\": [], \\\"missionCriteria\\\": { \\\"title\\\": \\\"Mission criteria\\\", \\\"subtitle\\\": \\\"Complete all steps to get that sweet reward.\\\" } }\", \"missionCriteria\": \"{ \\\"taskGroups\\\": [ { \\\"id\\\": 0, \\\"iconURL\\\": \\\"https://assets.prd.g-bank.app/loyalty/odyssey/icons/task_icon.png\\\", \\\"status\\\": \\\"UNLOCKED\\\", \\\"tasksRelationship\\\": \\\"\\\", \\\"tasks\\\": [ { \\\"id\\\": 0, \\\"title\\\": \\\"Debit card transaction worth RM 1 and above\\\", \\\"expectedValue\\\": 2, \\\"valueType\\\": \\\"COUNT\\\", \\\"progressText\\\": \\\"%s/%s\\\", \\\"completionText\\\": \\\"%s/%s 🎉\\\" } ] } ] }\" }, \"nodes\": { \"1\": { \"type\": \"scenarios\", \"children\": [ \"2\", \"5\" ], \"data\": { \"scenarios\": [ { \"eventType\": \"onDigibankDebitCardSpend\", \"condition\": { \"operator\": \"and\", \"conditions\": [ { \"operator\": \"ixn\", \"lhs\": \"var.genericUser.segment\", \"rhs\": [ \"shared.QA_GXB_TEST_1\" ] }, { \"operator\": \"ge\", \"lhs\": \"var.stream.amount\", \"rhs\": 100 }, { \"operator\": \"eq\", \"lhs\": \"var.stream.transactionCodeType\", \"rhs\": \"SPEND_CARD_PRESENT\" } ] } } ], \"meta\": { \"startTime\": \"2025-08-18T01:00:00.000Z\", \"endTime\": \"2025-09-30T16:00:00.000Z\" } } }, \"2\": { \"type\": \"count\", \"children\": [ \"3\" ], \"data\": { \"targets\": [ \"occurrence-1\" ] } }, \"3\": { \"type\": \"countCondition\", \"children\": [ \"4\" ], \"data\": { \"lhs\": \"1\", \"operator\": \"eq\", \"rhs\": 2 } }, \"4\": { \"type\": \"actions\", \"data\": [ { \"type\": \"digibankAwardFixedCashbackV2\", \"meta\": { \"genericParams\": { \"amount\": 500, \"currency\": \"MYR\", \"productType\": \"ODYSSEY\", \"rewardType\": \"ODYSSEY_CASHBACK\", \"domain\": \"DEPOSITS\", \"subdomain\": \"ODYSSEY\", \"missionID\": \"BETA_TEST_1129_6\", \"sourceAccount\": \"*********\" } }, \"id\": 1 }, { \"type\": \"digibankFixedCashbackNotificationV1\", \"meta\": { \"genericParams\": { \"templateID\": \"loyalty_push_odyssey_reward_notification\", \"title\": \"You have earned a reward on Odyssey!\", \"message\": \"Sweet! You have earned a reward on your completed mission!\", \"deeplink\": \"gxbankstg://open?screenType=Odyssey\" } }, \"id\": 2 } ], \"limits\": [ { \"per\": \"user\", \"limit\": 1 }, { \"per\": \"totalDaily\", \"limit\": 0 }, { \"per\": \"total\", \"limit\": 0 }, { \"per\": \"userDaily\", \"limit\": 0 } ] }, \"5\": { \"type\": \"actions\", \"data\": [ { \"type\": \"digibankUpdateTaskProgress\", \"meta\": { \"genericParams\": { \"value\": 1, \"taskID\": 0, \"taskGroupID\": 0, \"missionID\": \"BETA_TEST_1129_6\" } }, \"id\": 1 } ], \"limits\": [ { \"per\": \"user\", \"limit\": 2 }, { \"per\": \"totalDaily\", \"limit\": 0 }, { \"per\": \"total\", \"limit\": 0 }, { \"per\": \"userDaily\", \"limit\": 0 } ] } } }"}, "updateMissionsStatus": {"attemptCount": 1, "campaignID": "0af79bca48654d0bb1f2f6b007e197bc", "status": 2}}}