{"name": "loan-exp Service", "serviceName": "loan-exp", "host": "0.0.0.0", "port": 8080, "env": "prod", "ownerInfo": {"name": "digibank", "email": "<EMAIL>", "url": "digibank.myteksi.com"}, "data": {"mysql": {"master": {"dsn": "{{MASTER_USERNAME}}:{{MASTER_PASSWORD}}@tcp($MYSQL_HOST$:3306)/$DB_NAME$?parseTime=true&loc=UTC", "maxIdle": 25, "maxOpen": 50, "connMaxLifetime": "1800s"}, "slave": {"dsn": "{{REPLICA_USERNAME}}:{{REPLICA_PASSWORD}}@tcp($MYSQL_HOST_REPLICA$:3306)/$DB_NAME$?parseTime=true&loc=UTC", "maxIdle": 25, "maxOpen": 50, "connMaxLifetime": "1800s"}, "masterCircuitBreaker": {"timeoutInMs": 1000}, "slaveCircuitBreaker": {"timeoutInMs": 1000}}}, "statsd": {"host": "$DOGSTATSD_HOST$", "port": 8125}, "trace": {"host": "$DOGSTATSD_HOST$", "port": 8126}, "logger": {"workerCount": 10, "bufferSize": 10000, "logLevel": 1, "stacktraceLevel": 4, "logFormat": "json", "development": false}, "productMasterConfig": {"baseURL": "http://product-master.core-banking.svc.cluster.local", "serviceName": "product-master", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": true}, "customerMasterConfig": {"baseURL": "http://customer-master.onboarding.svc.cluster.local", "serviceName": "customer-master", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": true}, "accountServiceConfig": {"baseURL": "http://account-service.core-banking.svc.cluster.local", "serviceName": "account-service", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": true}, "loanCoreConfig": {"baseURL": "http://loan-core.lending-platform.svc.cluster.local", "serviceName": "loan-core", "circuitBreaker": {"timeout": 10000, "max_concurrent_requests": 2000, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": true}, "partnerPayEngineConfig": {"baseURL": "http://partnerpay-engine.payments.svc.cluster.local", "serviceName": "partnerpay-engine", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": true}, "pairingServiceConfig": {"baseURL": "http://pairing-service.payments.svc.cluster.local", "serviceName": "pairing-service", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": false}, "transactionStatementConfig": {"baseURL": "http://transaction-statements.core-banking.svc.cluster.local", "serviceName": "transaction-statements", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": true}, "digicardCoreConfig": {"baseURL": "http://digicard-core.core-banking.svc.cluster.local", "serviceName": "digicard-core", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": false}, "loanAccountLifecycleKafkaConfig": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "loan-exp-client-prd", "clusterType": "critical", "enableTLL": true, "stream": "prd-loan-account-lifecycle-event", "packageName": "pb", "dtoName": "LoanAccountLifecycle", "offsetType": "oldest", "maxRetryCount": 5, "delayInMilliSeconds": 200}, "loanCoreLOCKafkaConfig": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "loan-exp-client-prd", "clusterType": "critical", "enableTLL": true, "stream": "prd-loan-core-loc-account", "packageName": "pb", "dtoName": "LoanCoreLoc", "offsetType": "oldest", "maxRetryCount": 5, "delayInMilliSeconds": 200}, "loanExpTxKafkaConfig": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "stream": "prd-loan-exp-tx", "clusterType": "critical", "enableTLL": true, "offsetType": "oldest", "clientID": "loan-exp-client-prd", "packageName": "pb", "dtoName": "LoanExpTx", "maxRetryCount": 5, "delayInMilliSeconds": 200, "syncprod": true, "requiredAcks": -1}, "assetCampaignDetailKafkaConfig": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "loan-exp-client-prd", "clusterType": "critical", "enableTLL": true, "stream": "prd-asset-campaign-detail-event", "packageName": "pb", "dtoName": "AssetCampaignDetail", "offsetType": "oldest", "enable": false, "maxRetryCount": 5, "delayInMilliSeconds": 200}, "assetParameterDetailKafkaConfig": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "loan-exp-client-prd", "clusterType": "critical", "enableTLL": true, "stream": "prd-asset-parameter-detail-event", "packageName": "pb", "offsetType": "oldest", "dtoName": "AssetParameterDetail", "enable": false, "maxRetryCount": 5, "delayInMilliSeconds": 200}, "assetOfflineLimitReviewKafkaConfig": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "loan-exp-client-prd", "clusterType": "critical", "enableTLL": true, "stream": "prd-bilr-lms-action-tx", "packageName": "pb", "offsetType": "oldest", "dtoName": "AssetOfflineLimitReview", "enable": false, "maxRetryCount": 5, "delayInMilliSeconds": 200}, "assetOfflineUpdateEventKafkaConfig": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "loan-exp-client-prd", "clusterType": "critical", "enableTLL": true, "stream": "prd-bilr-lms-action-response-tx", "packageName": "pb", "offsetType": "oldest", "dtoName": "AssetOfflineUpdateEvent", "syncprod": true, "requiredAcks": -1}, "loanAutoRepaymentKafkaConfig": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "loan-exp-client-prd", "clusterType": "critical", "enableTLL": true, "stream": "prd-auto-payment-event", "packageName": "pb", "offsetType": "oldest", "dtoName": "AutoPaymentEvent", "enable": true, "maxRetryCount": 5, "delayInMilliSeconds": 200, "requiredAcks": -1}, "creditArrivalOnHoldAccount": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "loan-exp-client-prd", "clusterType": "critical", "enableTLL": true, "stream": "prd-hold-account-transaction-event", "packageName": "pb", "offsetType": "oldest", "dtoName": "DepositsBalanceEvent", "enable": false}, "generateInvoiceKafkaConfig": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "loan-exp-client-prd", "clusterType": "critical", "enableTLL": true, "stream": "prd-generate-invoice-event", "packageName": "pb", "offsetType": "oldest", "dtoName": "GenerateInvoiceEvent", "enable": true, "maxRetryCount": 5, "delayInMilliSeconds": 200}, "creditCardEventConsumerKafkaConfig": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "loan-exp-client-prd", "clusterType": "critical", "enableTLL": true, "stream": "prd-credit-card-account-lifecycle-event", "packageName": "pb", "dtoName": "CreditCardAccountLifecycle", "offsetType": "oldest", "maxRetryCount": 1, "delayInMilliSeconds": 200, "enable": false}, "opsLendingActionStatusEventKafkaConfig": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "loan-exp-client-prd", "clusterType": "critical", "enableTLL": true, "stream": "prd-ops-lending-action-status-event", "packageName": "pb", "dtoName": "OpsLendingActionStatusEvent", "offsetType": "oldest", "maxRetryCount": 10, "delayInMilliSeconds": 200, "enable": false}, "opsLendingActionKafkaConfig": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "loan-exp-client-prd", "clusterType": "critical", "enableTLL": true, "stream": "prd-ops-lending-action-event", "packageName": "pb", "dtoName": "OpsLendingActionStatusEvent", "offsetType": "oldest", "maxRetryCount": 10, "delayInMilliSeconds": 200, "enable": false}, "redisConfig": {"addr": "clustercfg.dbmy-prd-lending-ec-loan-exp.oyhkgx.apse1.cache.amazonaws.com:6379", "idleTimeoutInSec": 1, "poolSize": 300, "readOnlyFromSlaves": false, "readTimeoutInSec": 1, "writeTimeoutInSec": 1, "password": "{{REDIS_PASSWORD}}", "tlsEnabled": true}, "workflowRetryConfig": {"drawdown": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 250}, "auxiliary": {"intervalInSeconds": 30, "maxAttempt": 5}}, "repayment": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 250}, "auxiliary": {"intervalInSeconds": 30, "maxAttempt": 5}}, "acceleration": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 25}, "auxiliary": {"intervalInSeconds": 30, "maxAttempt": 5}}, "autoRepayment": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5}, "auxiliary": {"intervalInSeconds": 30, "maxAttempt": 2}}, "fullSettlement": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5}, "auxiliary": {"intervalInSeconds": 30, "maxAttempt": 2}}, "waiveoff": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 25}, "auxiliary": {"intervalInSeconds": 30, "maxAttempt": 5}}, "writeoff": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 25}, "auxiliary": {"intervalInSeconds": 30, "maxAttempt": 5}}, "reviselimit": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 25}}, "assetCampaign": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 25}, "auxiliary": {"intervalInSeconds": 60, "maxAttempt": 30}}, "autoAccelerationAndWriteOffOrchestrator": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 25}}, "assetParameter": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 10}, "auxiliary": {"intervalInSeconds": 30, "maxAttempt": 2}}, "downgrade": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 12}, "auxiliary": {"intervalInSeconds": 30, "maxAttempt": 5}}, "creditCardRestrictionChange": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 10}, "auxiliary": {"intervalInSeconds": 30, "maxAttempt": 6}}, "creditCardInstruction": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5}, "auxiliary": {"intervalInSeconds": 30, "maxAttempt": 2}}, "assetAccountConversion": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 10}, "auxiliary": {"intervalInSeconds": 30, "maxAttempt": 6}}, "creditCardOffboardingPolicy": {"auxiliary": {"intervalInSeconds": 30, "maxAttempt": 5}, "transactional": {"intervalInSeconds": 30, "maxAttempt": 25}}, "creditInsuranceClaimPolicy": {"auxiliary": {"intervalInSeconds": 30, "maxAttempt": 5}, "transactional": {"intervalInSeconds": 30, "maxAttempt": 25}}, "creditCardAutomatedAccelerationAndWriteOffPolicy": {"auxiliary": {"intervalInSeconds": 30, "maxAttempt": 5}, "transactional": {"intervalInSeconds": 30, "maxAttempt": 25}}, "fixedTermLoanDrawdown": {"auxiliary": {"intervalInSeconds": 30, "maxAttempt": 5}, "transactional": {"intervalInSeconds": 30, "maxAttempt": 25}}, "assetAccountTenureAdjustment": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5}, "auxiliary": {"intervalInSeconds": 30, "maxAttempt": 3}}, "assetAccountBatchConversion": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5}, "auxiliary": {"intervalInSeconds": 30, "maxAttempt": 3}}}, "hedwig": {"clientConfig": {"hostAddress": "http://hedwig.backend-rtc-app-01.svc.cluster.local", "pushServerPath": "/hedwig/v1/push", "pushInboxServerPath": "/hedwig/v1/pushInbox", "emailServerPath": "/hedwig/v1/email", "circuitConfig": {"hedwig": {"timeoutInMs": 15}}, "serviceName": "", "serviceKey": ""}, "pushNotificationTemplateMappings": {"MY": {"successfulDrawdownPushNotificationTemplate": "********-9fc0-49ea-9cde-033c9540597b", "unsuccessfulDrawdownPushNotificationTemplate": "014c3120-ef19-4c0b-9939-7b1eca278e2e", "successfulRepaymentPushNotificationTemplate": "e036398e-f989-4d14-88d6-96247f8a5bdd", "successfulRepaymentWithSavingsPushNotificationTemplate": "0f4da346-c86c-404b-895e-9ca5ccacf952", "unsuccessfulRepaymentPushNotificationTemplate": "e64f4b73-0684-43da-9432-2fa0026eeca4", "repaymentInstructionTimeoutPushNotificationTemplate": "a36627e0-65c2-4584-8ba6-7ec31be23d1e", "successfulCreditLimitReductionPushNotificationTemplate": "29b79c57-3787-449d-9e3b-c514d91b56c8", "successfulCreditLimitReductionByOPSPushNotificationTemplate": "16eff356-0f20-4b66-bb32-ee6d86f20301", "locDrawdownBlockedPushNotificationTemplate": "d1a9723b-6326-46c1-8d50-4d78533177f6", "locDrawdownUnblockedPushNotificationTemplate": "9ec65277-8ddb-4774-b979-f6ae9ac44625"}}, "emailNotificationTemplateMappings": {}, "smsNotificationTemplateMappings": {}}, "notificationTemplateConfig": {"emailNotificationTemplateMapping": {"firstSuccessfulDrawdownEmailNotificationTemplate": {"id": "first_flexi_loan_drawdown_successful_email", "client": "", "serverPath": ""}, "successfulDrawdownEmailNotificationTemplate": {"id": "flexi_loan_drawdown_successful_email", "client": "", "serverPath": ""}, "unsuccessfulDrawdownEmailNotificationTemplate": {"id": "flexi_loan_drawdown_unsuccessful_email", "client": "", "serverPath": ""}, "successfulRepaymentEmailNotificationTemplate": {"id": "flexi_loan_repayment_successful_email", "client": "", "serverPath": ""}, "splitLoanSuccessfulDrawdownEmailNotificationTemplate": {"id": "flexi_loan_akpk_drawdown_successful_email", "client": "", "serverPath": ""}, "successfulAutoRepaymentEmailNotificationTemplate": {"id": "auto_debit_successful_full_payment_email", "client": "", "serverPath": ""}, "partialSuccessfulAutoRepaymentEmailNotificationTemplate": {"id": "auto_debit_emi_partial_success_email", "client": "", "serverPath": ""}, "unSuccessfulAutoDebitEmailNotificationTemplate": {"id": "auto_debit_unsuccessful_on_overdue_email", "client": "", "serverPath": ""}, "unsuccessfulRepaymentEmailNotificationTemplate": {"id": "flexi_loan_repayment_unsuccessful_email"}, "successfulRepaymentWithSavingsEmailNotificationTemplate": {"id": "manual_repayment_successful_full_payment_email"}, "zeroBalanceAutoRepaymentEmailNotificationTemplate": {"id": "auto_debit_unsuccessful_on_overdue_email"}, "successfulBizRepaymentEmailNotificationTemplate": {"client": "", "id": "biz_flexi_loan_repayment_successful_email"}, "unsuccessfulBizRepaymentEmailNotificationTemplate": {"client": "", "id": "biz_flexi_loan_repayment_unsuccessful_email"}, "successfulBizRepaymentWithSavingsEmailNotificationTemplate": {"client": "", "id": "biz_flexi_loan_repayment_successful_saving_email"}, "successfulBizDrawdownEmailNotificationTemplate": {"id": "biz_flexi_loan_drawdown_success_email", "client": ""}, "bizInterestRateUpdateEmailNotificationTemplate": {"id": "biz_flexi_loan_drawdown_update_interest_rate_email"}, "unsuccessfulBizDrawdownEmailNotificationTemplate": {"id": "biz_flexi_loan_drawdown_unsuccess_email", "client": ""}, "bizSuccessfulAutoRepaymentEmailNotificationTemplate": {"id": "biz_auto_debit_successful_full_payment_email"}, "bizInternalFailureAutoRepaymentEmailNotificationTemplate": {"id": "biz_auto_debit_internal_failure_email"}, "bizPartialSuccessfulAutoRepaymentEmailNotificationTemplate": {"id": "biz_auto_debit_partial_success_email"}, "bizZeroBalanceAutoRepaymentEmailNotificationTemplate": {"id": "biz_auto_debit_unsuccessful_insufficient_email"}, "bizUnSuccessfulAutoDebitEmailNotificationTemplate": {"id": "biz_auto_debit_unsuccessful_insufficient_email"}}, "pushNotificationTemplateMapping": {"firstSuccessfulDrawdownPushNotificationTemplate": {"id": "first_flexi_loan_drawdown_successful_push", "client": "", "serverPath": ""}, "successfulDrawdownPushNotificationTemplate": {"id": "flexi_loan_drawdown_successful_push", "client": "", "serverPath": ""}, "unsuccessfulDrawdownPushNotificationTemplate": {"id": "flexi_loan_drawdown_unsuccessful_push", "client": "", "serverPath": ""}, "successfulRepaymentPushNotificationTemplate": {"id": "flexi_loan_repayment_successful_push", "client": "", "serverPath": ""}, "splitLoanSuccessfulDrawdownPushNotificationTemplate": {"id": "flexi_loan_akpk_drawdown_successful_push", "client": "", "serverPath": ""}, "successfulAutoRepaymentPushNotificationTemplate": {"id": "auto_debit_successful_full_payment_push", "client": "", "serverPath": ""}, "internalFailureAutoRepaymentPushNotificationTemplate": {"id": "auto_debit_emi_internal_failure_push", "client": "", "serverPath": ""}, "partialSuccessfulAutoRepaymentPushNotificationTemplate": {"id": "auto_debit_emi_partial_success_push", "client": "", "serverPath": ""}, "unSuccessfulAutoDebitPushNotificationTemplate": {"id": "auto_debit_unsuccessful_on_overdue_push", "client": "", "serverPath": ""}, "unsuccessfulRepaymentPushNotificationTemplate": {"id": "flexi_loan_repayment_unsuccessful_push"}, "successfulRepaymentWithSavingsPushNotificationTemplate": {"id": "manual_repayment_successful_full_payment_push"}, "zeroBalanceAutoRepaymentPushNotificationTemplate": {"id": "auto_debit_unsuccessful_on_overdue_push"}, "successfulBizRepaymentPushNotificationTemplate": {"id": "biz_flexi_loan_repayment_successful_push"}, "unsuccessfulBizRepaymentPushNotificationTemplate": {"id": "biz_flexi_loan_repayment_unsuccessful_push"}, "successfulBizRepaymentWithSavingsPushNotificationTemplate": {"id": "biz_flexi_loan_repayment_successful_saving_push"}, "successfulBizDrawdownPushNotificationTemplate": {"id": "biz_flexi_loan_drawdown_success_push"}, "bizInterestRateUpdatePushNotificationTemplate": {"id": "biz_flexi_loan_drawdown_update_interest_rate_push"}, "unsuccessfulBizDrawdownPushNotificationTemplate": {"id": "biz_flexi_loan_drawdown_unsuccess_push"}, "bizSuccessfulAutoRepaymentPushNotificationTemplate": {"id": "biz_auto_debit_successful_full_payment_push"}, "bizInternalFailureAutoRepaymentPushNotificationTemplate": {"id": "biz_auto_debit_internal_failure_push"}, "bizPartialSuccessfulAutoRepaymentPushNotificationTemplate": {"id": "biz_auto_debit_partial_success_push"}, "bizZeroBalanceAutoRepaymentPushNotificationTemplate": {"id": "biz_auto_debit_unsuccessful_insufficient_push"}, "bizUnSuccessfulAutoDebitPushNotificationTemplate": {"id": "biz_auto_debit_unsuccessful_insufficient_push"}}, "smsNotificationTemplateMapping": {}}, "paymentEngineConfig": {"baseURL": "http://payment-engine.payments.svc.cluster.local", "serviceName": "payment-engine", "circuitBreaker": {"timeout": 10000, "max_concurrent_requests": 100, "request_volume_threshold": 100, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": false}, "appian": {"clientConfig": {"hostAddress": "https://dbmy.gforce.g-bank.app", "clientID": "loan-exp", "circuitConfig": {"appian": {"timeout": 10000}}, "registeredClientID": "{{APPIAN_CLIENT_ID}}", "registeredClientSecret": "{{APPIAN_CLIENT_SECRET}}", "grantType": "client_credentials"}}, "pigeonConfig": {"baseURL": "http://pigeon.pigeon.svc.cluster.local", "emailNotificationTemplateMappings": {"MY": {"firstSuccessfulDrawdownEmailNotificationTemplate": "first_flexi_loan_drawdown_successful_email", "successfulDrawdownEmailNotificationTemplate": "flexi_loan_drawdown_successful_email", "unsuccessfulDrawdownEmailNotificationTemplate": "flexi_loan_drawdown_unsuccessful_email"}}}, "dynamicConstants": {"defaultTimeZoneOffset": 28800, "defaultCurrencyCode": "MYR", "defaultCountryCode": "MY", "defaultRoundPrecision": 2, "defaultNotificationLanguage": "ms"}, "localeConfig": {"defaultCurrencyCode": "MYR", "defaultCountryCode": "MY", "defaultNotificationLanguage": "en", "defaultPushNotificationLanguage": "en", "defaultRoundPrecision": 2, "defaultTimeFormat": "15:04", "defaultTimeZoneOffset": 28800, "minAllowedPaymentAmount": 10}, "featureFlags": {"enableCache": true, "enableCacheOnListDefaultParameters": true, "enableLimitRevisionByOPS": true, "enableFirstDrawdownNotification": true, "enablePartialAutoRepayment": true, "enableAssetAccountConversion": true, "enableBizFlexiCredit": true, "enableBIFForNotification": true, "enableBizDrawdownInvoice": true, "enableBizLendingOtherBank": false, "enableManualPartialRepayment": true, "enableActiveProfile": true}, "cacheTimeoutConfig": {"listDefaultParametersTTLInMinutes": 30}, "internalAccounts": {"AKPKRnRConversion": "*********"}, "expireLoanRepayments": {"delayTimeDurationInSeconds": 300, "fetchRecordsLimit": 100, "startTimeRangeInDays": 7}, "customerExperienceConfig": {"serviceName": "customer-experience", "baseURL": "http://customer-experience.onboarding.svc.cluster.local", "circuitBreaker": {"timeout": 3000, "max_concurrent_requests": 20, "request_volume_threshold": 20, "sleep_window": 6000, "ignoredHTTPCode": [400, 401, 402, 403, 404]}, "withHealthCheck": true}}