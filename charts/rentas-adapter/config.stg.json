{"name": "rentas-adapter", "serviceName": "rentas-adapter", "env": "stg", "host": "0.0.0.0", "port": 8080, "data": {"mysql": {"master": {"dsn": "{{ MYSQL_USERNAME }}:{{ MYSQL_PASSWORD }}@tcp($MYSQL_HOST$:3306)/$DB_NAME$?parseTime=true&loc=UTC", "maxIdle": 25, "maxOpen": 50, "connMaxLifetime": "1800s"}, "slave": {"dsn": "{{ MYSQL_USERNAME }}:{{ MYSQL_PASSWORD }}@tcp($MYSQL_HOST_REPLICA$:3306)/$DB_NAME$?parseTime=true&loc=UTC", "maxIdle": 25, "maxOpen": 50, "connMaxLifetime": "1800s"}, "masterCircuitBreaker": {"timeoutInMs": 1000}, "slaveCircuitBreaker": {"timeoutInMs": 1000}}}, "statsd": {"host": "$DOGSTATSD_HOST$", "port": 8125}, "trace": {"host": "$DOGSTATSD_HOST$", "port": 8126}, "logger": {"workerCount": 10, "bufferSize": 10000, "logLevel": 1, "stacktraceLevel": 4, "logFormat": "json"}, "redisCluster": {"addr": "clustercfg.dbmy-stg-payments-ec-payments.m7ivtf.apse1.cache.amazonaws.com:6379", "idleTimeoutInSec": 1, "poolSize": 300, "readOnlyFromSlaves": false, "readTimeoutInSec": 1, "writeTimeoutInSec": 1, "password": "{{ REDIS_PASSWORD }}", "tlsEnabled": true}, "featureFlags": {"disabledCreditTransfer": false, "disabledInterbankTransfer": false}, "partnerStream": {"brokers": ["b-1.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "topicName": "stg-payment-engine-tx", "clusterType": "critical", "enableTLS": true, "initOffset": "oldest", "clientID": "rentas-adapter-stg"}, "workflowRetryConfig": {"wf_ct_cashin": {"auxiliary": {"intervalInSeconds": 10, "maxAttempt": 5}}}, "s3Config": {"finops": {"bucketName": "stg-swift-autoclient-s3", "pathPrefix": "Payment/emission/"}}, "sqsConfig": {"url": "https://sqs.ap-southeast-1.amazonaws.com/************/stg-payments-rentas-adapter-queuehandler-sqs", "waitTimeInSec": 10}, "partner": {"enabled": true, "pairingService": {"baseURL": "http://pairing-service.payments.svc.cluster.local"}, "paymentEngine": {"baseURL": "http://payment-engine.payments.svc.cluster.local"}, "partnerpayEngine": {"baseURL": "http://partnerpay-engine.payments.svc.cluster.local"}, "paymentCore": {"baseURL": "https://payment-core.payments.svc.cluster.local"}}, "sqs": {"s3bucketeventhandler": {"consumerName": "s3bucketevent", "queueURL": "https://sqs.ap-southeast-1.amazonaws.com/************/stg-payments-rentas-adapter-s3event", "waitTimeSeconds": 10, "exponentialBackoffBaseIntervalSeconds": 10, "workerCount": 1, "maxNumberOfMessages": 1, "defaultVisibilityTimeoutSeconds": 60}, "queuehandler": {"consumerName": "queuehandler", "queueURL": "https://sqs.ap-southeast-1.amazonaws.com/************/stg-payments-rentas-adapter-queuehandler-sqs", "waitTimeSeconds": 10, "exponentialBackoffBaseIntervalSeconds": 10, "workerCount": 1, "maxNumberOfMessages": 1, "defaultVisibilityTimeoutSeconds": 60}}, "bankInfo": {"identifierCode": "GXSPMYKL", "swiftCode": "GXSPMYKLXXX", "rentasService": "bnm.rtgs!pu", "rentasMainGL": "*********", "rentasPlusGL": "*********"}, "interbankTransactionCode": {"domain": "DEPOSITS", "sweepInTransactionType": "SWEEP_IN", "sweepOutTransactionType": "SWEEP_OUT", "subType": "RENTAS+"}, "slack": {"webhookURLs": {"paymentNotification": "{{ PAYMENT_NOTIFICATION_WEBHOOK_URL }}"}}, "slackEntity": {"rentasTransactionFailed": "<!subteam^S079ZNF6WP4>"}}