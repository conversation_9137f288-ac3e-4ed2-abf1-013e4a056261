env: dev
serviceAccount:
  create: true
  name: tm-image-copy
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks/cluster/infra-01/tm/tm-image-copy
    # update this annotation to trigger image copy job to run, any value different from previous will do the trick
    tm.cen.g-bank.app/force-image-copy: "2025-09-05T00:39:52Z"
pod:
  disabled: true
resources:
  requests:
    cpu: 100m
    memory: 100Mi
  limits:
    memory: 512Mi
job:
  disabled: false
  containers:
    tm_image_copy:
      image:
        repository: ************.dkr.ecr.ap-southeast-1.amazonaws.com/build/awscli-tool
        tag: v0.0.3
# We use secret manager instead of vault secret for image registry token
#  podAnnotations:
#    vault.hashicorp.com/agent-inject: 'true'
#    vault.hashicorp.com/agent-init-first: "true" # required to skip istio
#    vault.hashicorp.com/agent-pre-populate-only: 'true'
#    vault.hashicorp.com/agent-inject-secret-token: 'kv/tm/tm_registry_token'
#    vault.hashicorp.com/role: "tm-image-copy"
#    vault.hashicorp.com/agent-inject-template-token: |
#      {{ with secret "kv/tm/tm_registry_token" -}}
#        export TM_REGISTRY_TOKEN="{{ .Data.token }}"
#      {{- end }}
tmVaultVersion: 4.6.57