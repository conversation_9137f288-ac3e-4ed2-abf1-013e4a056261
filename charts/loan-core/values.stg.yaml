env: stg
configName: config.stg.json

serviceAccount:
  create: true
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks/cluster/stg-lending-01/loan-core
  name: loan-core

envVars:
  MYSQL_HOST: loan-core-db.lending.stg.g-bank.app
  MYSQL_HOST_REPLICA: loan-core-db.lending.stg.g-bank.app
  DB_NAME: loan_core
  SECRET_CONF: /vault/secrets/
  AWS_ROLE_ARN: arn:aws:iam::************:role/eks/cluster/stg-lending-01/loan-core
  AWS_WEB_IDENTITY_TOKEN_FILE: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
  AWS_REGION: ap-southeast-1

#  MOCK_DISABLE_PENDING_ACTIVATION: "\"true\""
#  USE_MOCK: "\"true\""

image:
  probeInitialDelay: 120

replicaCount: 2

gateway:
  enabled: true
  annotations: { }
  hosts:
    - "internal.lending.stg.g-bank.app"
  tls: true

deployment:
  enabled: true

resources:
  limits:
    memory: 256Mi
  requests:
    cpu: 250m
    memory: 256Mi

autoscaling:
  enabled: true
  minReplicas: 4
  maxReplicas: 8
  targetCPUUtilizationPercentage: 70

podAnnotations:
  cluster-autoscaler.kubernetes.io/safe-to-evict: "true"
  vault.hashicorp.com/role: "loan-core"
  vault.hashicorp.com/agent-inject-secret-db_creds.json: "database/creds/stg-lending-loan-core-rds-mysql-dba"
  vault.hashicorp.com/agent-inject-template-db_creds.json: |
    {{ with secret "database/creds/stg-lending-loan-core-rds-mysql-dba" -}}
      {
        "MASTER_USERNAME": "{{ .Data.username }}",
        "MASTER_PASSWORD": "{{ .Data.password }}",

        "REPLICA_USERNAME": "{{ .Data.username }}",
        "REPLICA_PASSWORD": "{{ .Data.password }}"
      }
    {{- end }}
  vault.hashicorp.com/agent-inject-secret-db-schema-migration: "database/creds/stg-lending-loan-core-rds-mysql-dba"
  vault.hashicorp.com/agent-inject-template-db-schema-migration: |
    {{ with secret "database/creds/stg-lending-loan-core-rds-mysql-dba" -}}
      export mysqluser="{{ .Data.username }}"
      export mysqlpass="{{ .Data.password }}"
    {{- end }}
  vault.hashicorp.com/agent-inject-secret-static_creds_tm_token.json: "kv2/data/lending-platform/app/loan-core"
  vault.hashicorp.com/agent-inject-template-static_creds_tm_token.json: |
    {{ with secret "kv2/data/lending-platform/app/loan-core" -}}
      {
        "TM_TOKEN": "{{ .Data.data.tm_token }}"
      }
    {{- end }}
  vault.hashicorp.com/agent-inject-secret-redis_creds.json: "kv/data/security/tf/stg/loan-core/ec/auth"
  vault.hashicorp.com/agent-inject-template-redis_creds.json: |
    {{ with secret "kv/data/security/tf/stg/loan-core/ec/auth" -}}
      {
        "REDIS_PASSWORD": "{{ .Data.auth_token }}"
      }
    {{- end }}

authorizationpolicy:
  enabled: true
  rules:
    #    the following istio-ingress source is only for dev/staging env, it should not be copied to prod
    - sources: [ "cluster.local/ns/istio-ingress/sa/istio-ingressgateway-service-account" ]
    - sources: ["cluster.local/ns/lending-platform/sa/loan-exp"]
      endpoints:
        - methods: [ "POST" ]
          paths: [ "/v1/flexi-term-loans/create", "/v1/flexi-term-loans/summary", "/v1/customer-insights",
                   "/v1/flexi-term-loans/repayment-validation", "/v1/flexi-loc/revise-limit", "/v1/flexi-term-loans/interest-savings",
                   "/v1/account-details", "/v1/flexi-loc/parameters", "/v1/flexi-loc/hold-codes", "/v1/flexi-term-loans/drawdown-validation",
                   "/v1/flexi-term-loans/accelerate", "/v1/flexi-term-loans/write-off", "/v1/flexi-term-loans/waive-off", "/v1/flexi-term-loans/detail",
                   "/v1/flexi-loc/parameters", "/v1/asset-accounts/validate-conversion"]
        - methods: [ "PUT" ]
          paths: [ "/v1/flexi-loc/parameters" ]
        - methods: [ "GET" ]
          paths: [ "/v1/flexi-loc/hold-codes/*", "/health_check" ]
    - sources: ["cluster.local/ns/payments/sa/payment-core"]
      endpoints:
        - methods: ["POST"]
          paths: ["/v1/transfers", "/v1/transfers/authorisations", "/v1/transfers/authorisations/releases",
                  "/v1/transfers/authorisations/settlements", "/v1/transfers/reversals"]
        - methods: [ "GET" ]
          paths: [ "/health_check" ]
    - sources: [ "cluster.local/ns/payments/sa/payment-engine" ]
      endpoints:
        - methods: [ "POST" ]
          paths: [ "/v1/transfers/validation" ]
        - methods: [ "GET" ]
          paths: [ "/health_check" ]
    - sources: [ "cluster.local/ns/core-banking/sa/account-service" ]
      endpoints:
        - methods: [ "POST" ]
          paths: [ "/v1/flexi-loc/create",
                   "/v1/flexi-loc/close" ]
        - methods: [ "GET" ]
          paths: [ "/health_check" ]
    - sources: [ "cluster.local/ns/core-banking/sa/risk-service" ]
      endpoints:
        - methods: [ "POST" ]
          paths: [ "/v1/flexi-loc/parameters"]
        - methods: [ "GET" ]
          paths: [ "/health_check" ]
    - sources: [ "cluster.local/ns/fintrust/sa/gd-proxy" ]
      endpoints:
        - methods: [ "GET" ]
          paths: [ "/health_check" ]
        - methods: [ "POST" ]
          paths: [ "/v1/flexi-loc/parameters" ]
    - sources: [ "cluster.local/ns/fintrust/sa/risk-broker" ]
      endpoints:
        - methods: [ "GET" ]
          paths: [ "/health_check" ]
        - methods: [ "POST" ]
          paths: [ "/v1/flexi-loc/parameters" ]
    - sources: [ "cluster.local/ns/operations/sa/payment-ops-trf" ]
      endpoints:
        - methods: [ "POST" ]
          paths: [ "/v1/transfers", "/v1/internal/transfers" ]
        - methods: [ "GET" ]
          paths: [ "/health_check" ]
    - sources: [ "cluster.local/ns/operations/sa/bulk-operation" ]
      endpoints:
        - methods: [ "GET" ]
          paths: [ "/health_check" ]
        - methods: [ "POST" ]
          paths: [ "/v1/flexi-loc/parameters" ]
