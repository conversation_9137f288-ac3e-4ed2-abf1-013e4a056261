{"name": "loan-core Service", "serviceName": "loan-core", "host": "0.0.0.0", "port": 8080, "env": "prod", "ownerInfo": {"name": "digibank", "email": "<EMAIL>", "url": "digibank.myteksi.com"}, "data": {"mysql": {"master": {"dsn": "{{ MASTER_USERNAME }}:{{ MASTER_PASSWORD }}@tcp($MYSQL_HOST$:3306)/loan_core?parseTime=true&loc=UTC", "maxIdle": 25, "maxOpen": 10000, "connMaxLifetime": "1800s"}, "slave": {"dsn": "{{ REPLICA_USERNAME }}:{{ REPLICA_PASSWORD }}@tcp($MYSQL_HOST$:3306)/loan_core?parseTime=true&loc=UTC", "maxIdle": 25, "maxOpen": 10000, "connMaxLifetime": "1800s"}, "masterCircuitBreaker": {"timeoutInMs": 1000, "maxConcurrentReq": 10000, "maxQueueSize": 10000}, "slaveCircuitBreaker": {"timeoutInMs": 1000, "maxConcurrentReq": 10000, "maxQueueSize": 10000}}}, "statsd": {"host": "$DOGSTATSD_HOST$", "port": 8125}, "trace": {"host": "$DOGSTATSD_HOST$", "port": 8126}, "logger": {"syslogTag": "structuredlog.loan-core", "workerCount": 10, "bufferSize": 10000, "logLevel": 1, "stacktraceLevel": 4, "logFormat": "json", "development": false}, "thoughtMachineConfig": {"thoughtMachineURL": "https://core-api.tm.prd.g-bank.app", "thoughtMachineToken": "{{TM_TOKEN}}", "accountCreatedEventResponseTopic": "vault.core_api.v1.accounts.account.events", "planCreatedEventResponseTopic": "vault.core_api.v1.plans.plan.events", "planUpdatedEventResponseTopic": "vault.core_api.v1.plans.account_plan_assoc.events", "planClosureEventResponseTopic": "vault.core_api.v1.plans.plan_update.events", "postingClientResponseTopic": "integration.sdkclient.loan_core", "postingInstructionBatchCreateEventResponseTopic": "vault.api.v1.postings.posting_instruction_batch.created", "thoughtMachineKafkaURL": ["b-1.dbmyprdtmmskclust.welduw.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.dbmyprdtmmskclust.welduw.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.dbmyprdtmmskclust.welduw.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "kafkaConsumerGroupID": "prd-thought-machine-loan-core", "requestTimeoutMillis": 100000, "initialIntervalMillis": 10, "maxIntervalMillis": 100000, "attemptCount": 5, "supervisorContractVersionID": "fa408f74-d069-400c-94d9-0f1c8ac9f785", "supervisorContractVersionIDs": {"flexiLoanLineOfCredit": "fa408f74-d069-400c-94d9-0f1c8ac9f785", "bizFlexiCreditLineOfCredit": "12055f83-4a13-4695-b688-61119579e4f6"}, "smartContractVersionIDs": {"flexiLoanLineOfCreditContractVersionID": "139", "flexiLoanTermLoansContractVersionID": "142", "bizFlexiCreditLineOfCreditContractVersionID": "91", "bizFlexiCreditTermLoansContractVersionID": "97"}, "dpdFlag": {"count": 16, "definitionID": "DPD_15", "description": "Determines if additional penalty interest should be given since DPD >= 16"}, "accelerationFlag": {"definitionID": "ACCELERATION_FLAG", "description": "Name of the flag to be applied to accelerate the loan"}, "tenorInterestAccelerationFlag": {"definitionID": "TENOR_INTEREST_ACCELERATION_FLAG", "description": "Name of the flag to be applied to accelerate the upcoming due tenor of loan"}, "defaultYearInScheduleTimeExpression": 2096, "repaymentDayScheduleTimeExpression": "0 21 16 * * * *", "supervisorRepaymentDayScheduleName": "SUPERVISOR_REPAYMENT_DAY_SCHEDULE", "accountPlanAssociationListAPIPageSize": "100", "kafkaRetryConfig": {"maxRetryCount": 5, "delayInMilliSeconds": 100}, "supervisorLastScheduleOfDayName": "FLEXILOAN_SUPERVISOR_DAILY_SCHEDULE", "supervisorLastScheduleOfDayTimeExpression": "0 1 16 * * * *", "planClosureEventRetryConfig": {"topic": "vault.core_api.v1.plans.plan_update.events", "enabled": false, "producerMaxRetryCount": 5, "producerDelayInMilliSeconds": 100}}, "loanCoreLOCAccountKafkaConfig": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "loan-core-client-prd", "clusterType": "critical", "enableTLL": true, "stream": "prd-loan-core-loc-account", "packageName": "pb", "dtoName": "LoanCoreLoc", "offsetType": "oldest", "syncprod": true, "requiredAcks": -1}, "loanAccountLifecycleEventKafkaConfig": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "loan-core-client-prd", "clusterType": "critical", "enableTLL": true, "stream": "prd-loan-account-lifecycle-event", "packageName": "pb", "dtoName": "LoanAccountLifecycle", "offsetType": "oldest", "delayInMilliSeconds": 200, "maxRetryCount": 5, "syncprod": true, "requiredAcks": -1}, "loanCoreTxKafkaConfig": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "loan-core-client-prd", "clusterType": "critical", "enableTLL": true, "stream": "prd-loan-core-tx", "packageName": "pb", "dtoName": "LoanCoreTx", "offsetType": "oldest", "syncprod": true, "requiredAcks": -1}, "paymentEngineTxKafkaConfig": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "loan-core-client-prd", "clusterType": "critical", "enableTLL": true, "stream": "prd-payment-engine-tx", "packageName": "pb", "offsetType": "oldest", "dtoName": "PaymentEngineTx", "maxRetryCount": 5, "delayInMilliSeconds": 200}, "loanCoreTxWithRetryKafkaConfig": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "loan-core-client-prd", "clusterType": "critical", "enableTLL": true, "stream": "prd-loan-core-tx-retry", "packageName": "pb", "dtoName": "LoanCoreTx", "offsetType": "oldest", "maxRetryCount": 5, "delayInMilliSeconds": 200, "syncprod": true, "requiredAcks": -1}, "loanAccountDPDComputeEventKafkaConfig": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "loan-core-client-prd", "clusterType": "critical", "enableTLL": true, "stream": "prd-loan-account-dpd-compute-event", "packageName": "pb", "dtoName": "LoanAccountDpdComputeEvent", "offsetType": "oldest", "maxRetryCount": 5, "delayInMilliSeconds": 200, "syncprod": true, "requiredAcks": -1}, "overdraftAccountCreationEventKafkaConfig": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "loan-core-client-prd", "clusterType": "critical", "enableTLL": true, "stream": "prd-overdraft-account-creation-event", "packageName": "pb", "dtoName": "OverdraftAccountEvent", "offsetType": "oldest", "syncprod": true, "requiredAcks": -1}, "loanCoreAccountCreationKafkaConfig": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "loan-core-client-prd", "clusterType": "critical", "enableTLL": true, "stream": "prd-loan-core-account-creation-event", "packageName": "pb", "dtoName": "LoanCoreAccountCreation", "offsetType": "oldest", "maxRetryCount": 5, "delayInMilliSeconds": 200, "syncprod": true, "requiredAcks": -1}, "overdraftTxEventKafkaConfig": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "loan-core-client-prd", "clusterType": "critical", "enableTLL": true, "stream": "prd-overdraft-tx-event", "packageName": "pb", "dtoName": "OverdraftTxEvent", "offsetType": "oldest", "enable": false, "maxRetryCount": 5, "delayInMilliSeconds": 200}, "incidentalOverdraftAccountDPDComputeEventKafkaConfig": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "loan-core-client-prd", "clusterType": "critical", "enableTLL": true, "stream": "prd-iod-account-dpd-compute-event", "packageName": "pb", "dtoName": "OverdraftAccountDpdComputeEvent", "offsetType": "oldest", "enable": false, "delayInMilliSeconds": 200, "maxRetryCount": 5, "syncprod": true, "requiredAcks": -1}, "creditCardLifecycleEventKafkaConfig": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "loan-core-client-prd", "clusterType": "critical", "enableTLL": true, "stream": "prd-credit-card-account-lifecycle-event", "packageName": "pb", "dtoName": "CreditCardAccountLifecycle", "offsetType": "oldest", "syncprod": true, "requiredAcks": -1}, "AutoPaymentEventKafkaConfig": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "loan-core-client-prd", "clusterType": "critical", "enableTLL": true, "stream": "prd-auto-payment-event", "packageName": "pb", "offsetType": "oldest", "dtoName": "AutoPaymentEvent", "enable": true, "maxRetryCount": 5, "delayInMilliSeconds": 200, "syncprod": true, "requiredAcks": -1}, "incidentalOverdraftAccountWriteOffEventKafkaConfig": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "loan-core-client-prd", "clusterType": "critical", "enableTLL": true, "stream": "prd-overdraft-account-write-off-event", "packageName": "pb", "dtoName": "OverdraftAccountWriteOffEvent", "offsetType": "oldest", "enable": false, "maxRetryCount": 5, "delayInMilliSeconds": 200, "syncprod": true, "requiredAcks": -1}, "rlocLifecycleEventKafkaConfig": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "loan-core-client-prd", "clusterType": "critical", "enableTLL": true, "stream": "prd-rloc-account-lifecycle-event", "packageName": "pb", "dtoName": "RLOCAccountLifecycle", "offsetType": "oldest", "syncprod": true, "requiredAcks": -1, "enable": false}, "bizTermLoanAccountDPDComputeEventKafkaConfig": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "loan-core-client-prd", "clusterType": "critical", "enableTLL": true, "stream": "prd-biz-term-loan-dpd-compute-event", "packageName": "pb", "dtoName": "LoanAccountDpdComputeEvent", "offsetType": "oldest", "maxRetryCount": 5, "delayInMilliSeconds": 200, "syncprod": true, "requiredAcks": -1, "enable": true}, "creditCardAccountDPDComputeEventKafkaConfig": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "loan-core-client-prd", "clusterType": "critical", "enableTLL": true, "stream": "prd-credit-card-dpd-compute-event", "packageName": "pb", "dtoName": "CreditCardAccountDpdComputeEvent", "offsetType": "oldest", "maxRetryCount": 5, "delayInMilliSeconds": 200, "syncprod": true, "requiredAcks": -1}, "localeConfig": {"defaultCurrencyCode": "MYR", "defaultCountryCode": "MY", "defaultTimeZoneOffset": 28800, "defaultRoundPrecision": 2, "defaultTimeZone": "Asia/Kuala_Lumpur", "defaultTimeFormat": "15:04"}, "dynamicConstants": {"defaultCurrencyCode": "MYR", "defaultCountryCode": "MY", "defaultTimeZoneOffset": 28800, "loanDrawdownDisableDPDCount": 15, "loanDrawdownInDefiniteBlockDPDCount": 60, "dynamicHardCapOnRepaymentInPercentage": 100, "loanInstallmentDPDCountHardCap": 30, "loanCoreTxRetryCount": 10, "listPlanScheduleLoopCount": 1000, "defaultTimeZone": "Asia/Kuala_Lumpur", "expireLoanDrawdowns": {"delayTimeDurationInSeconds": 300, "fetchRecordsLimit": 100}, "defaultPageSizeDPDJob": 5000, "dpdJobAssetAccountsRetryCount": 5, "defaultTimeFormat": "15:04", "defaultRoundPrecision": 2, "loanPastDueAccountingVersion": "2", "loanPastDueCollectibilityVersion": "1", "loanAutoRepaymentWorkerConfig": {"pageSize": 100}}, "accountServiceConfig": {"baseURL": "http://account-service.core-banking.svc.cluster.local", "serviceName": "account-service", "circuitBreaker": {"timeout": 5000, "max_concurrent_requests": 50}, "withHealthCheck": true}, "productMasterConfig": {"baseURL": "http://product-master.core-banking.svc.cluster.local", "serviceName": "product-master", "circuitBreaker": {"timeout": 5000, "max_concurrent_requests": 50}, "withHealthCheck": true}, "loanExpConfig": {"baseURL": "http://loan-exp.lending-platform.svc.cluster.local", "serviceName": "loan-exp", "circuitBreaker": {"timeout": 5000, "max_concurrent_requests": 50}, "withHealthCheck": true}, "partnerpayEngineConfig": {"baseURL": "http://partnerpay-engine.payments.svc.cluster.local", "serviceName": "partnerpay-engine", "circuitBreaker": {"timeout": 5000, "max_concurrent_requests": 50}, "withHealthCheck": true}, "internalAccounts": {"loanUndrawnCommitment": "*********", "loanUndrawnCommitmentContra": "*********", "badDebtRecoveryAccount": "*********", "opsLossRecoveryAccount": "*********"}, "redisConfig": {"addr": "clustercfg.dbmy-prd-lending-ec-loan-core.oyhkgx.apse1.cache.amazonaws.com:6379", "idleTimeoutInSec": 1, "poolSize": 300, "readOnlyFromSlaves": false, "readTimeoutInSec": 1, "writeTimeoutInSec": 1, "password": "{{ REDIS_PASSWORD }}", "tlsEnabled": true}, "workflowRetryConfig": {"createFlexiLoanAccount": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 43}, "auxiliary": {"intervalInSeconds": 300, "maxAttempt": 12}}, "createFlexiLOCAccount": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 43}}, "closeFlexiLoanAccount": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 25}}, "closeFlexiLOCAccount": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 25}}, "reviseLimit": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 25}}, "schedulerEvents": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 15}}, "accelerateFlexiTermLoanAccount": {"transactional": {"intervalInSeconds": 60, "maxAttempt": 25}, "auxiliary": {"intervalInSeconds": 600, "maxAttempt": 25}, "tmJobScheduler": {"intervalInSeconds": 600, "maxAttempt": 30}}, "waiveOffFlexiLoanAccounts": {"transactional": {"intervalInSeconds": 60, "maxAttempt": 25}}, "writeOffFlexiTermLoanAccount": {"transactional": {"intervalInSeconds": 60, "maxAttempt": 25}, "auxiliary": {"intervalInSeconds": 600, "maxAttempt": 25}}, "repaymentLoanAccounts": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 25}}, "updateLOCAccountParameters": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 25}}, "createOverdraftAccount": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 25}}, "overdraftTxEvent": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 25}}, "delayedTransactions": {"auxiliary": {"intervalInSeconds": 1200, "maxAttempt": 20}, "transactional": {"intervalInSeconds": 30, "maxAttempt": 25}}, "downgradeTransactionRetryPolicy": {"auxiliary": {"intervalInSeconds": 300, "maxAttempt": 26}, "transactional": {"intervalInSeconds": 60, "maxAttempt": 26}, "tmJobScheduler": {"intervalInSeconds": 600, "maxAttempt": 30}}, "createCreditCardAccount": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 40}, "auxiliary": {"intervalInSeconds": 300, "maxAttempt": 10}}, "balanceTransferParametersRetryPolicy": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 25}}, "cardSpendSettlementTx": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5}, "auxiliary": {"intervalInSeconds": 300, "maxAttempt": 15}}, "cardRepaymentSettlementTx": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5}, "auxiliary": {"intervalInSeconds": 300, "maxAttempt": 15}}, "creditCardClosure": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5}}, "creditCardAccelerationRetryPolicy": {"auxiliary": {"intervalInSeconds": 300, "maxAttempt": 15}, "transactional": {"intervalInSeconds": 30, "maxAttempt": 5}}, "creditCardWriteOffRetryPolicy": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5}, "auxiliary": {"intervalInSeconds": 180, "maxAttempt": 15}}, "creditCardWaiveOffRetryPolicy": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5}}, "createOverarchingAccountRetryPolicy": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5}}, "createFixedTermLoanAccount": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5}}, "assetAccountTenureAdjustmentRetryPolicy": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 25}, "auxiliary": {"intervalInSeconds": 180, "maxAttempt": 15}}, "updateOverarchingAccountRetryPolicy": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 25}, "auxiliary": {"intervalInSeconds": 180, "maxAttempt": 15}}, "updateRLOCAccountStatusRetryPolicy": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5}, "auxiliary": {"intervalInSeconds": 300, "maxAttempt": 15}}, "rlocSpendSettlementTx": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5}, "auxiliary": {"intervalInSeconds": 300, "maxAttempt": 15}}, "rlocRepaymentSettlementTx": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5}, "auxiliary": {"intervalInSeconds": 300, "maxAttempt": 15}}, "rlocContractNotificationEventRetryPolicy": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5}}, "rlocUpdateAccountBalanceRetryPolicy": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5}, "auxiliary": {"intervalInSeconds": 300, "maxAttempt": 15}}}, "featureFlags": {"repaymentsTesting": true, "enableLoanCoreTxWithRetryPublisher": true, "holdCodeNewImplementation": true, "disableDrawdownValidationFromExistingCreateAPI": false, "validatePlanSchedule": false, "enableDelayedTransactions": true, "enablePublishingMessageForBlockUnblockByOPS": true, "accountDetailsForClosedLoan": true, "accountDetailsWithDrawdownDisabledReasons": true, "enableLoanAccountLifeCycleStreamOnDPDCheckPoint": true, "enableAutoRepaymentToggle": true, "enableLoanUpgradeAfterDowngrade": true, "enablePenalInterestUpdateViaTmSplitMap": false, "enableBizFlexiCredit": true, "enableTMPostingStreamClient": true, "enableAutoAccelerationAndWriteOffPenaltyScheduleJobCheck": true, "enableTMCheckForPenalInterest": false, "enableBalanceTransferConversion": true, "enableBizLendingOtherBank": false, "enableBlockDrawdownWindow": true, "enableFlexiLineOfCreditIncreaseLimit": true, "enableRetryableStream": false}, "allowedLendingTMSchedulers": {"SUPERVISOR_REPAYMENT_DAY_SCHEDULE": true, "SUPERVISOR_ACCRUE_INTEREST_SCHEDULE": true, "SUPERVISOR_GRACE_PERIOD_END_SCHEDULE": true, "SUPERVISOR_PENALTY_INTEREST_SCHEDULE": true, "LABYRINTH_SUPERVISOR_SCHEDULE_AST": true, "FLEXICREDIT_SUPERVISOR_DAILY_SCHEDULE": true, "LABYRINTH_SUPERVISOR_FLEXI_CREDIT_DAILY_SCHEDULE_AST": true}, "countryCodeToDelayedTransactionsMapping": {"MY": [{"transactionType": "REPAYMENT", "blackOutHoursConfig": [{"unit": "daily", "start": "00:00", "end": "03:00"}]}, {"transactionType": "DRAWDOWN", "blackOutHoursConfig": [{"unit": "daily", "start": "23:55", "end": "00:01"}]}]}}