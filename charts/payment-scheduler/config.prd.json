{"name": "payment-scheduler", "serviceName": "payment-scheduler", "env": "prd", "host": "0.0.0.0", "port": 8080, "data": {"mysql": {"master": {"dsn": "{{ MYSQL_USERNAME }}:{{ MYSQL_PASSWORD }}@tcp($MYSQL_HOST$:3306)/$DB_NAME$?parseTime=true&loc=UTC", "maxIdle": 25, "maxOpen": 50, "connMaxLifetime": "1800s"}, "slave": {"dsn": "{{ MYSQL_USERNAME }}:{{ MYSQL_PASSWORD }}@tcp($MYSQL_HOST_REPLICA$:3306)/$DB_NAME$?parseTime=true&loc=UTC", "maxIdle": 25, "maxOpen": 50, "connMaxLifetime": "1800s"}, "masterCircuitBreaker": {"timeoutInMs": 1000}, "slaveCircuitBreaker": {"timeoutInMs": 1000}}}, "locale": {"timezone": "Asia/Kuala_Lumpur", "homeCountry": "MY"}, "cronManager": {"schedulerJob": {"disabled": false, "cronExpression": "0 23 * * *", "lockDuration": 500}, "notificationJob": {"disabled": false, "cronExpression": "0 1 * * *", "lockDuration": 500}}, "notificationConfig": {"remindNDaysBefore": 1}, "redisCluster": {"addr": "clustercfg.dbmy-prd-payments-ec-payment-scheduler.90skwd.apse1.cache.amazonaws.com:6379", "idleTimeoutInSec": 1, "poolSize": 300, "readOnlyFromSlaves": false, "readTimeoutInSec": 1, "writeTimeoutInSec": 1, "password": "{{ REDIS_PASSWORD }}", "tlsEnabled": true}, "apiHandler": {"syncExponentialBackOffCount": 5, "syncExponentialBackOffMs": 500, "transactionDelayInSec": 3}, "workflowRetryConfig": {"wf_schedule_job": {"transactional": {"intervalInSeconds": 3, "maxAttempt": 5}}}, "schedulerConfig": {"maxYear": 20, "maxFutureGeneratedTasks": 5}, "statsd": {"host": "$DOGSTATSD_HOST$", "port": 8125}, "trace": {"host": "$DOGSTATSD_HOST$", "port": 8126}, "logger": {"workerCount": 10, "bufferSize": 10000, "logLevel": 1, "stacktraceLevel": 4, "logFormat": "json"}, "pigeon": {"baseURL": "http://pigeon.pigeon.svc.cluster.local", "apiVersion": "v2"}, "customerMaster": {"baseURL": "http://customer-master.onboarding.svc.cluster.local", "groupName": "dbmy"}, "pairingService": {"baseURL": "http://pairing-service.payments.svc.cluster.local"}, "accountService": {"baseURL": "http://account-service.core-banking.svc.cluster.local", "groupName": "dbmy"}, "accountDetailStream": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "payment-scheduler-prd", "clusterType": "critical", "topicName": "prd-deposits-account-detail-event", "enableTLS": true, "initOffset": "oldest", "enable": true}, "customerJournalStream": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "payment-scheduler-journal-prd", "clusterType": "critical", "topicName": "prd-audit-log", "enableTLS": true, "initOffset": "oldest", "enable": false}, "sqs": {"queueURL": "https://sqs.ap-southeast-1.amazonaws.com/************/prd-payments-payment-scheduler-sqs20240227091642623500000003", "awsRegion": "ap-southeast-1", "exponentialBackoffBaseIntervalSeconds": 10, "defaultVisibilityTimeoutSeconds": 60, "waitTimeSeconds": 5, "workerCount": 2, "maxNumberOfMessages": 1, "maxProducerMessageBatchSize": 100, "producerMessageBufferTimeSeconds": 1}}