{"name": "referral-engine Service", "serviceName": "referral-engine", "env": "dev", "host": "0.0.0.0", "port": 8080, "ownerInfo": {"name": "digibank", "email": "<EMAIL>", "url": "digibank.myteksi.com"}, "data": {"mysql": {"master": {"dsn": "{{ MYSQL_USERNAME }}:{{ MYSQL_PASSWORD }}@tcp($MYSQL_HOST$:3306)/$DB_NAME$?parseTime=true&loc=UTC", "maxIdle": 2, "maxOpen": 10, "connMaxLifetime": "1800s"}, "slave": {"dsn": "{{ MYSQL_USERNAME }}:{{ MYSQL_PASSWORD }}@tcp($MYSQL_HOST$:3306)/$DB_NAME$?parseTime=true&loc=UTC", "maxIdle": 2, "maxOpen": 10, "connMaxLifetime": "1800s"}, "masterCircuitBreaker": {"timeoutInMs": 1000}, "slaveCircuitBreaker": {"timeoutInMs": 1000}}}, "statsd": {"host": "$DOGSTATSD_HOST$", "port": 8125}, "trace": {"host": "$DOGSTATSD_HOST$", "port": 8126}, "logger": {"workerCount": 10, "bufferSize": 10000, "logLevel": 0, "stacktraceLevel": 4, "logFormat": "json"}, "codeGeneratorConf": {"maxAttempts": 100, "maxFormatChanges": 10}, "serviceFeatureFlags": {"enableGXSRetailReferrals": false, "enableBizReonboardCheck": true, "enableReferralScreenFlag": true, "disablePublishReferralCreatedStream": false}, "customerMasterClientConf": {"serviceName": "customer-master", "group": "dbmy", "baseURL": "http://customer-master.onboarding.svc.cluster.local"}, "streamConsumers": [{"key": "loanAppLifecycleEventStream", "brokers": ["b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "referral-engine-client-dev", "clusterType": "critical", "topicName": "dev-loan-app-lifecycle-event", "enableTLS": true, "initOffset": "oldest", "consumerGroupID": "loyalty-loan-app-lifecycle-tx-consumer-group-dev", "dtoName": "LoanAppLifecycleEvent", "disabled": false}, {"key": "dbmyReferralEventStream", "brokers": ["b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "referral-engine-client-dev", "clusterType": "critical", "topicName": "dev-referral-event", "enableTLS": true, "initOffset": "oldest", "consumerGroupID": "loyalty-referral-tx-consumer-group-dev", "dtoName": "ReferralEvent", "disabled": false}], "referralCreatedStream": {"key": "referralCreatedStream", "brokers": ["b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "referral-engine-client-dev", "clusterType": "critical", "topicName": "dev-referral-created", "enableTLS": true, "initOffset": "oldest", "consumerGroupID": "loyalty-referral-created-tx-consumer-group-dev", "dtoName": "ReferralCreated"}, "rulesEngineConf": {"enabled": true, "businessRules": {"promoCodeRules": [{"campaignID": 4, "campaignName": "TEST campaign for promo code", "codeRules": {"format": "XXXXXX", "placeholders": {"X": "ABCDEFGHIJKLMNOPQRSTUVYZ0123456789"}, "prefix": "PROMO", "suffix": "", "excludeCharacters": [], "campaignStartAfterTimestamp": "0001-01-01T00:00:00Z", "expiryAtTimestamp": "0001-01-01T00:00:00Z", "redemptionCap": 5, "codeGenerateQuantity": 20, "productChannels": ["GXB_BIZ_ONBOARDING", "GXB_RETAIL_ONBOARDING"], "maxUsesPerCampaign": "0", "maxUsesPerUser": "0", "maxUsesPerProfile": "1"}, "metadata": {}}, {"campaignID": 6, "campaignName": "Campaign for expired promo code", "codeRules": {"format": "XXXX", "placeholders": {"X": "ABCDEFGHIJKLMNOPQRSTUVYZ0123456789"}, "prefix": "PROMO", "suffix": "", "excludeCharacters": [], "campaignStartAfterTimestamp": "0001-01-01T00:00:00Z", "expiryAtTimestamp": "2025-05-21T04:00:00Z", "redemptionCap": 5, "codeGenerateQuantity": 3, "productChannels": ["GXB_BIZ_ONBOARDING"], "maxUsesPerCampaign": "0", "maxUsesPerUser": "0", "maxUsesPerProfile": "0"}, "metadata": {}}, {"campaignID": 1, "campaignName": "Campaign for OTA promo code GXB biz onboarding", "codeRules": {"format": "", "placeholders": {}, "prefix": "", "suffix": "", "excludeCharacters": [], "__commentExpiryAt": "TBD on actual expiry time", "campaignStartAfterTimestamp": "0001-01-01T00:00:00Z", "expiryAtTimestamp": "2026-01-01T00:00:00Z", "redemptionCap": 1, "codeGenerateQuantity": 0, "productChannels": ["GXB_BIZ_ONBOARDING"], "maxUsesPerCampaign": "1", "maxUsesPerUser": "0", "maxUsesPerProfile": "1"}, "metadata": {}}, {"campaignID": 2, "campaignName": "Campaign for lapasar promo code", "codeRules": {"format": "lapasarNNNN", "placeholders": {"N": "0123456789"}, "prefix": "", "suffix": "", "excludeCharacters": [], "campaignStartAfterTimestamp": "2025-08-20T15:59:59Z", "expiryAtTimestamp": "0001-01-01T00:00:00Z", "redemptionCap": 1, "codeGenerateQuantity": 1000, "productChannels": ["GXB_BIZ_ONBOARDING"], "maxUsesPerCampaign": "1", "maxUsesPerUser": "1", "maxUsesPerProfile": "1"}, "metadata": {}}], "referralCodeRules": [{"__commentCampaignRules": "TBD on actual productChannels, maxUsesPerCampaign, maxUsesPerUser and maxUsesPerProfile", "campaignID": 1, "campaignName": "Campaign for retail referral", "codeRules": {"format": "YYY", "placeholders": {"Y": "0123456789"}, "prefix": "format.UserNRICNameWith4Char", "suffix": "", "excludeCharacters": [], "campaignStartAfterTimestamp": "0001-01-01T00:00:00Z", "expiryAtTimestamp": "0001-01-01T00:00:00Z", "redemptionCap": 0, "codeGenerateQuantity": 1, "productChannels": ["GXB_BIZ_ONBOARDING", "GXB_RETAIL_ONBOARDING", "GXB_RETAIL_FLEXICREDIT_ACTIVATION"], "maxUsesPerCampaign": "0", "maxUsesPerUser": "0", "maxUsesPerProfile": "0"}, "metadata": {}, "createdAt": "2025-07-11T03:16:00Z", "createdBy": "SYSTEM", "inactiveAt": "0001-01-01T00:00:00Z"}]}}, "tridentCampaignIDMap": {"flexiCreditActivated": "1e85779a-963b-4566-bfd2-feb0b98ee6fa", "NTBRetailOnboarded": "1e85779a-963b-4566-bfd2-feb0b98ee6fa"}}