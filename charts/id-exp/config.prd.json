{"name": "Identity Experience Service", "serviceName": "id-exp", "host": "0.0.0.0", "port": 8080, "statsd": {"host": "$DOGSTATSD_HOST$", "port": 8125}, "data": {"mysql": {"master": {"dsn": "{{ MYSQL_USERNAME }}:{{ MYSQL_PASSWORD }}@tcp($MYSQL_HOST$:3306)/id_exp?parseTime=true&loc=UTC", "maxIdle": 25, "maxOpen": 50}, "slave": {"dsn": "{{ MYSQL_USERNAME }}:{{ MYSQL_PASSWORD }}@tcp($MYSQL_HOST_REPLICA$:3306)/id_exp?parseTime=true&loc=UTC", "maxIdle": 25, "maxOpen": 50}, "masterCircuitBreaker": {"timeoutInMs": 1000, "maxConcurrentReq": 1000}, "slaveCircuitBreaker": {"timeoutInMs": 1000, "maxConcurrentReq": 1000}}}, "dataAdditionalConfig": {"datetimePrecision": 6}, "logger": {"workerCount": 10, "bufferSize": 10000, "logLevel": 1, "stacktraceLevel": 4, "logFormat": "json"}, "trace": {"host": "$DOGSTATSD_HOST$", "port": 8126}, "redisConfig": {"addr": "clustercfg.dbmy-prd-identity-ec-id-exp.o0rkvr.apse1.cache.amazonaws.com:6379", "idleTimeoutInSec": 1, "poolSize": 300, "readOnlyFromSlaves": false, "readTimeoutInSec": 1, "writeTimeoutInSec": 1, "password": "{{ REDIS_PASSWORD }}", "tlsEnabled": true}, "timeoutInMs": 100000, "localTimeZone": "Asia/Kuala_Lumpur", "pigeon": {"serviceName": "pigeon", "baseURL": "http://pigeon.pigeon.svc.cluster.local", "circuitBreaker": {"timeout": 100000}}, "grabIDConfig": {"hostAddress": "http://grab-id.identity.svc.cluster.local", "circuitConfig": {"timeout": 10}, "serviceID": "ID_EXP", "serviceKey": "{{ grab_id_service_key }}", "digibankServiceID": "DIGIBANK", "digibankServiceKey": "qSdYEvKfmroJNrHOA7HZVp8VgcjzEli9zNGi36MqvBssj7XIr1IQOm0-MUhGcUnT4KIKi5xvGjxJWexISrFvtA"}, "ekycService": {"serviceName": "ekyc-service", "baseURL": "http://ekyc-service.onboarding.svc.cluster.local", "circuitBreaker": {"timeout": 10000}}, "gdProxy": {"serviceName": "gd-proxy", "baseURL": "http://gd-proxy.fintrust.svc.cluster.local", "circuitBreaker": {"timeout": 2000}}, "riskBroker": {"serviceName": "risk-broker", "baseURL": "http://risk-broker.fintrust.svc.cluster.local", "circuitBreaker": {"timeout": 20000}}, "accountService": {"serviceName": "account-service", "baseURL": "http://account-service.core-banking.svc.cluster.local", "circuitBreaker": {"timeout": 3000}}, "customerExperience": {"serviceName": "customer-experience", "baseURL": "http://customer-experience.onboarding.svc.cluster.local", "circuitBreaker": {"timeout": 3000}}, "customerMaster": {"serviceName": "customer-master", "baseURL": "http://customer-master.onboarding.svc.cluster.local", "circuitBreaker": {"timeout": 3000, "error_percent_threshold": 100}}, "euronetAdapter": {"serviceName": "euronet-adapter", "baseURL": "http://euronet-adapter.cards.svc.cluster.local", "circuitBreaker": {"timeout": 3000}}, "applicationService": {"serviceName": "application-service", "baseURL": "http://application-service.onboarding.svc.cluster.local", "circuitBreaker": {"timeout": 10000}}, "loanExperience": {"serviceName": "loan-experience", "baseURL": "http://loan-exp.lending-platform.svc.cluster.local", "circuitBreaker": {"timeout": 3000}}, "gidLoginStream": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "idexp-prd", "clusterType": "critical", "topicName": "gid-login-stream", "enableTLS": true, "initOffset": "oldest"}, "expiredAppStream": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "idexp-prd", "clusterType": "critical", "topicName": "prd-application-expired", "enableTLS": true, "initOffset": "oldest"}, "customerClosingStream": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "idexp-prd", "clusterType": "critical", "topicName": "prd-customer-closing", "enableTLS": true, "initOffset": "oldest"}, "accountServiceDormantAccountEventStream": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "idexp-prd", "clusterType": "critical", "topicName": "prd-account-service-dormant-account-event", "enableTLS": true, "initOffset": "oldest", "dtoName": "AccountServiceDormantAccountEvent"}, "applicationStatusTransitionStream": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "idexp-prd", "clusterType": "critical", "topicName": "prd-application-status-update-transition", "enableTLS": true, "initOffset": "oldest", "dtoName": "ApplicationStatusTransition"}, "bizCraStream": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "idexp-prd", "clusterType": "critical", "topicName": "prd-biz-cra", "enableTLS": true, "initOffset": "oldest", "dtoName": "BusinessCustomerRiskAssessment"}, "businessStatusStream": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "idexp-prd", "clusterType": "critical", "topicName": "prd-business-status", "enableTLS": true, "initOffset": "oldest", "dtoName": "BusinessStatus"}, "auditLogStream": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "streamID": "prd_audit_log", "clientID": "idexp-prd", "clusterType": "critical", "enableTLS": true, "topicName": "prd-audit-log", "initOffset": "oldest", "dtoName": "AuditLog"}, "inAppAuthTxStream": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "streamID": "prd_in_app_auth_tx", "clientID": "idexp-prd", "clusterType": "critical", "enableTLS": true, "topicName": "prd-in-app-auth-tx", "initOffset": "oldest", "dtoName": "InAppAuthTx"}, "transactionLimitTxStream": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "idexp-prd", "clusterType": "critical", "topicName": "prd-transaction-limit-tx", "enableTLS": true, "initOffset": "oldest", "dtoName": "TransactionLimitTx"}, "depositsAccountDetailStream": {"brokers": ["b-1.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.7zv5uz.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "idexp-prd", "clusterType": "critical", "topicName": "prd-deposits-account-detail-event", "enableTLS": true, "initOffset": "oldest", "dtoName": "AccountDetail"}, "retryStream": {"queueURL": "https://sqs.ap-southeast-1.amazonaws.com/************/prd-identity-id-exp-sqs-retryable-stream", "awsRegion": "ap-southeast-1", "exponentialBackoffBaseIntervalSeconds": 0, "exponentialBackoffGrowthRateSeconds": 1, "waitTimeSeconds": 5}, "userProfileConfig": {"maxPhoneOtpAttempts": 3, "maxEmailOtpAttempts": 6, "riskServiceFallbackEnabled": true, "updateProfileCacheExpiryInMin": 5, "maxSelfieAttempts": 3, "maxOtpRequestLimit": 5, "OtpExpiryInMin": 5, "testPhoneRegex": [], "maxEmailUpdateAttempts": 3}, "bizInfoConfig": {"maxEmailCodeAttempts": 6, "updateBizInfoCacheExpiryInMin": 5, "maxCodeRequestLimit": 5, "codeExpiryInMin": 5}, "featureFlags": {"enableGIDLoginConsumer": true, "enableSkipPreviousEmailCheck": true, "enableInboxLoginNewDevice": false, "enableExpiredApplicationConsumer": true, "enableDeactivateIdentityAccount": true, "enableCustomerClosingConsumer": true, "enableAccountServiceDormantAccountEventConsumer": true, "enableApplicationStatusTransitionConsumer": true, "enableBizCraConsumer": true, "enableBusinessStatusConsumer": true, "enableTransactionLimitTxConsumer": true, "enableDepositsAccountDetailConsumer": true, "enableGDLoginCheck": true, "enableGDPinResetCheck": true, "defaultLoginVerdictType": "PIN_PLUS&FACE_LIVENESS", "defaultPinResetVerdictType": "FACE_LIVENESS", "enableTxSignInBoundedLogin": false, "enableRiskCheckInProfileCheck": false, "enablePigeonSms": true, "enableOtpNumberCache": false, "enablePreLoginDeviceBoundCheck": false, "enableKafkaRetryHandling": true, "enableActiveProfileFallback": true, "enableSmsInternalRiskCheck": true}, "gdConfig": {"verdictPriority": {"NotAllow": 99, "Selfie": 5, "PIN": 2, "Allow": 1}}, "ekycWorkflowRunResultConfig": {"isFixedPolling": false, "getFromSource": true, "exponentialPollingConfig": {"maxRetry": 8, "retryDelayInSec": 2, "maxPollDurationInSec": 60}, "fixedPollingConfig": {"loopCount": 4, "retryDurationInSec": 13}}, "coolingOffDurationInSecond": 43200, "appAuthConfig": {"cacheExpiryInSec": 110, "AESKey": "", "cacheExpiryDelayInSec": 10, "txValidityPeriodInSec": 120, "expiryMessageDelayInSec": 120, "statusCacheExpiryInSec": 240, "deleteAppAuthDuringGet": false, "enableAsMechType": {"payment": false, "insurance": false}, "typesToRetainCacheDuringGet": ["CARD", "PARTNER_CHARGE"]}, "notificationTemplate": {"foundBoundedDevicePushTemplate": "identity_push_manual_logout_for_unbounded_device_login"}, "partnerOnboardedConfig": {"cacheExpiryDurationInHour": 168}, "slackBotURL": "", "slackBotURLs": [], "snowflakeConfig": {"dsn": "{{ SNOWFLAKE_USER }}:{{ SNOWFLAKE_PASSWORD }}@$SNOWFLAKE_URL$/GOLD/PUSH_NOTIFICATIONS?warehouse=push_notifications_wh&role=PUSH_NOTIFICATIONS_SERVICE_ROLE&client_session_keep_alive=true", "engine": "snowflake"}, "tableNamespace": {"schemaName": "PUSH_NOTIFICATIONS"}, "workerConfig": {"unverifiedEmailReminderNotification": {"lockDurationInMinutes": 200, "cronExpression": "0 23 * * *", "enabled": true, "rateLimitMilliSecond": 150}}, "sqsConfig": {"queueURL": "https://sqs.ap-southeast-1.amazonaws.com/************/prd-identity-id-exp-sqs-standard", "awsRegion": "ap-southeast-1", "exponentialBackoffBaseIntervalSeconds": 10, "waitTimeSeconds": 5, "defaultVisibilityTimeoutSeconds": 10, "workerCount": 1, "maxNumberOfMessages": 1}, "releaseFeatureFlagConfig": {"getActiveProfileFromHeader": true}, "useUpdateProfileErrV2": false}