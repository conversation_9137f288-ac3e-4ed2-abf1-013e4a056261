atlantisUrl: https://atlantis-test.g-bank.app

vcsSecretName: "atlantis-test-secrets"

image:
  repository: ************.dkr.ecr.ap-southeast-1.amazonaws.com/build/atlantis
  tag: v0.35.1
  pullPolicy: Always

service:
  type: LoadBalancer
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.beta.kubernetes.io/aws-load-balancer-ssl-cert: "arn:aws:acm:ap-southeast-1:************:certificate/938db0d2-c9b9-42ed-b339-f57d6d525f6f"
    service.beta.kubernetes.io/aws-load-balancer-internal: "true"
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: "http"
    service.beta.kubernetes.io/aws-load-balancer-connection-idle-timeout: "400"
    service.beta.kubernetes.io/aws-load-balancer-cross-zone-load-balancing-enabled: "true"
    external-dns.alpha.kubernetes.io/hostname: "atlantis-test.g-bank.app"
    external-dns.alpha.kubernetes.io/alias: "true"
    service.beta.kubernetes.io/aws-load-balancer-ssl-negotiation-policy: "ELBSecurityPolicy-TLS-1-2-2017-01"
    service.beta.kubernetes.io/aws-load-balancer-additional-resource-tags: "Environment=dev,Name=atlantis-infra-dev-nlb"
    service.beta.kubernetes.io/aws-load-balancer-subnets: "subnet-0955bfbb7806c08a6,subnet-00975a82419de8ca8,subnet-018107ac85a807c52"
  port: 443
  targetPort: 4141

serviceAccount:
  create: true
  mount: true
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/infra-01-mng-atlantis
