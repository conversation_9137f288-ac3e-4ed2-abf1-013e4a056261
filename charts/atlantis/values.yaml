## -------------------------- ##
# Values to override for your instance.
## -------------------------- ##

# -- Provide a name to substitute for the full names of resources.
fullnameOverride: ""

# -- Provide a name to substitute for the name of the chart.
nameOverride: ""

# -- An option to override the atlantis url,
# if not using an ingress, set it to the external IP.
# Check values.yaml for examples.
atlantisUrl: ""
# Example:  http://10.0.0.0

# -- Replace this with your own repo allowlist.
orgAllowlist: gitlab.com/gx-regional/dbmy*

# -- Deprecated in favor of orgAllowlist.
orgWhitelist: "<deprecated>"

# -- Specify the log level for Atlantis.
# Accepts: debug, info, warn, or error.
logLevel: info

# -- If using GitHub, please enter your values as follows.
# The chart will perform the base64 encoding for values that are stored in secrets.
# The 'hostname' key is exclusive to GitHub Enterprise installations.
# Check values.yaml for examples.
github: {}
# github:
#   user: foo
#   token: bar
#   secret: baz
#   hostname: github.your.org

# -- If using a GitHub App, please enter your values as follows.
# The chart will perform the base64 encoding for you for values that are stored in secrets.
# installationId is necessary when there are multiple installs of the Github App.
# Check values.yaml for examples.
githubApp: {}
# githubApp:
#   id: 123456
#   installationId: 1
#   slug: foo
#   key: |
#     -----BEGIN PRIVATE KEY-----
#     ...
#     -----END PRIVATE KEY-----
#   secret: baz

# -- If using Gitea, please enter your values as follows.
# The 'baseUrl' key is exclusive to self-hosted Gitea installations.
# The chart will perform the base64 encoding for you for values that are stored in secrets.
# Check values.yaml for examples.
gitea: {}
# gitea:
#   user: foo
#   token: bar
#   secret: baz
#   baseUrl: gitea.your.org

# -- If using GitLab, please enter your values as follows.
# The 'hostname' key is exclusive to GitLab Enterprise installations.
# The chart will perform the base64 encoding for you for values that are stored in secrets.
# Check values.yaml for examples.
gitlab:
  user: svc.infra.gitlab
# gitlab:
#   user: foo
#   token: bar
#   secret: baz
#   hostname: gitlab.your.org

# -- If using Bitbucket, there are two approaches:
# Bitbucket Server, deployed in your own infrastructure
# and Cloud available at (https://Bitbucket.org).
# The chart will perform the base64 encoding for you for values that are stored in secrets.
# Check values.yaml for examples.
bitbucket: {}

# Bitbucket Server
# bitbucket:
#   user: foo
#   token: bar
#   secret: baz
#   baseURL: https://bitbucket.yourorganization.com

# Bitbucket Cloud
# The recommendation is to genarate a service user on your cloud environment, but you can live on the edge using your own user :).
# Create an APP PASSWORD to the user for the token value.
# Base URL are not needed here, but keep in mind to provide an IP Whitelist as the Atlantis documentation.
# bitbucket:
#   user: foo
#   token: bar

# -- If using Azure DevOps, please enter your values as follows.
# The chart will perform the base64 encoding for you for values that are stored in secrets.
# Check values.yaml for examples.
azuredevops: {}
# azuredevops:
#   user: foo
#   token: bar
#   webhookUser: foo
#   webhookPassword: baz

# -- If managing secrets outside the chart for the webhook, use this variable to reference the secret name
vcsSecretName: ""

# -- When referencing Terraform modules in private repositories, it may be helpful
# (necessary?) to use redirection in a .gitconfig.
# Check values.yaml for examples.
gitconfig: ""
# gitconfig: |
# [url "https://<EMAIL>"]
#   insteadOf = https://github.com
# [url "https://<EMAIL>"]
#   insteadOf = ssh://**************
# [url "https://oauth2:<EMAIL>"]
#   insteadOf = https://gitlab.com
# [url "https://oauth2:<EMAIL>"]
#   insteadOf = ssh://**************
# Source: https://stackoverflow.com/questions/42148841/github-clone-with-oauth-access-token

# -- When true gitconfig file is mounted as read only.
# When false, the gitconfig value will be copied to '/home/<USER>/.gitconfig' before starting the atlantis process,
# instead of being mounted as a file.
gitconfigReadOnly: true

# -- If managing secrets outside the chart for the gitconfig, use this variable to reference the secret name
gitconfigSecretName: ""

# -- When referencing Terraform modules in private repositories or registries (such as Artfactory)
# configuing a .netrc file for authentication may be required.
# Check values.yaml for examples.
netrc: ""
# netrc: |
# machine artifactory.myapp.com login YOUR_USERNAME password YOUR_PASSWORD
# machine bitbucket.myapp.com login YOUR_USERNAME password YOUR_PASSWORD

# -- If managing secrets outside the chart for the netrc file, use this variable to reference the secret name
netrcSecretName: ""

# -- To specify AWS credentials to be mapped to ~/.aws or to aws.directory.
# Check values.yaml for examples.
aws: {}
# aws:
#   credentials: |
#     [default]
#     aws_access_key_id=YOUR_ACCESS_KEY_ID
#     aws_secret_access_key=YOUR_SECRET_ACCESS_KEY
#     region=us-east-1
#   config: |
#     [profile a_role_to_assume]
#     role_arn = arn:aws:iam::*********:role/service-role/roleToAssume
#     source_profile = default
#   directory: "/home/<USER>/.aws"

# -- To reference an already existing Secret object with AWS credentials
awsSecretName: ""

# -- To keep backwards compatibility only.
# Deprecated (see googleServiceAccountSecrets).
# To be used for mounting credential files (when using google provider).
# Check values.yaml for examples.
serviceAccountSecrets: {}
# serviceAccountSecrets:
#   credentials: <json file as base64 encoded string>
#   credentials-staging: <json file as base64 encoded string>

## -------------------------- ##
# Default values for atlantis (override as needed).
## -------------------------- ##

# -- (int) [optional] Define the port you would like atlantis to run on. When 0, the value of service.targetPort is used.
containerPort: 0

image:
  repository: ghcr.io/runatlantis/atlantis
  # -- If not set appVersion field from Chart.yaml is used
  tag: ""
  pullPolicy: Always

# -- Optionally specify an array of imagePullSecrets.
# Secrets must be manually created in the namespace.
# ref: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/.
# Check values.yaml for examples.
imagePullSecrets: []
# imagePullSecrets:
#   - myRegistryKeySecretName

# -- Override atlantis main configuration by config map,
# ref: https://www.runatlantis.io/docs/using-slack-hooks.html#configuring-atlantis.
# Check values.yaml for examples.
config: ""
# config: |
#   ---
#   webhooks:
#     - event: apply
#       workspace-regex: .*
#       branch-regex: .*
#       kind: slack
#       channel: my-channel

# -- Use Server Side Repo Config,
# ref: https://www.runatlantis.io/docs/server-side-repo-config.html.
# Check values.yaml for examples.
repoConfig: |
  ---
  repos:
  - id: gitlab.com/gx-regional/dbmy/sre/infrastructure
    pre_workflow_hooks:
      - run: |
          cd terraform/L0/01-accounts
          terraform init
          terraform output -json | jq '[.short_name_account_ids.value | to_entries[] | .key]' > /tmp/$HEAD_COMMIT.yaml
          echo '---' | cat - /tmp/$HEAD_COMMIT.yaml > temp && mv temp /tmp/$HEAD_COMMIT.yaml
          echo '#@data/values' | cat - /tmp/$HEAD_COMMIT.yaml > temp && mv temp /tmp/$HEAD_COMMIT.yaml
      - run: |
          # Reset atlantis.yaml before execute next script
          git restore atlantis.yaml
          
          # Check for yq and the script.
          if command -v yq &> /dev/null && [ -f scripts/atlantis.autogen.sh ]; then
            # Add execute permission to the script
            chmod +x scripts/atlantis.autogen.sh 
            
            echo "yq and scripts/atlantis.autogen.sh found. Generating atlantis.merged.yaml."
            ./scripts/atlantis.autogen.sh
          else
            echo "Skipping generation of atlantis.merged.yaml as yq or scripts/atlantis.autogen.sh not found."
          fi

          # Check if the autogenerated file exists and use it with ytt.
          if [ -f atlantis.merged.yaml ]; then
            echo "atlantis.merged.yaml found. Merging with ytt."
            ytt -f atlantis.merged.yaml -f /tmp/$HEAD_COMMIT.yaml > atlantis.ytt.yaml
            rm atlantis.merged.yaml
          else
            echo "atlantis.merged.yaml not found. Merging atlantis.yaml with ytt."
            ytt -f atlantis.yaml -f /tmp/$HEAD_COMMIT.yaml > atlantis.ytt.yaml
          fi

          # Finally, replace the atlantis.yaml file.
          mv atlantis.ytt.yaml atlantis.yaml
    workflow: default
    allowed_overrides: [workflow,repo_locking,apply_requirements]
    allow_custom_workflows: true
    apply_requirements: [approved, mergeable, undiverged]
  - id: gitlab.com/gx-regional/dbmy/deploy/argocd-config
    workflow: default
    allowed_overrides: [workflow,repo_locking]
    allow_custom_workflows: true
    apply_requirements: [approved, mergeable, undiverged]
  - id: gitlab.com/gx-regional/dbmy/sre/atlantis-test-proj
    pre_workflow_hooks:
      - run: |
          cd terraform/L0/01-accounts
          terraform init
          terraform output -json | jq '[.short_name_account_ids.value | to_entries[] | .key]' > /tmp/$HEAD_COMMIT.yaml
          echo '---' | cat - /tmp/$HEAD_COMMIT.yaml > temp && mv temp /tmp/$HEAD_COMMIT.yaml
          echo '#@data/values' | cat - /tmp/$HEAD_COMMIT.yaml > temp && mv temp /tmp/$HEAD_COMMIT.yaml
      - run: |
          ytt -f atlantis.yaml -f /tmp/$HEAD_COMMIT.yaml > atlantis2.yaml
          mv atlantis2.yaml atlantis.yaml
  - id: gitlab.com/gx-regional/dbmy/gitlab-configs
    workflow: default
    allowed_overrides: [workflow,repo_locking]
    allow_custom_workflows: true
    apply_requirements: [approved, mergeable, undiverged]
  policies:
    owners:
      users:
        - senglik.ting
        - ryan.ooi
        - edward.tan2
        - hans.hezri
        - nabil.noh
        - yinlin.chee1
        - luqmanhadee.azman
        - LDHo
        - azran.ghafar

    policy_sets:
    - name: custom
      path: /atlantis-data/conftest_policies
      approve_count: 1
      source: local
  workflows:
      default:
        plan:
          steps:
          - init
          - run: aws s3 cp s3://gxb-atlantis-provider-enforcer-gp0j6/whitelist.sh - | bash
          - plan
        state_rm:
          steps:
          - run: echo "checking for state rm permission for user='$USER_NAME' invoking the command" 
          - run: if [[ "$USER_NAME" != "edward.tan2" && "$USER_NAME" != "ryan.ooi" && "$USER_NAME" != "senglik.ting" ]]; then exit 1; fi
          - init
          - state_rm
        import:
          steps:
          - run: echo "checking for state import permission for user='$USER_NAME' invoking the command" 
          - run: if [[ "$USER_NAME" != "edward.tan2" && "$USER_NAME" != "ryan.ooi" && "$USER_NAME" != "senglik.ting" ]]; then exit 1; fi
          - init
          - import
        policy_check:
          steps:
          - show
          - policy_check:
              extra_args: ["-p /atlantis-data/conftest_policies/"]
      dev-vault:
        plan:
          steps:
          - init
          - run: aws s3 cp s3://gxb-atlantis-provider-enforcer-gp0j6/whitelist.sh - | bash
          - env:
              name: TF_VAR_vault_token_dev
              command: 'curl -s --request POST https://vault.infra.dev.g-bank.app/v1/auth/k8s-infra-01-mng/login  --header "Content-Type: application/json" --data-raw "{\"jwt\": \"$(cat /var/run/secrets/kubernetes.io/serviceaccount/token)\", \"role\": \"atlantis\"}" | jq -r .auth.client_token'
          - plan:
              extra_args: ["-var-file", "dev.tfvars"]
        policy_check:
          steps:
          - show
          - policy_check:
              extra_args: ["-p /atlantis-data/conftest_policies/"]
    
      stg-vault:
        plan:
          steps:
          - init
          - run: aws s3 cp s3://gxb-atlantis-provider-enforcer-gp0j6/whitelist.sh - | bash
          - env:
              name: TF_VAR_vault_token_stg
              command: 'curl -s --request POST https://vault.infra.stg.g-bank.app/v1/auth/k8s-infra-01-mng/login  --header "Content-Type: application/json" --data-raw "{\"jwt\": \"$(cat /var/run/secrets/kubernetes.io/serviceaccount/token)\", \"role\": \"atlantis\"}" | jq -r .auth.client_token'
          - plan:
              extra_args: ["-var-file", "stg.tfvars"]
        policy_check:
          steps:
          - show
          - policy_check:
              extra_args: ["-p /atlantis-data/conftest_policies/"]

      prd-vault:
        plan:
          steps:
          - init
          - run: aws s3 cp s3://gxb-atlantis-provider-enforcer-gp0j6/whitelist.sh - | bash
          - env:
              name: TF_VAR_vault_token_prd
              command: 'curl -s --request POST https://vault.infra.prd.g-bank.app/v1/auth/k8s-infra-01-mng/login  --header "Content-Type: application/json" --data-raw "{\"jwt\": \"$(cat /var/run/secrets/kubernetes.io/serviceaccount/token)\", \"role\": \"atlantis\"}" | jq -r .auth.client_token'
          - plan:
              extra_args: ["-var-file", "prd.tfvars"]
        policy_check:
          steps:
          - show
          - policy_check:
              extra_args: ["-p /atlantis-data/conftest_policies/"]


      dev-vault-tm:
        plan:
          steps:
          - init
          - run: aws s3 cp s3://gxb-atlantis-provider-enforcer-gp0j6/whitelist.sh - | bash
          - env:
              name: TF_VAR_vault_token_tm_dev
              command: 'curl -s --request POST https://vault.tm.dev.g-bank.app/v1/auth/k8s-infra-01-mng/login  --header "Content-Type: application/json" --data-raw "{\"jwt\": \"$(cat /var/run/secrets/kubernetes.io/serviceaccount/token)\", \"role\": \"atlantis\"}" | jq -r .auth.client_token'
          - plan:
              extra_args: ["-var-file", "dev.tfvars"]
        policy_check:
          steps:
          - show
          - policy_check:
              extra_args: ["-p /atlantis-data/conftest_policies/"]

      stg-vault-tm:
        plan:
          steps:
          - init
          - run: aws s3 cp s3://gxb-atlantis-provider-enforcer-gp0j6/whitelist.sh - | bash
          - env:
              name: TF_VAR_vault_token_tm_stg
              command: 'curl -s --request POST https://vault.tm.stg.g-bank.app/v1/auth/k8s-infra-01-mng/login  --header "Content-Type: application/json" --data-raw "{\"jwt\": \"$(cat /var/run/secrets/kubernetes.io/serviceaccount/token)\", \"role\": \"atlantis\"}" | jq -r .auth.client_token'
          - plan:
              extra_args: ["-var-file", "stg.tfvars"]
        policy_check:
          steps:
          - show
          - policy_check:
              extra_args: ["-p /atlantis-data/conftest_policies/"]

      prd-vault-tm:
        plan:
          steps:
          - init
          - run: aws s3 cp s3://gxb-atlantis-provider-enforcer-gp0j6/whitelist.sh - | bash
          - env:
              name: TF_VAR_vault_token_tm_prd
              command: 'curl -s --request POST https://vault.tm.prd.g-bank.app/v1/auth/k8s-infra-01-mng/login  --header "Content-Type: application/json" --data-raw "{\"jwt\": \"$(cat /var/run/secrets/kubernetes.io/serviceaccount/token)\", \"role\": \"atlantis\"}" | jq -r .auth.client_token'
          - plan:
              extra_args: ["-var-file", "prd.tfvars"]
        policy_check:
          steps:
          - show
          - policy_check:
              extra_args: ["-p /atlantis-data/conftest_policies/"]
      
      vault-combined:
        plan:
          steps:
          - init
          - run: aws s3 cp s3://gxb-atlantis-provider-enforcer-gp0j6/whitelist.sh - | bash
          - env:
              name: TF_VAR_vault_token_tm_dev
              command: 'curl -s --request POST https://vault.tm.dev.g-bank.app/v1/auth/k8s-infra-01-mng/login  --header "Content-Type: application/json" --data-raw "{\"jwt\": \"$(cat /var/run/secrets/kubernetes.io/serviceaccount/token)\", \"role\": \"atlantis\"}" | jq -r .auth.client_token'
          - env:
              name: TF_VAR_vault_token_dev
              command: 'curl -s --request POST https://vault.infra.dev.g-bank.app/v1/auth/k8s-infra-01-mng/login  --header "Content-Type: application/json" --data-raw "{\"jwt\": \"$(cat /var/run/secrets/kubernetes.io/serviceaccount/token)\", \"role\": \"atlantis\"}" | jq -r .auth.client_token'
          - env:
              name: TF_VAR_vault_token_tm_stg
              command: 'curl -s --request POST https://vault.tm.stg.g-bank.app/v1/auth/k8s-infra-01-mng/login  --header "Content-Type: application/json" --data-raw "{\"jwt\": \"$(cat /var/run/secrets/kubernetes.io/serviceaccount/token)\", \"role\": \"atlantis\"}" | jq -r .auth.client_token'
          - env:
              name: TF_VAR_vault_token_stg
              command: 'curl -s --request POST https://vault.infra.stg.g-bank.app/v1/auth/k8s-infra-01-mng/login  --header "Content-Type: application/json" --data-raw "{\"jwt\": \"$(cat /var/run/secrets/kubernetes.io/serviceaccount/token)\", \"role\": \"atlantis\"}" | jq -r .auth.client_token'
          - env:
              name: TF_VAR_vault_token_tm_prd
              command: 'curl -s --request POST https://vault.tm.prd.g-bank.app/v1/auth/k8s-infra-01-mng/login  --header "Content-Type: application/json" --data-raw "{\"jwt\": \"$(cat /var/run/secrets/kubernetes.io/serviceaccount/token)\", \"role\": \"atlantis\"}" | jq -r .auth.client_token'
          - env:
              name: TF_VAR_vault_token_prd
              command: 'curl -s --request POST https://vault.infra.prd.g-bank.app/v1/auth/k8s-infra-01-mng/login  --header "Content-Type: application/json" --data-raw "{\"jwt\": \"$(cat /var/run/secrets/kubernetes.io/serviceaccount/token)\", \"role\": \"atlantis\"}" | jq -r .auth.client_token'
          - plan:
              extra_args: ["-var-file", "cen.tfvars"]
        policy_check:
          steps:
          - show
          - policy_check:
              extra_args: ["-p /atlantis-data/conftest_policies/"]


      dev-default:
        plan:
          steps:
          - init
          - run: aws s3 cp s3://gxb-atlantis-provider-enforcer-gp0j6/whitelist.sh - | bash
          - env:
              name: TF_VAR_vault_token_dev
              command: 'curl -s --request POST https://vault.infra.dev.g-bank.app/v1/auth/k8s-infra-01-mng/login  --header "Content-Type: application/json" --data-raw "{\"jwt\": \"$(cat /var/run/secrets/kubernetes.io/serviceaccount/token)\", \"role\": \"atlantis\"}" | jq -r .auth.client_token'
          - plan
        policy_check:
          steps:
          - show
          - policy_check:
              extra_args: ["-p /atlantis-data/conftest_policies/"]

      stg-default:
        plan:
          steps:
          - init
          - run: aws s3 cp s3://gxb-atlantis-provider-enforcer-gp0j6/whitelist.sh - | bash
          - env:
              name: TF_VAR_vault_token_stg
              command: 'curl -s --request POST https://vault.infra.stg.g-bank.app/v1/auth/k8s-infra-01-mng/login  --header "Content-Type: application/json" --data-raw "{\"jwt\": \"$(cat /var/run/secrets/kubernetes.io/serviceaccount/token)\", \"role\": \"atlantis\"}" | jq -r .auth.client_token'
          - plan
        policy_check:
          steps:
          - show
          - policy_check:
              extra_args: ["-p /atlantis-data/conftest_policies/"]

      prd-default:
        plan:
          steps:
          - init
          - run: aws s3 cp s3://gxb-atlantis-provider-enforcer-gp0j6/whitelist.sh - | bash
          - env:
              name: TF_VAR_vault_token_prd
              command: 'curl -s --request POST https://vault.infra.prd.g-bank.app/v1/auth/k8s-infra-01-mng/login  --header "Content-Type: application/json" --data-raw "{\"jwt\": \"$(cat /var/run/secrets/kubernetes.io/serviceaccount/token)\", \"role\": \"atlantis\"}" | jq -r .auth.client_token'
          - plan
        policy_check:
          steps:
          - show
          - policy_check:
              extra_args: ["-p /atlantis-data/conftest_policies/"]

      dev:
        plan:
          steps:
          - init
          - run: aws s3 cp s3://gxb-atlantis-provider-enforcer-gp0j6/whitelist.sh - | bash
          - plan:
              extra_args: ["-var-file", "dev.tfvars"]
        policy_check:
          steps:
          - show
          - policy_check:
              extra_args: ["-p /atlantis-data/conftest_policies/"]

      stg:
        plan:
          steps:
          - init
          - run: aws s3 cp s3://gxb-atlantis-provider-enforcer-gp0j6/whitelist.sh - | bash
          - plan:
              extra_args: ["-var-file", "stg.tfvars"]
        policy_check:
          steps:
          - show
          - policy_check:
              extra_args: ["-p /atlantis-data/conftest_policies/"]

      prd:
        plan:
          steps:
          - init
          - run: aws s3 cp s3://gxb-atlantis-provider-enforcer-gp0j6/whitelist.sh - | bash
          - plan:
              extra_args: ["-var-file", "prd.tfvars"]
        policy_check:
          steps:
          - show
          - policy_check:
              extra_args: ["-p /atlantis-data/conftest_policies/"]

      dr:
        plan:
          steps:
          - init
          - run: aws s3 cp s3://gxb-atlantis-provider-enforcer-gp0j6/whitelist.sh - | bash
          - plan:
              extra_args: ["-var-file", "dr.tfvars"]
        policy_check:
          steps:
          - show
          - policy_check:
              extra_args: ["-p /atlantis-data/conftest_policies/"]
      
      drstg:
        plan:
          steps:
          - init
          - run: aws s3 cp s3://gxb-atlantis-provider-enforcer-gp0j6/whitelist.sh - | bash
          - plan:
              extra_args: ["-var-file", "drstg.tfvars"]
        policy_check:
          steps:
          - show
          - policy_check:
              extra_args: ["-p /atlantis-data/conftest_policies/"]

      cen:
        plan:
          steps:
          - init
          - run: aws s3 cp s3://gxb-atlantis-provider-enforcer-gp0j6/whitelist.sh - | bash
          - plan:
              extra_args: ["-var-file", "cen.tfvars"]
        policy_check:
          steps:
          - show
          - policy_check:
              extra_args: ["-p /atlantis-data/conftest_policies/"]

      dev-test-opa:
        plan:
          steps:
          - init
          - run: aws s3 cp s3://gxb-atlantis-provider-enforcer-gp0j6/whitelist.sh - | bash
          - plan:
              extra_args: ["-var-file", "dev.tfvars"]
        policy_check:
          steps:
          - show
          - policy_check:
              extra_args: ["-p /atlantis-data/conftest_policies_test/"]
      
      stg-test-opa:
        plan:
          steps:
          - init
          - run: aws s3 cp s3://gxb-atlantis-provider-enforcer-gp0j6/whitelist.sh - | bash
          - plan:
              extra_args: ["-var-file", "stg.tfvars"]
        policy_check:
          steps:
          - show
          - policy_check:
              extra_args: ["-p /atlantis-data/conftest_policies_test/"]
      
      prd-test-opa:
        plan:
          steps:
          - init
          - run: aws s3 cp s3://gxb-atlantis-provider-enforcer-gp0j6/whitelist.sh - | bash
          - plan:
              extra_args: ["-var-file", "prd.tfvars"]
        policy_check:
          steps:
          - show
          - policy_check:
              extra_args: ["-p /atlantis-data/conftest_policies_test/"]
# Example with default configuration:
# repoConfig: |
#   ---
#   repos:
#   - id: /.*/
#     apply_requirements: []
#     workflow: default
#     allowed_overrides: []
#     allow_custom_workflows: false
#   workflows:
#     default:
#       plan:
#         steps: [init, plan]
#       apply:
#         steps: [apply]
#   metrics:
#     prometheus:
#       endpoint: /metrics

# -- Enables atlantis to run on a fork Pull Requests.
allowForkPRs: false

# -- Enables atlantis to run on a draft Pull Requests.
allowDraftPRs: false

# -- Enables atlantis to hide previous plan comments.
hidePrevPlanComments: true

# -- Enables atlantis to hide no-changes plan comments from the pull request.
hideUnchangedPlanComments: true

# -- Sets the default terraform distribution to use. Can be set to terraform or opentofu.
defaultTFDistribution: terraform

# -- Sets the default terraform version to be used in atlantis server.
# Check values.yaml for examples.
defaultTFVersion: v1.13.1
# Example: "0.12.0".

# -- Disables running `atlantis apply` regardless of which flags are sent with it.
disableApply: false

# -- Disables running `atlantis apply` without any flags.
disableApplyAll: false

# -- Stops atlantis locking projects and or workspaces when running terraform.
disableRepoLocking: false

# -- Use Diff Markdown Format for color coding diffs.
enableDiffMarkdownFormat: true

# -- Optionally specify an username and a password for basic authentication.
basicAuth:
  username: ""
  password: ""

# -- If managing secrets outside the chart for the Basic Auth secret, use this variable to reference the secret name.
basicAuthSecretName: ""

# -- Optionally specify an API secret to enable the API.
# Check values.yaml for examples.
api: {}
# api:
#   secret: "s3cr3t"

# -- If managing secrets outside the chart for the API secret, use this variable to reference the secret name. The key containing the secret must be called 'apisecret'.
apiSecretName: ""

# -- Override the command field of the Atlantis container.
command: []

# -- Common Labels for all resources created by this chart.
commonLabels: {}

livenessProbe:
  enabled: true
  # -- We only need to check every 60s since Atlantis is not a high-throughput service.
  periodSeconds: 60
  initialDelaySeconds: 5
  timeoutSeconds: 5
  successThreshold: 1
  failureThreshold: 5
  scheme: HTTP

readinessProbe:
  enabled: true
  periodSeconds: 60
  initialDelaySeconds: 5
  timeoutSeconds: 5
  successThreshold: 1
  failureThreshold: 5
  scheme: HTTP

service:
  type: NodePort
  annotations: {}
  port: 80
  portName: atlantis
  nodePort: null
  # -- (int) [optional] Define the port you would like atlantis to run on. Defaults to 4141.
  targetPort: 4141
  loadBalancerIP: null
  loadBalancerSourceRanges: []
  externalTrafficPolicy: null

podTemplate:
  # -- Check values.yaml for examples.
  annotations: {}
  # annotations:
  #   iam.amazonaws.com/role: role-arn # kube2iam example.
  labels: {}

statefulSet:
  annotations: {}
  labels: {}
  securityContext:
    fsGroup: 1000
    # -- It is not recommended to run atlantis as root.
    runAsUser: 100
    fsGroupChangePolicy: "OnRootMismatch"
  priorityClassName: ""
  updateStrategy: {}
  # -- Option to share process namespace with atlantis container.
  shareProcessNamespace: false

ingress:
  enabled: false
  ingressClassName:
  apiVersion: ""
  labels: {}
  # -- Check values.yaml for examples.
  annotations: {}
  # annotations:
  #   kubernetes.io/ingress.class: nginx
  #   kubernetes.io/tls-acme: "true"
  # -- Use / for nginx.
  path: /*
  # --  Used when several paths under the same host, with different backend services, are required.
  # Check values.yaml for examples.
  paths: []
  #    - path: "/path1"
  #      service: test1
  #      port:
  #    - path: "/path2"
  #      service: test2
  #      port:
  pathType: ImplementationSpecific
  host: ""
  # -- Used when several hosts are required.
  # Check values.yaml for examples.
  hosts: []
  #   - host: chart-example.local
  #     paths: ["/"]
  #     service: chart-example1
  #   - host: chart-example.local2
  #     service: chart-example1
  #     paths: ["/lala"]
  # -- Check values.yaml for examples.
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

webhook_ingress:
  # -- When true creates a secondary webhook.
  enabled: false
  ingressClassName:
  apiVersion: ""
  # -- Check values.yaml for examples.
  annotations: {}
  # annotations:
  #   kubernetes.io/ingress.class: nginx
  #   kubernetes.io/tls-acme: "true"
  # -- Use / for nginx.
  path: /*
  # --  Used when several paths under the same host, with different backend services, are required.
  # Check values.yaml for examples.
  paths: []
  #    - path: "/path1"
  #      service: test1
  #      port:
  #    - path: "/path2"
  #      service: test2
  #      port:
  pathType: ImplementationSpecific
  host: ""
  # -- Used when several hosts are required.
  # Check values.yaml for examples.
  hosts: []
  #   - host: chart-example.local
  #     paths: ["/"]
  #     service: chart-example1
  #   - host: chart-example.local2
  #     service: chart-example1
  #     paths: ["/lala"]
  # -- TLS configuration.
  # Check values.yaml for examples.
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local
  labels: {}

# -- Allows to override the /etc/ssl/certs/ca-certificates.cer with your custom one.
# You have to create a secret with the specified name.
customPem: ""

# -- Resources for Atlantis.
# Check values.yaml for examples.
resources:
  requests:
    cpu: 500m
  limits:
    cpu: 1000m
# resources:
#   requests:
#     memory: 1Gi
#     cpu: 100m
#   limits:
#     memory: 1Gi
#     cpu: 100m

# -- Path to the data directory for the volumeMount.
atlantisDataDirectory: /atlantis-data

volumeClaim:
  enabled: true
  # -- Disk space available to check out repositories.
  dataStorage: 50Gi
  # -- Storage class name (if possible, use a resizable one).
  storageClassName: gp3
  accessModes: ["ReadWriteOnce"]

# -- DEPRECATED - Disk space available to check out repositories.
# Example: 5Gi.
dataStorage: ""
# -- DEPRECATED - Storage class name for Atlantis disk.
storageClassName: ""

# -- Replica count for Atlantis pods.
replicaCount: 1

test:
  # -- Enables test container.
  enabled: false
  image: bats/bats
  imageTag: 1.9.0
  annotations: {}

nodeSelector: {}

tolerations:
  - key: service
    operator: Equal
    value: atlantis
    effect: NoSchedule

affinity:
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
        - matchExpressions:
            - key: service
              operator: In
              values:
                - atlantis

# -- You can use topology spread constraints to control how Pods are spread across your cluster among failure-domains such as regions,
# zones, nodes, and other user-defined topology domains. (requires Kubernetes >= 1.19).
# Check values.yaml for examples.
topologySpreadConstraints: []
#  - labelSelector:
#      matchLabels:
#        app.kubernetes.io/name: aws-example-cluster
#    maxSkew: 1
#    topologyKey: topology.kubernetes.io/zone
#    whenUnsatisfiable: DoNotSchedule

serviceAccount:
  # -- Specifies whether a ServiceAccount should be created.
  create: true
  # -- Set the `automountServiceAccountToken` field on the pod template spec.
  # -- If false, no kubernetes service account token will be mounted to the pod.
  mount: true
  # -- The name of the ServiceAccount to use.
  # If not set and create is true, a name is generated using the fullname template.
  name: null
  # -- Annotations for the Service Account.
  # Check values.yaml for examples.
  annotations: {}
  # annotations:
  #   annotation1: value
  #   annotation2: value
  # IRSA example:
  # annotations:
  #   eks.amazonaws.com/role-arn: role-arn

# -- Optionally deploy rbac to allow for the serviceAccount to manage terraform state via the kubernetes backend.
enableKubernetesBackend: false

# -- TLS Secret Name for Atlantis pod.
tlsSecretName: ""

# -- Additional path (`:` separated) that will be appended to the system `PATH` environment variable.
extraPath: ""

# -- Environment values to add to the Atlantis pod.
# Check values.yaml for examples.
environment:
  TF_PLUGIN_CACHE_MAY_BREAK_DEPENDENCY_LOCK_FILE: true
# environment:
#   ATLANTIS_DEFAULT_TF_VERSION: v1.2.9

# -- Optionally specify additional environment variables to be populated from Kubernetes secrets.
# Useful for passing in TF_VAR_foo or other secret environment variables from Kubernetes secrets.
# Check values.yaml for examples.
environmentSecrets: []
# environmentSecrets:
#   - name: THE_ENV_VAR
#     secretKeyRef:
#       name: the_k8s_secret_name
#       key: the_key_of_the_value_in_the_secret

# -- Optionally specify additional environment variables in raw yaml format.
# Useful to specify variables refering to k8s objects.
# Check values.yaml for examples.
environmentRaw: []
# environmentRaw:
#   - name: POD_IP
#     valueFrom:
#       fieldRef:
#         fieldPath: status.podIP

# -- Optionally specify additional Kubernetes secrets to load environment variables from.
# All key-value pairs within these secrets will be set as environment variables.
# Note that any variables set here will be ignored if also defined in the env block of the atlantis statefulset.
# For example, providing ATLANTIS_GH_USER here and defining a value for github.user will result in the github.user value being used.
# Check values.yaml for examples.
loadEnvFromSecrets: []
# loadEnvFromSecrets:
#   - secret_one
#   - secret_two

# -- Optionally specify additional Kubernetes ConfigMaps to load environment variables from.
# All key-value pairs within these ConfigMaps will be set as environment variables.
# Note that any variables set here will be ignored if also defined in the env block of the atlantis statefulset.
# For example, providing ATLANTIS_ALLOW_FORK_PRS here and defining a value for allowForkPRs will result in the allowForkPRs value being used.
# Check values.yaml for examples.
loadEnvFromConfigMaps: []
# loadEnvFromConfigMaps:
#   - config_one
#   - config_two

# -- Optionally specify google service account credentials as Kubernetes secrets. If you are using the terraform google provider you can specify the credentials as "${file("/var/secrets/some-secret-name/key.json")}".
# Check values.yaml for examples.
googleServiceAccountSecrets: []
# googleServiceAccountSecrets:
#   - name: some-secret-name
#     secretName: the_k8s_secret_name

# -- Optionally specify additional volumes for the pod.
# Check values.yaml for examples.
extraVolumes: []
# extraVolumes:
#   - name: some-volume-name
#     emptyDir: {}

# -- Optionally specify additional volume mounts for the container.
# Check values.yaml for examples.
extraVolumeMounts: []
# extraVolumeMounts:
#   - name: some-volume-name
#     mountPath: /path/in/container

# -- Optionally specify additional manifests to be created.
# Check values.yaml for examples.
extraManifests: []
# extraManifests:
#  - apiVersion: cloud.google.com/v1beta1
#    kind: BackendConfig
#    metadata:
#      name: "{{ .Release.Name }}-test"
#    spec:
#      securityPolicy:
#        name: "gcp-cloud-armor-policy-test"

# -- Optionally specify init containers manifests to be added to the Atlantis pod.
# Check values.yaml for examples.
initContainers: []
# initContainers:
# - name: example
#   image: alpine:latest
#   command: ['sh', '-c', 'echo The init container is running! && sleep 10']

initConfig:
  # -- Install providers/plugins into a path shared with the Atlantis pod.
  enabled: false
  image: alpine:latest
  imagePullPolicy: IfNotPresent
  # -- SharedDir is set as env var INIT_SHARED_DIR.
  sharedDir: /plugins
  sharedDirReadOnly: true
  workDir: /tmp
  # -- Size for the shared volume.
  sizeLimit: 300Mi
  # -- Security context for the container.
  containerSecurityContext: {}
  # -- Script to run on the init container.
  # @default -- Check values.yaml.
  script: |
    #!/bin/sh
    set -eoux pipefail

    # example for terragrunt
    TG_VERSION="v0.67.5"
    TG_SHA256_SUM="4e5ae67854a774be6419f7215733990b481662375dc0bd5f2eda05211a692cf0"
    TG_FILE="${INIT_SHARED_DIR}/terragrunt"
    wget https://github.com/gruntwork-io/terragrunt/releases/download/${TG_VERSION}/terragrunt_linux_amd64 -O "${TG_FILE}"
    echo "${TG_SHA256_SUM}  ${TG_FILE}" | sha256sum -c
    chmod 755 "${TG_FILE}"
    terragrunt -v

    # example for terragrunt-atlantis-config
    TAC_VERSION="1.18.0" # without v
    TAC_SHA256_SUM="59178dcd3e426abf4b5d8fcb1ac8dbdea548a04aa64eaf39be200484a5e6f2ca"
    TAC_FILE="${INIT_SHARED_DIR}/terragrunt-atlantis-config"
    wget "https://github.com/transcend-io/terragrunt-atlantis-config/releases/download/v${TAC_VERSION}/terragrunt-atlantis-config_${TAC_VERSION}_linux_amd64"
    echo "${TAC_SHA256_SUM}  terragrunt-atlantis-config_${TAC_VERSION}_linux_amd64" | sha256sum -c
    cp -fv "terragrunt-atlantis-config_${TAC_VERSION}_linux_amd64" "${TAC_FILE}"
    chmod 755 "${TAC_FILE}"
    terragrunt-atlantis-config version

# -- Optionally specify hostAliases for the Atlantis pod.
# Check values.yaml for examples.
hostAliases: []
# hostAliases:
#   - hostnames:
#     - aaa.com
#     - test.ccc.com
#     ip: 10.0.0.0
#   - hostnames:
#     - bbb.com
#     ip: ********

# -- Optionally specify dnsPolicy parameter to specify a DNS policy for a pod
# Check https://kubernetes.io/docs/concepts/services-networking/dns-pod-service/#pod-s-dns-policy
dnsPolicy: "ClusterFirst"

# -- Optionally specify dnsConfig for the Atlantis pod.
# Check values.yaml for examples.
dnsConfig: {}
# dnsConfig:
#  nameservers:
#    - *******
#  searches:
#    - mydomain.com

hostNetwork: false

secret:
  # -- Annotations for the Secrets.
  # Check values.yaml for examples.
  annotations: {}
  # annotations:
  #   annotation1: value
  #   annotation2: value

# -- These annotations will be added to all the resources.
# Check values.yaml for examples.
extraAnnotations: {}
# extraAnnotations:
#   team: example

# -- Optionally specify extra arguments for the Atlantis pod.
# Check values.yaml for examples.
extraArgs:
  - --allow-commands=state,version,plan,apply,unlock,approve_policies,import
  - --automerge
  - --emoji-reaction=eyes
  - --enable-policy-checks
  - --quiet-policy-checks
  - --silence-no-projects
  - --write-git-creds
# extraArgs:
#   - --disable-autoplan
#   - --disable-repo-locking

# -- Optionally specify extra containers for the Atlantis pod.
# Check values.yaml for examples.
extraContainers:
  - name: opa-policy-sync
    image: 712221657655.dkr.ecr.ap-southeast-1.amazonaws.com/mirror/docker.io/amazon/aws-cli:2.28.23
    imagePullPolicy: IfNotPresent
    resources:
      limits:
        cpu: 200m
        memory: 256Mi
      requests:
        cpu: 100m
        memory: 128Mi
    volumeMounts:
      - name: atlantis-data
        mountPath: /atlantis-data
    securityContext:
      runAsUser: 0
    command:
      [
        "sh",
        "-c",
        "while true; do aws s3 sync s3://s3-regula-rules/policy /atlantis-data/conftest_policies --delete; sleep 60; done",
      ]
  - name: opa-policy-sync-test
    image: 712221657655.dkr.ecr.ap-southeast-1.amazonaws.com/mirror/docker.io/amazon/aws-cli:2.28.23
    imagePullPolicy: IfNotPresent
    resources:
      limits:
        cpu: 200m
        memory: 256Mi
      requests:
        cpu: 100m
        memory: 128Mi
    volumeMounts:
      - name: atlantis-data
        mountPath: /atlantis-data
    securityContext:
      runAsUser: 0
    command:
      [
        "sh",
        "-c",
        "while true; do aws s3 sync s3://s3-regula-rules/policy-test /atlantis-data/conftest_policies_test --delete; sleep 60; done",
      ]
# extraContainers:
#   - name: <container name>
#     args:
#       - ...
#     image: <docker images>
#     imagePullPolicy: IfNotPresent
#     resources:
#       limits:
#         memory: 128Mi
#       requests:
#         cpu: 100m
#         memory: 128Mi
#     volumeMounts:
#       - ...

# -- Check values.yaml for examples.
containerSecurityContext: {}
# containerSecurityContext:
#   allowPrivilegeEscalation: false
#   readOnlyRootFilesystem: true

servicemonitor:
  # -- To enable a Prometheus servicemonitor, set enabled to true,
  #   and enable the metrics in this file's repoConfig
  #   by setting a value for metrics.prometheus.endpoint.
  enabled: false
  interval: "30s"
  path: /metrics
  # -- Prometheus ServiceMonitor labels.
  additionalLabels: {}
  auth:
    # -- If auth is enabled on Atlantis, use one of the following mechanism.
    basicAuth:
      # -- Authentication from the secret generated with the basicAuth values
      #   this will reference the username and password keys
      #   from the atlantis-basic-auth secret.
      enabled: false
    externalSecret:
      # -- Authentication based on an external secret
      enabled: false
      name: ""
      # -- Check values.yaml for examples.
      keys: {}
      # keys:
      #   username: USERNAME
      #   password: ATLANTIS_WEB_PASSWORD
  # -- Optional metric relabelings to drop or modify metrics.
  metricRelabelings: []
  # metricRelabelings:
  #   - action: drop
  #     regex: "atlantis_project_apply_execution_.*"
  #     sourceLabels: [__name__]

# -- Enable this if you're using Google Managed Prometheus.
podMonitor:
  enabled: false
  interval: "30s"

# -- Set the desired Locking DB type
# Accepts boltdb or redis.
lockingDbType: ""

# -- Configure Redis Locking DB.
# lockingDbType value must be redis for the config to take effect.
# Check values.yaml for examples.
redis: {}
# redis:
#   host: redis.host.name
#   password: myRedisPassword
#   port: 6379
#   db: 0
#   tlsEnabled: false
#   insecureSkipVerify: false

# -- When managing secrets outside the chart for the Redis secret, use this variable to reference the secret name.
redisSecretName: ""

# -- Key within the existing Redis secret that contains the password value.
redisSecretPasswordKey: password

# -- (int) Optionally customize the termination grace period in seconds.
# @default -- default depends on the kubernetes version.
terminationGracePeriodSeconds:
# terminationGracePeriodSeconds: 300

# -- Set lifecycle hooks.
# https://kubernetes.io/docs/tasks/configure-pod-container/attach-handler-lifecycle-event/.
lifecycle: {}
# lifecycle:
#   preStop:
#     exec:
#       command:
#         - /bin/sh
#         - -c
#         - while pgrep -x "terraform|tofu|terragrunt" > /dev/null; do sleep 1; done
