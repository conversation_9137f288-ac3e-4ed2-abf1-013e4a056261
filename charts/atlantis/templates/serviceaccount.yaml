{{- if .Values.serviceAccount.create }}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ template "atlantis.serviceAccountName" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "atlantis.labels" . | nindent 4 }}
  {{- if or .Values.serviceAccount.annotations .Values.extraAnnotations }}
  annotations:
    {{- with .Values.serviceAccount.annotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
    {{- with .Values.extraAnnotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- end }}
{{- end }}
