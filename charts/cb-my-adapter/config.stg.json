{"name": "cb-my-adapter Service", "serviceName": "cb-my-adapter", "host": "0.0.0.0", "port": 8080, "env": "stg", "ownerInfo": {"name": "digibank", "email": "<EMAIL>", "url": "digibank.myteksi.com"}, "data": {"mysql": {"master": {"dsn": "{{ MASTER_USERNAME }}:{{ MASTER_PASSWORD }}@tcp($MYSQL_HOST$:3306)/$DB_NAME$?parseTime=true&loc=UTC", "maxIdle": 25, "maxOpen": 50, "connMaxLifetime": "1800s"}, "slave": {"dsn": "{{ REPLICA_USERNAME }}:{{ REPLICA_PASSWORD }}@tcp($MYSQL_HOST_REPLICA$:3306)/cb_my_adapter?parseTime=true&loc=UTC", "maxIdle": 25, "maxOpen": 50, "connMaxLifetime": "1800s"}, "masterCircuitBreaker": {"timeoutInMs": 1000}, "slaveCircuitBreaker": {"timeoutInMs": 1000}}}, "statsd": {"host": "$DOGSTATSD_HOST$", "port": 8125}, "trace": {"host": "$DOGSTATSD_HOST$", "port": 8126}, "logger": {"workerCount": 10, "bufferSize": 10000, "logLevel": 1, "stacktraceLevel": 4, "logFormat": "json", "development": false}, "ctosConfig": {"reportConfig": {"id": "{{ REPORT_CONFIG_ID }}", "userName": "{{ REPORT_CONFIG_USERNAME }}", "clientID": "{{ REPORT_CONFIG_CLIENT_ID }}", "password": "{{ REPORT_CONFIG_PASSWORD }}", "issuer": "{{ REPORT_CONFIG_ISSUER }}", "subject": "{{ REPORT_CONFIG_SUBJECT }}", "audience": "{{ REPORT_CONFIG_AUDIENCE }}", "hostAddress": "https://uat-integration.ctos.com.my", "loginHostAddress": "https://uat-sso.ctos.com.my", "jwtExpireTimeInSec": 300, "requestBodyCredential": {"companyCode": "{{ CTOS_COMPANY_CODE }}", "accountNo": "{{ CTOS_ACCOUNT_NO }}", "userID": "{{ CTOS_USER_ID }}", "requestForReport": {"includeConsent": 1, "includeCtos": 1, "includeTrex": 1, "includeCcris": 1, "includeCcrisSumm": 0, "includeDcheq": 0, "includeCcrisSupp": 0, "includeFico": 1, "includeSfi": 0, "includeSsm": 0, "includeSsmRetention": 0, "includeAngkasa": 0, "includeIe": 1, "includeCi": 0, "includeEtrPlus": 0}}, "circuitConfig": {"ctosReport": {"timeout": 60000, "max_concurrent_requests": 10}}}, "bizReportConfig": {"userName": "{{ BIZ_REPORT_CONFIG_USERNAME }}", "clientID": "{{ BIZ_REPORT_CONFIG_CLIENT_ID }}", "password": "{{ BIZ_REPORT_CONFIG_PASSWORD }}", "requestBodyCredential": {"companyCode": "{{ BIZ_CTOS_COMPANY_CODE }}", "accountNo": "{{ BIZ_CTOS_ACCOUNT_NO }}", "userID": "{{ BIZ_CTOS_USER_ID }}", "bizRequestForReport": {"business": {"includeConsent": 1, "includeCtos": 1, "includeTrex": 1, "includeCcris": 1, "includeDcheq": 0, "includeCcrisSupp": 1, "includeFico": 1, "includeSfi": 0, "includeSsm": 1, "includeSsmRetention": 30, "includeAngkasa": 0, "includeIe": 0, "includeCi": 0, "includeEtrPlus": 1}, "owner": {"includeConsent": 1, "includeCtos": 1, "includeTrex": 1, "includeCcris": 1, "includeDcheq": 0, "includeCcrisSupp": 1, "includeFico": 1, "includeSfi": 0, "includeSsm": 0, "includeSsmRetention": 0, "includeAngkasa": 0, "includeIe": 0, "includeCi": 0, "includeEtrPlus": 1}}}}, "httpClientConfig": {"maxIdleConnsPerHost": 64, "idleConnTimeoutInSMillis": 10000, "timeoutInMillis": 60000, "requestLogLockTimeoutInMillis": 5000}, "ttlForBearerTokenInRedisInSec": 240}, "finexusConfig": {"finexusClientConfig": {"userID": "GXB00019", "userGroup": "GVT-GXB", "versionNo": "0800", "fiCode": "*********", "ProdIDs": {"flexiLoan": "CC", "bizFlexiCredit": "FL"}, "hostAddress": "https://dev2.finexusgroup.com:21778/ccrisgateway/XMLInterface", "authUserID": "{{ FINEXUS_USER_ID }}", "authPassword": "{{ FINEXUS_PASSWORD }}", "circuitConfig": {"finexusApplication": {"timeout": 240000}}}, "httpClientConfig": {"maxIdleConnsPerHost": 64, "idleConnTimeoutInSMillis": 10000, "timeoutInMillis": 240000, "requestLogLockTimeoutInMillis": 5000}}, "ctosBlackOutConfig": {"scheduleBlackOutConfigs": [{"unit": "custom", "date": "09/Sep/2025", "start": "11:50", "end": "14:00"}], "excludedDates": []}, "finexusBlackOutConfig": {"scheduleBlackOutConfigs": [{"unit": "custom", "date": "28/Mar/2025", "start": "12:35", "end": "12:50"}], "excludedDates": []}, "workflowRetry": {"default": {"interval": 30, "maxAttempts": 10}, "ctosBlackOut": {"interval": 60, "maxAttempts": 6}, "transactional": {"interval": 30, "maxAttempts": 288}}, "notifyWorkflowRetry": {"default": {"interval": 30, "maxAttempts": 10}, "finexusBlackOut": {"interval": 150, "maxAttempts": 195}, "transactional": {"interval": 100, "maxAttempts": 10}}, "s3config": {"bucketName": "stg-lending-cb-my-adapter-dbmy-s3"}, "cbMyAdapterStream": {"brokers": ["b-1.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.2hhvef.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "cb-my-adapter-stg", "clusterType": "critical", "stream": "stg-cb-my-adapter-tx", "enableTLL": true, "initOffset": "oldest", "packageName": "pb", "dtoName": "CbAdapterEvent", "syncprod": true, "requiredAcks": -1}, "redisConfig": {"addr": "clustercfg.dbmy-stg-lending-ec-cb-my-adapter.nofyfm.apse1.cache.amazonaws.com:6379", "idleTimeoutInSec": 1, "poolSize": 300, "readOnlyFromSlaves": false, "readTimeoutInSec": 1, "writeTimeoutInSec": 1, "password": "{{ REDIS_PASSWORD }}", "tlsEnabled": true}, "rateLimiters": [{"key": "ctosReport_consumer_report", "maxReq": 10, "windowSizeInSec": 1}, {"key": "finexus_consumer_notify", "maxReq": 1000, "windowSizeInSec": 1}], "featureFlags": {"enableCTOSRateLimiter": true, "enableFinexusRateLimiter": false}, "privateKey": "{{ CTOS_PRIVATE_KEY }}"}