apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: test-role
rules:
  - apiGroups:
      - ""
    resources:
      - pods
      - services
      - configmaps
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - apps
    resources:
      - deployments
      - replicasets
    verbs:
      - create
      - update
      - delete
  - apiGroups:
      - "*"
    resources:
      - "*"
    verbs:
      - get
