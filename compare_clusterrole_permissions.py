#!/usr/bin/env python3
"""
Kubernetes ClusterRole Permissions Comparison Tool

This script compares permissions between two Kubernetes ClusterRole YAML manifest files.
It flattens all permissions into individual entries and identifies differences between the files.
"""

import argparse
import csv
import sys
from pathlib import Path
from typing import Dict, List, Set, Tuple, Union
import yaml


class PermissionComparator:
    """Compares Kubernetes ClusterRole permissions between two YAML manifest files."""
    
    # Special characters for handling empty strings and wildcards
    CORE_API_GROUP = "<CORE>"
    ALL_WILDCARD = "<ALL>"
    
    # Status constants
    EXISTS_IN_BOTH = "EXISTS_IN_BOTH"
    EXISTS_IN_FIRST_ONLY = "EXISTS_IN_FIRST_ONLY"
    EXISTS_IN_SECOND_ONLY = "EXISTS_IN_SECOND_ONLY"
    
    def __init__(self):
        self.permissions_file1: Set[Tuple[str, str, str]] = set()
        self.permissions_file2: Set[Tuple[str, str, str]] = set()
        self.file1_path: str = ""
        self.file2_path: str = ""
    
    def _normalize_value(self, value: str) -> str:
        """Normalize special values in permissions."""
        if value == "":
            return self.CORE_API_GROUP
        elif value == "*":
            return self.ALL_WILDCARD
        return value
    
    def _extract_permissions_from_rules(self, rules: List[Dict]) -> Set[Tuple[str, str, str]]:
        """Extract and flatten permissions from ClusterRole rules."""
        permissions = set()
        
        for rule in rules:
            # Skip rules that don't have the standard permission structure
            if 'apiGroups' not in rule or 'resources' not in rule or 'verbs' not in rule:
                continue
                
            api_groups = rule.get('apiGroups', [])
            resources = rule.get('resources', [])
            verbs = rule.get('verbs', [])
            
            # Create cartesian product of apiGroups x resources x verbs
            for api_group in api_groups:
                for resource in resources:
                    for verb in verbs:
                        normalized_permission = (
                            self._normalize_value(api_group),
                            self._normalize_value(resource),
                            self._normalize_value(verb)
                        )
                        permissions.add(normalized_permission)
        
        return permissions
    
    def _load_yaml_file(self, file_path: str) -> Dict:
        """Load and parse YAML file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                # Handle multiple YAML documents in a single file
                documents = list(yaml.safe_load_all(file))

                # Find the ClusterRole document
                for doc in documents:
                    if doc and doc.get('kind') == 'ClusterRole':
                        return doc

                # If no ClusterRole found, try the first document
                if documents and documents[0]:
                    return documents[0]

                raise ValueError("No valid YAML document found")

        except FileNotFoundError:
            raise FileNotFoundError(f"File not found: {file_path}")
        except yaml.YAMLError as e:
            raise ValueError(f"Error parsing YAML file {file_path}: {e}")
    
    def load_permissions(self, file1_path: str, file2_path: str) -> None:
        """Load permissions from both YAML files."""
        self.file1_path = file1_path
        self.file2_path = file2_path
        
        # Load first file
        yaml_doc1 = self._load_yaml_file(file1_path)
        rules1 = yaml_doc1.get('rules', [])
        self.permissions_file1 = self._extract_permissions_from_rules(rules1)
        
        # Load second file
        yaml_doc2 = self._load_yaml_file(file2_path)
        rules2 = yaml_doc2.get('rules', [])
        self.permissions_file2 = self._extract_permissions_from_rules(rules2)
    
    def compare_permissions(self) -> List[Dict[str, Union[str, bool]]]:
        """Compare permissions and return results."""
        results = []
        
        # Get all unique permissions from both files
        all_permissions = self.permissions_file1.union(self.permissions_file2)
        
        for api_group, resource, verb in sorted(all_permissions):
            in_file1 = (api_group, resource, verb) in self.permissions_file1
            in_file2 = (api_group, resource, verb) in self.permissions_file2
            
            if in_file1 and in_file2:
                status = self.EXISTS_IN_BOTH
            elif in_file1:
                status = self.EXISTS_IN_FIRST_ONLY
            else:
                status = self.EXISTS_IN_SECOND_ONLY
            
            results.append({
                'API_Group': api_group,
                'Resource': resource,
                'Verb': verb,
                'Status': status,
                'Source_File_1': in_file1,
                'Source_File_2': in_file2
            })
        
        return results
    
    def export_to_csv(self, results: List[Dict], output_path: str) -> None:
        """Export comparison results to CSV file."""
        fieldnames = ['API_Group', 'Resource', 'Verb', 'Status', 'Source_File_1', 'Source_File_2']
        
        with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(results)
    
    def print_summary(self, results: List[Dict]) -> None:
        """Print a summary of the comparison results."""
        total = len(results)
        both = sum(1 for r in results if r['Status'] == self.EXISTS_IN_BOTH)
        first_only = sum(1 for r in results if r['Status'] == self.EXISTS_IN_FIRST_ONLY)
        second_only = sum(1 for r in results if r['Status'] == self.EXISTS_IN_SECOND_ONLY)
        
        print(f"\nComparison Summary:")
        print(f"==================")
        print(f"Total unique permissions: {total}")
        print(f"Permissions in both files: {both}")
        print(f"Permissions only in {Path(self.file1_path).name}: {first_only}")
        print(f"Permissions only in {Path(self.file2_path).name}: {second_only}")


def main():
    """Main function to handle command-line interface."""
    parser = argparse.ArgumentParser(
        description="Compare Kubernetes ClusterRole permissions between two YAML manifest files"
    )
    parser.add_argument(
        'file1',
        help='Path to the first ClusterRole YAML manifest file'
    )
    parser.add_argument(
        'file2', 
        help='Path to the second ClusterRole YAML manifest file'
    )
    parser.add_argument(
        '-o', '--output',
        help='Output CSV file path (optional)'
    )
    parser.add_argument(
        '--no-summary',
        action='store_true',
        help='Skip printing the summary to console'
    )
    
    args = parser.parse_args()
    
    try:
        # Initialize comparator and load permissions
        comparator = PermissionComparator()
        comparator.load_permissions(args.file1, args.file2)
        
        # Compare permissions
        results = comparator.compare_permissions()
        
        # Export to CSV if output path provided
        if args.output:
            comparator.export_to_csv(results, args.output)
            print(f"Results exported to: {args.output}")
        
        # Print summary unless disabled
        if not args.no_summary:
            comparator.print_summary(results)
            
        # If no CSV output, print some sample results
        if not args.output and not args.no_summary:
            print(f"\nSample results (first 10):")
            print(f"==========================")
            for i, result in enumerate(results[:10]):
                print(f"{result['API_Group']:<20} {result['Resource']:<30} {result['Verb']:<15} {result['Status']}")
    
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()
